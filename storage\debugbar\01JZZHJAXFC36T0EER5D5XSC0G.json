{"__meta": {"id": "01JZZHJAXFC36T0EER5D5XSC0G", "datetime": "2025-07-12 04:45:29", "utime": **********.136326, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331526.659335, "end": **********.136337, "duration": 2.4770021438598633, "duration_str": "2.48s", "measures": [{"label": "Booting", "start": 1752331526.659335, "relative_start": 0, "end": **********.344258, "relative_end": **********.344258, "duration": 0.****************, "duration_str": "685ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.344267, "relative_start": 0.****************, "end": **********.136338, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.79s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.364385, "relative_start": 0.***************, "end": **********.367108, "relative_end": **********.367108, "duration": 0.002723217010498047, "duration_str": "2.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.table-base", "start": **********.794541, "relative_start": 2.****************, "end": **********.794541, "relative_end": **********.794541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header", "start": **********.795459, "relative_start": 2.****************, "end": **********.795459, "relative_end": **********.795459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.export", "start": **********.797083, "relative_start": 2.1377480030059814, "end": **********.797083, "relative_end": **********.797083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.download", "start": **********.798897, "relative_start": 2.1395621299743652, "end": **********.798897, "relative_end": **********.798897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "start": **********.800472, "relative_start": 2.14113712310791, "end": **********.800472, "relative_end": **********.800472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye-off", "start": **********.801098, "relative_start": 2.141763210296631, "end": **********.801098, "relative_end": **********.801098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.801919, "relative_start": 2.1425840854644775, "end": **********.801919, "relative_end": **********.801919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.802325, "relative_start": 2.1429901123046875, "end": **********.802325, "relative_end": **********.802325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "start": **********.802815, "relative_start": 2.143480062484741, "end": **********.802815, "relative_end": **********.802815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.search", "start": **********.803351, "relative_start": 2.1440160274505615, "end": **********.803351, "relative_end": **********.803351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.search", "start": **********.803935, "relative_start": 2.1446001529693604, "end": **********.803935, "relative_end": **********.803935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "start": **********.804441, "relative_start": 2.145106077194214, "end": **********.804441, "relative_end": **********.804441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "start": **********.805172, "relative_start": 2.1458370685577393, "end": **********.805172, "relative_end": **********.805172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table", "start": **********.805678, "relative_start": 2.1463429927825928, "end": **********.805678, "relative_end": **********.805678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.806489, "relative_start": 2.1471540927886963, "end": **********.806489, "relative_end": **********.806489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.807352, "relative_start": 2.148017168045044, "end": **********.807352, "relative_end": **********.807352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.809257, "relative_start": 2.1499221324920654, "end": **********.809257, "relative_end": **********.809257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.809723, "relative_start": 2.15038800239563, "end": **********.809723, "relative_end": **********.809723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.810119, "relative_start": 2.1507840156555176, "end": **********.810119, "relative_end": **********.810119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.81063, "relative_start": 2.1512951850891113, "end": **********.81063, "relative_end": **********.81063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.811088, "relative_start": 2.1517531871795654, "end": **********.811088, "relative_end": **********.811088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.811397, "relative_start": 2.152062177658081, "end": **********.811397, "relative_end": **********.811397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.custom-loading", "start": **********.812383, "relative_start": 2.153048038482666, "end": **********.812383, "relative_end": **********.812383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.814212, "relative_start": 2.154877185821533, "end": **********.814212, "relative_end": **********.814212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.815108, "relative_start": 2.155773162841797, "end": **********.815108, "relative_end": **********.815108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.994478, "relative_start": 2.3351430892944336, "end": **********.994478, "relative_end": **********.994478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.104422, "relative_start": 2.445087194442749, "end": **********.104422, "relative_end": **********.104422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.105125, "relative_start": 2.4457900524139404, "end": **********.105125, "relative_end": **********.105125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.106041, "relative_start": 2.4467060565948486, "end": **********.106041, "relative_end": **********.106041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.10704, "relative_start": 2.447705030441284, "end": **********.10704, "relative_end": **********.10704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.107673, "relative_start": 2.448338031768799, "end": **********.107673, "relative_end": **********.107673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.108546, "relative_start": 2.4492111206054688, "end": **********.108546, "relative_end": **********.108546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.109549, "relative_start": 2.450214147567749, "end": **********.109549, "relative_end": **********.109549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.110235, "relative_start": 2.450900077819824, "end": **********.110235, "relative_end": **********.110235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.111158, "relative_start": 2.4518229961395264, "end": **********.111158, "relative_end": **********.111158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.113377, "relative_start": 2.4540421962738037, "end": **********.113377, "relative_end": **********.113377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.114242, "relative_start": 2.454907178878784, "end": **********.114242, "relative_end": **********.114242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.11497, "relative_start": 2.4556350708007812, "end": **********.11497, "relative_end": **********.11497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.11573, "relative_start": 2.456395149230957, "end": **********.11573, "relative_end": **********.11573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.116299, "relative_start": 2.4569640159606934, "end": **********.116299, "relative_end": **********.116299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.116942, "relative_start": 2.4576070308685303, "end": **********.116942, "relative_end": **********.116942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.117778, "relative_start": 2.4584431648254395, "end": **********.117778, "relative_end": **********.117778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.118382, "relative_start": 2.4590470790863037, "end": **********.118382, "relative_end": **********.118382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.119054, "relative_start": 2.459719181060791, "end": **********.119054, "relative_end": **********.119054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.119813, "relative_start": 2.4604780673980713, "end": **********.119813, "relative_end": **********.119813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.120404, "relative_start": 2.461069107055664, "end": **********.120404, "relative_end": **********.120404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.121079, "relative_start": 2.4617440700531006, "end": **********.121079, "relative_end": **********.121079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.121965, "relative_start": 2.462630033493042, "end": **********.121965, "relative_end": **********.121965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.122551, "relative_start": 2.4632160663604736, "end": **********.122551, "relative_end": **********.122551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.123225, "relative_start": 2.4638900756835938, "end": **********.123225, "relative_end": **********.123225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.123996, "relative_start": 2.464661121368408, "end": **********.123996, "relative_end": **********.123996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.editable", "start": **********.124586, "relative_start": 2.4652512073516846, "end": **********.124586, "relative_end": **********.124586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.editable", "start": **********.125255, "relative_start": 2.4659202098846436, "end": **********.125255, "relative_end": **********.125255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table-base", "start": **********.125894, "relative_start": 2.4665591716766357, "end": **********.125894, "relative_end": **********.125894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.footer", "start": **********.126651, "relative_start": 2.467316150665283, "end": **********.126651, "relative_end": **********.126651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.down", "start": **********.127456, "relative_start": 2.46812105178833, "end": **********.127456, "relative_end": **********.127456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.pagination", "start": **********.128381, "relative_start": 2.469046115875244, "end": **********.128381, "relative_end": **********.128381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.133, "relative_start": 2.4736649990081787, "end": **********.134118, "relative_end": **********.134118, "duration": 0.0011181831359863281, "duration_str": "1.12ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 37612320, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 57, "nb_templates": 57, "templates": [{"name": "1x livewire-powergrid::components.frameworks.tailwind.table-base", "param_count": null, "params": [], "start": **********.794497, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/table-base.blade.phplivewire-powergrid::components.frameworks.tailwind.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header", "param_count": null, "params": [], "start": **********.795406, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header.blade.phplivewire-powergrid::components.frameworks.tailwind.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.export", "param_count": null, "params": [], "start": **********.797046, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/export.blade.phplivewire-powergrid::components.frameworks.tailwind.header.export", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fexport.blade.php&line=1", "ajax": false, "filename": "export.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.export"}, {"name": "1x livewire-powergrid::components.icons.download", "param_count": null, "params": [], "start": **********.798859, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/download.blade.phplivewire-powergrid::components.icons.download", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fdownload.blade.php&line=1", "ajax": false, "filename": "download.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.download"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "param_count": null, "params": [], "start": **********.800436, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/toggle-columns.blade.phplivewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Ftoggle-columns.blade.php&line=1", "ajax": false, "filename": "toggle-columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.toggle-columns"}, {"name": "1x livewire-powergrid::components.icons.eye-off", "param_count": null, "params": [], "start": **********.801064, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye-off.blade.phplivewire-powergrid::components.icons.eye-off", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye-off.blade.php&line=1", "ajax": false, "filename": "eye-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.eye-off"}, {"name": "2x livewire-powergrid::components.icons.eye", "param_count": null, "params": [], "start": **********.801884, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye.blade.phplivewire-powergrid::components.icons.eye", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye.blade.php&line=1", "ajax": false, "filename": "eye.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.icons.eye"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "param_count": null, "params": [], "start": **********.802764, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsoft-deletes.blade.php&line=1", "ajax": false, "filename": "soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.soft-deletes"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.search", "param_count": null, "params": [], "start": **********.803317, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/search.blade.phplivewire-powergrid::components.frameworks.tailwind.header.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.search"}, {"name": "1x livewire-powergrid::components.icons.search", "param_count": null, "params": [], "start": **********.803902, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/search.blade.phplivewire-powergrid::components.icons.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.search"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "param_count": null, "params": [], "start": **********.804406, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/enabled-filters.blade.phplivewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fenabled-filters.blade.php&line=1", "ajax": false, "filename": "enabled-filters.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.enabled-filters"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "param_count": null, "params": [], "start": **********.805138, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/message-soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fmessage-soft-deletes.blade.php&line=1", "ajax": false, "filename": "message-soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes"}, {"name": "1x livewire-powergrid::components.table", "param_count": null, "params": [], "start": **********.805645, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table.blade.phplivewire-powergrid::components.table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table"}, {"name": "2x livewire-powergrid::components.table.tr", "param_count": null, "params": [], "start": **********.806454, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/tr.blade.phplivewire-powergrid::components.table.tr", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Ftr.blade.php&line=1", "ajax": false, "filename": "tr.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.table.tr"}, {"name": "2x livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.807317, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.cols"}, {"name": "2x __components::6da562a044c365ee08fb55b6325b90a1", "param_count": null, "params": [], "start": **********.809225, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6da562a044c365ee08fb55b6325b90a1.blade.php__components::6da562a044c365ee08fb55b6325b90a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6da562a044c365ee08fb55b6325b90a1.blade.php&line=1", "ajax": false, "filename": "6da562a044c365ee08fb55b6325b90a1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::6da562a044c365ee08fb55b6325b90a1"}, {"name": "2x livewire-powergrid::components.icons.chevron-up-down", "param_count": null, "params": [], "start": **********.809686, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-up-down.blade.phplivewire-powergrid::components.icons.chevron-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-up-down.blade.php&line=1", "ajax": false, "filename": "chevron-up-down.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.icons.chevron-up-down"}, {"name": "1x components.custom-loading", "param_count": null, "params": [], "start": **********.812301, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/custom-loading.blade.phpcomponents.custom-loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcustom-loading.blade.php&line=1", "ajax": false, "filename": "custom-loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.custom-loading"}, {"name": "10x livewire-powergrid::components.row", "param_count": null, "params": [], "start": **********.814174, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/row.blade.phplivewire-powergrid::components.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 10, "name_original": "livewire-powergrid::components.row"}, {"name": "10x livewire-powergrid::components.frameworks.tailwind.editable", "param_count": null, "params": [], "start": **********.815069, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/editable.blade.phplivewire-powergrid::components.frameworks.tailwind.editable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Feditable.blade.php&line=1", "ajax": false, "filename": "editable.blade.php", "line": "?"}, "render_count": 10, "name_original": "livewire-powergrid::components.frameworks.tailwind.editable"}, {"name": "10x livewire-powergrid::components.editable", "param_count": null, "params": [], "start": **********.994434, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/editable.blade.phplivewire-powergrid::components.editable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Feditable.blade.php&line=1", "ajax": false, "filename": "editable.blade.php", "line": "?"}, "render_count": 10, "name_original": "livewire-powergrid::components.editable"}, {"name": "1x livewire-powergrid::components.table-base", "param_count": null, "params": [], "start": **********.125858, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table-base.blade.phplivewire-powergrid::components.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.footer", "param_count": null, "params": [], "start": **********.126617, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/footer.blade.phplivewire-powergrid::components.frameworks.tailwind.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.footer"}, {"name": "1x livewire-powergrid::components.icons.down", "param_count": null, "params": [], "start": **********.127422, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/down.blade.phplivewire-powergrid::components.icons.down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fdown.blade.php&line=1", "ajax": false, "filename": "down.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.down"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.pagination", "param_count": null, "params": [], "start": **********.128335, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/pagination.blade.phplivewire-powergrid::components.frameworks.tailwind.pagination", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.pagination"}]}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00169, "accumulated_duration_str": "1.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.3697479, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 31.361}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.386527, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 31.361, "width_percent": 68.639}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.table.lanuage-line-table #5jbw6njwZOFn04CkXZ3Y": "array:4 [\n  \"data\" => array:55 [\n    \"tableName\" => \"lanuage-line-table-uarxp3-table\"\n    \"deferLoading\" => true\n    \"loadingComponent\" => \"components.custom-loading\"\n    \"primaryKey\" => \"key\"\n    \"sortField\" => \"key\"\n    \"value\" => null\n    \"languageCode\" => \"ar\"\n    \"theme\" => array:17 [\n      \"name\" => \"tailwind\"\n      \"root\" => \"livewire-powergrid::components.frameworks.tailwind\"\n      \"table\" => array:3 [\n        \"layout\" => array:5 [\n          \"base\" => \"p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8\"\n          \"div\" => \"rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n          \"table\" => \"min-w-full dark:!bg-primary-800\"\n          \"container\" => \"-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8\"\n          \"actions\" => \"flex gap-2\"\n        ]\n        \"header\" => array:4 [\n          \"thead\" => \"shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900\"\n          \"tr\" => \"\"\n          \"th\" => \"font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300\"\n          \"thAction\" => \"!font-bold\"\n        ]\n        \"body\" => array:10 [\n          \"tbody\" => \"text-pg-primary-800\"\n          \"tbodyEmpty\" => \"\"\n          \"tr\" => \"border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700\"\n          \"td\" => \"px-3 py-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdEmpty\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdSummarize\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2\"\n          \"trSummarize\" => \"\"\n          \"tdFilters\" => \"\"\n          \"trFilters\" => \"\"\n          \"tdActionsContainer\" => \"flex gap-2\"\n        ]\n      ]\n      \"footer\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n        \"footer\" => \"border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n        \"footer_with_pagination\" => \"md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900\"\n      ]\n      \"cols\" => array:1 [\n        \"div\" => \"select-none flex items-center gap-1\"\n      ]\n      \"editable\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.editable\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"layout\" => array:4 [\n        \"table\" => \"livewire-powergrid::components.frameworks.tailwind.table-base\"\n        \"header\" => \"livewire-powergrid::components.frameworks.tailwind.header\"\n        \"pagination\" => \"livewire-powergrid::components.frameworks.tailwind.pagination\"\n        \"footer\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n      ]\n      \"toggleable\" => array:1 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.toggleable\"\n      ]\n      \"checkbox\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900\"\n      ]\n      \"radio\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-radio rounded-full transition ease-in-out duration-100\"\n      ]\n      \"filterBoolean\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.boolean\"\n        \"base\" => \"min-w-[5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterDatePicker\" => array:3 [\n        \"base\" => \"\"\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.date-picker\"\n        \"input\" => \"flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n      ]\n      \"filterMultiSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.multi-select\"\n        \"base\" => \"inline-block relative w-full\"\n        \"select\" => \"mt-1\"\n      ]\n      \"filterNumber\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.number\"\n        \"input\" => \"w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6\"\n      ]\n      \"filterSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.select\"\n        \"base\" => \"\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterInputText\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.input-text\"\n        \"base\" => \"min-w-[9.5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"searchBox\" => array:3 [\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8\"\n        \"iconClose\" => \"text-pg-primary-400 dark:text-pg-primary-200\"\n        \"iconSearch\" => \"text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200\"\n      ]\n    ]\n    \"primaryKeyAlias\" => null\n    \"ignoreTablePrefix\" => true\n    \"setUp\" => array:3 [\n      \"header\" => array:8 [\n        \"name\" => \"header\"\n        \"searchInput\" => true\n        \"toggleColumns\" => true\n        \"softDeletes\" => false\n        \"showMessageSoftDeletes\" => false\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"wireLoading\" => false\n      ]\n      \"footer\" => array:8 [\n        \"name\" => \"footer\"\n        \"perPage\" => 10\n        \"perPageValues\" => array:5 [\n          0 => 10\n          1 => 25\n          2 => 50\n          3 => 100\n          4 => 0\n        ]\n        \"recordCount\" => \"full\"\n        \"pagination\" => null\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"pageName\" => \"page\"\n      ]\n      \"exportable\" => array:10 [\n        \"name\" => \"exportable\"\n        \"csvSeparator\" => \",\"\n        \"csvDelimiter\" => \"\"\"\n        \"type\" => array:1 [\n          0 => \"csv\"\n        ]\n        \"striped\" => \"d0d3d8\"\n        \"columnWidth\" => []\n        \"deleteFileAfterSend\" => true\n        \"batchExport\" => []\n        \"stripTags\" => false\n        \"fileName\" => \"export-language\"\n      ]\n    ]\n    \"showErrorBag\" => false\n    \"rowIndex\" => true\n    \"readyToLoad\" => true\n    \"columns\" => array:2 [\n      0 => array:25 [\n        \"title\" => \"English\"\n        \"field\" => \"english\"\n        \"dataField\" => \"english\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"style\"\n        \"bodyStyle\" => \"width: calc(25 * 3ch); word-wrap: break-word; white-space: normal; line-height: 1.8;\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      1 => array:25 [\n        \"title\" => \"Arabic\"\n        \"field\" => \"value\"\n        \"dataField\" => \"value\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"text-wrap\"\n        \"headerStyle\" => \"white-space: normal;\"\n        \"bodyClass\" => \"style\"\n        \"bodyStyle\" => \"max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\"\n        \"toggleable\" => []\n        \"editable\" => array:3 [\n          \"hasPermission\" => true\n          \"fallback\" => null\n          \"saveOnMouseOut\" => true\n        ]\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n    ]\n    \"headers\" => []\n    \"search\" => \"\"\n    \"currentTable\" => \"\"\n    \"total\" => 1046\n    \"totalCurrentPage\" => 0\n    \"supportModel\" => true\n    \"paginateRaw\" => false\n    \"measurePerformance\" => false\n    \"checkbox\" => false\n    \"checkboxAll\" => false\n    \"checkboxValues\" => []\n    \"checkboxAttribute\" => \"id\"\n    \"filters\" => []\n    \"filtered\" => array:1046 [\n      0 => \"12_hours\"\n      1 => \"24_hours\"\n      2 => \"24_hours_limit\"\n      3 => \"<EMAIL>\"\n      4 => \"access_denied\"\n      5 => \"access_denied_note\"\n      6 => \"access_system_settings\"\n      7 => \"access_token\"\n      8 => \"access_token_info\"\n      9 => \"account_cannot_be_deactivated\"\n      10 => \"account_connect_failed\"\n      11 => \"account_connect_successfully\"\n      12 => \"account_delete_confirmation\"\n      13 => \"account_delete_password\"\n      14 => \"account_disconnected\"\n      15 => \"account_profile\"\n      16 => \"acquire_new_lead_automatically\"\n      17 => \"action\"\n      18 => \"actions\"\n      19 => \"activate_auto_clear_chat\"\n      20 => \"activate_openai_in_chat\"\n      21 => \"active\"\n      22 => \"activity_log\"\n      23 => \"activity_log_details\"\n      24 => \"activity_log_list\"\n      25 => \"activity_logs_deleted\"\n      26 => \"add\"\n      27 => \"add_button\"\n      28 => \"add_contact_title\"\n      29 => \"add_message_bot\"\n      30 => \"add_notes_title\"\n      31 => \"add_role\"\n      32 => \"add_user\"\n      33 => \"added_new_feature_and_improvements\"\n      34 => \"additional_phone_number\"\n      35 => \"addon\"\n      36 => \"address\"\n      37 => \"admin\"\n      38 => \"admin_user_has_full_access_to_all_features\"\n      39 => \"administrator_access\"\n      40 => \"administrator_info\"\n      41 => \"administrators_have_full_access_to_all_features_and_settings\"\n      42 => \"agents\"\n      43 => \"ai_integration\"\n      44 => \"ai_prompt_delete_failed\"\n      45 => \"ai_prompt_delete_successfully\"\n      46 => \"ai_prompt_edit_failed\"\n      47 => \"ai_prompt_save_failed\"\n      48 => \"ai_prompt_saved_successfully\"\n      49 => \"ai_prompt_updated_successfully\"\n      50 => \"ai_prompts\"\n      51 => \"alert\"\n      52 => \"all_chats\"\n      53 => \"all_contacts_from_group\"\n      54 => \"all_notification_cleared\"\n      55 => \"all_notification_marked_as_read\"\n      56 => \"allowed_fromats_jpeg_png_max_5\"\n      57 => \"an_error_occured_deleting_contact\"\n      58 => \"announcement\"\n      59 => \"announcement_setting\"\n      60 => \"announcement_settings_description\"\n      61 => \"announcement_toggle_note\"\n      62 => \"api_access_is_disabled\"\n      63 => \"api_integration_and_access\"\n      64 => \"api_integration_and_access_description\"\n      65 => \"api_management\"\n      66 => \"api_setting_update_successfully\"\n      67 => \"api_settings\"\n      68 => \"api_status\"\n      69 => \"api_token\"\n      70 => \"api_token_description\"\n      71 => \"app_debug\"\n      72 => \"app_env\"\n      73 => \"app_id\"\n      74 => \"app_key\"\n      75 => \"app_secret\"\n      76 => \"app_size\"\n      77 => \"assigned\"\n      78 => \"assigned_select\"\n      79 => \"attach_img_doc_vid\"\n      80 => \"attack_injection_risk\"\n      81 => \"attempts\"\n      82 => \"auth.already_registered\"\n      83 => \"auth.forgot_password\"\n      84 => \"auto_clear_chat_history\"\n      85 => \"auto_clear_history_time\"\n      86 => \"auto_clear_note\"\n      87 => \"auto_dismiss_desktop\"\n      88 => \"automate_lead_generation\"\n      89 => \"available_merge_fields\"\n      90 => \"avatar\"\n      91 => \"back_to_campaigns\"\n      92 => \"backup_failed\"\n      93 => \"before_update_description\"\n      94 => \"body\"\n      95 => \"body_data\"\n      96 => \"bot_clone_successfully\"\n      97 => \"bot_file_option3\"\n      98 => \"bot_name\"\n      99 => \"bot_performance\"\n      100 => \"bot_protection\"\n      101 => \"bot_protection_description\"\n      102 => \"bug_fix\"\n      103 => \"bug_fixes_and_performance_improvements\"\n      104 => \"build_mail_message_error\"\n      105 => \"bulk_campaign\"\n      106 => \"bulk_delete\"\n      107 => \"business_account_id\"\n      108 => \"button1\"\n      109 => \"button1_id\"\n      110 => \"button2\"\n      111 => \"button2_id\"\n      112 => \"button3\"\n      113 => \"button3_id\"\n      114 => \"button_link\"\n      115 => \"button_name\"\n      116 => \"cache_cleared_successfully\"\n      117 => \"cache_description\"\n      118 => \"cache_dir_writable\"\n      119 => \"cache_driver\"\n      120 => \"cache_management\"\n      121 => \"campaign\"\n      122 => \"campaign_created_successfully\"\n      123 => \"campaign_delete_successfully\"\n      124 => \"campaign_details\"\n      125 => \"campaign_error\"\n      126 => \"campaign_for_csv_file\"\n      127 => \"campaign_message_failed\"\n      128 => \"campaign_message_result\"\n      129 => \"campaign_name\"\n      130 => \"campaign_name_capital\"\n      131 => \"campaign_paused_successfully\"\n      132 => \"campaign_resend_process_initiated\"\n      133 => \"campaign_resumed_successfully\"\n      134 => \"campaign_save_failed\"\n      135 => \"campaign_successfully_sent_to_these\"\n      136 => \"campaign_update_successfully\"\n      137 => \"can_send_message\"\n      138 => \"cancel\"\n      139 => \"canned_replies\"\n      140 => \"canned_reply\"\n      141 => \"canned_reply_activate\"\n      142 => \"canned_reply_deactivate\"\n      143 => \"canned_reply_delete_failed\"\n      144 => \"canned_reply_delete_successfully\"\n      145 => \"canned_reply_edit_failed\"\n      146 => \"canned_reply_save_failed\"\n      147 => \"canned_reply_save_successfully\"\n      148 => \"canned_reply_update_successfully\"\n      149 => \"capabilities\"\n      150 => \"category\"\n      151 => \"change\"\n      152 => \"change_language\"\n      153 => \"change_log\"\n      154 => \"change_password_heading\"\n      155 => \"change_tone\"\n      156 => \"changelog\"\n      157 => \"chat\"\n      158 => \"chat_cleanup_completed\"\n      159 => \"chat_cleanup_completed_successfully\"\n      160 => \"chat_cleanup_failed\"\n      161 => \"chat_delete_successfully\"\n      162 => \"chat_model\"\n      163 => \"choose_csv_file\"\n      164 => \"choose_file_type\"\n      165 => \"city\"\n      166 => \"class\"\n      167 => \"clear_all\"\n      168 => \"clear_all_logs\"\n      169 => \"clear_cache_routing\"\n      170 => \"clear_chat_history\"\n      171 => \"clear_config\"\n      172 => \"clear_framework_text\"\n      173 => \"clear_log\"\n      174 => \"clear_system_log_file\"\n      175 => \"click_to_open_leads\"\n      176 => \"click_user_to_chat\"\n      177 => \"clone\"\n      178 => \"close\"\n      179 => \"cluster\"\n      180 => \"code\"\n      181 => \"company\"\n      182 => \"config\"\n      183 => \"configure_bots\"\n      184 => \"configure_stop_bot\"\n      185 => \"configure_support_agent\"\n      186 => \"confirm\"\n      187 => \"confirm_password\"\n      188 => \"confirm_run_cleanup\"\n      189 => \"connect_account\"\n      190 => \"connect_waba\"\n      191 => \"connect_with_facebook_step1\"\n      192 => \"contact\"\n      193 => \"contact_created_successfully\"\n      194 => \"contact_delete_success\"\n      195 => \"contact_details\"\n      196 => \"contact_disabled_successfully\"\n      197 => \"contact_enable_successfully\"\n      198 => \"contact_fields\"\n      199 => \"contact_information\"\n      200 => \"contact_update_successfully\"\n      201 => \"contacts\"\n      202 => \"contacts_create\"\n      203 => \"contacts_delete\"\n      204 => \"contacts_delete_successfully\"\n      205 => \"contacts_read\"\n      206 => \"contacts_update\"\n      207 => \"content\"\n      208 => \"copied\"\n      209 => \"copy\"\n      210 => \"core\"\n      211 => \"core_version\"\n      212 => \"country\"\n      213 => \"country_select\"\n      214 => \"cover_page_image\"\n      215 => \"create_campaign\"\n      216 => \"create_message_bot\"\n      217 => \"create_new_campaign\"\n      218 => \"create_role\"\n      219 => \"create_support_ticket\"\n      220 => \"create_template_bot\"\n      221 => \"created_at\"\n      222 => \"creation_time\"\n      223 => \"critical\"\n      224 => \"cron_job\"\n      225 => \"cron_job_executed_successfully\"\n      226 => \"cron_job_required\"\n      227 => \"cron_job_setup_info\"\n      228 => \"cron_last_run\"\n      229 => \"cron_not_running\"\n      230 => \"cronjob\"\n      231 => \"cronjob_1\"\n      232 => \"cronjob_2\"\n      233 => \"cronjob_3\"\n      234 => \"cronjob_4\"\n      235 => \"cronjob_description\"\n      236 => \"cronjob_running\"\n      237 => \"csv_encoding_description\"\n      238 => \"csv_encoding_format\"\n      239 => \"csv_file_only\"\n      240 => \"csv_sample_file_download\"\n      241 => \"csv_uploaded_successfully\"\n      242 => \"cta_url\"\n      243 => \"current_password\"\n      244 => \"custom_prompt\"\n      245 => \"customer\"\n      246 => \"customize_notification_sound\"\n      247 => \"daily_api_calls\"\n      248 => \"dark\"\n      249 => \"dark_logo\"\n      250 => \"dashboard\"\n      251 => \"dashboard_overview\"\n      252 => \"data_tippy_content\"\n      253 => \"database\"\n      254 => \"database_info_retrieval_failed\"\n      255 => \"date\"\n      256 => \"date_format\"\n      257 => \"days\"\n      258 => \"days_ago\"\n      259 => \"debug\"\n      260 => \"debug_false\"\n      261 => \"debug_mode\"\n      262 => \"debug_token\"\n      263 => \"default_body_content\"\n      264 => \"default_language\"\n      265 => \"default_number_updated\"\n      266 => \"default_permissions_for_webhook_access\"\n      267 => \"default_phone_number\"\n      268 => \"default_subject\"\n      269 => \"delete\"\n      270 => \"delete_account\"\n      271 => \"delete_account_message\"\n      272 => \"delete_activity_log_title\"\n      273 => \"delete_ai_prompts_title\"\n      274 => \"delete_campaign_title\"\n      275 => \"delete_canned_title\"\n      276 => \"delete_chat_title\"\n      277 => \"delete_contact_title\"\n      278 => \"delete_language\"\n      279 => \"delete_log_file\"\n      280 => \"delete_log_file_confirmation\"\n      281 => \"delete_message\"\n      282 => \"delete_message_bot\"\n      283 => \"delete_message_bot_successfully\"\n      284 => \"delete_notes_title\"\n      285 => \"delete_role\"\n      286 => \"delete_source_title\"\n      287 => \"delete_status_title\"\n      288 => \"delete_templatebot\"\n      289 => \"delete_user\"\n      290 => \"deleted_conversations\"\n      291 => \"deleted_messages\"\n      292 => \"deleting_the\"\n      293 => \"delivered_to\"\n      294 => \"description\"\n      295 => \"dest_notify_desc\"\n      296 => \"details\"\n      297 => \"development_warning_content\"\n      298 => \"development_warning_details\"\n      299 => \"development_warning_title\"\n      300 => \"disable_production_mode_successfully\"\n      301 => \"disconnect\"\n      302 => \"disconnect_account\"\n      303 => \"disconnect_message\"\n      304 => \"disconnected_info\"\n      305 => \"dismiss\"\n      306 => \"display_phone_number\"\n      307 => \"do_you_want_custom_service\"\n      308 => \"document\"\n      309 => \"document_uploaded\"\n      310 => \"documentation\"\n      311 => \"documentations\"\n      312 => \"does_not_exist\"\n      313 => \"download_document\"\n      314 => \"download_sample\"\n      315 => \"download_successful\"\n      316 => \"download_update\"\n      317 => \"drag_and_drop_description\"\n      318 => \"dynamic_input_error\"\n      319 => \"edit\"\n      320 => \"edit_bot\"\n      321 => \"edit_campaign\"\n      322 => \"edit_contact_title\"\n      323 => \"edit_english_language_not_allowed\"\n      324 => \"edit_role\"\n      325 => \"edit_template_bot\"\n      326 => \"email\"\n      327 => \"email_address\"\n      328 => \"email_config_is_required\"\n      329 => \"email_not_sent_try_again\"\n      330 => \"email_notification\"\n      331 => \"email_notification_description\"\n      332 => \"email_password_fp\"\n      333 => \"email_recaptcha_failed\"\n      334 => \"email_sent_successfull_with_emoji\"\n      335 => \"email_sent_successfully\"\n      336 => \"email_service_not_configured\"\n      337 => \"email_settings\"\n      338 => \"email_settings_description\"\n      339 => \"email_template_editor\"\n      340 => \"email_template_list_title\"\n      341 => \"email_template_name\"\n      342 => \"email_template_subject\"\n      343 => \"email_template_title\"\n      344 => \"email_template_update_failed\"\n      345 => \"email_template_update_successfully\"\n      346 => \"email_template_updated_successfully\"\n      347 => \"email_unverified\"\n      348 => \"email_veri\"\n      349 => \"email_verification\"\n      350 => \"emergency\"\n      351 => \"emojis\"\n      352 => \"enable_api_access\"\n      353 => \"enable_debug_mode\"\n      354 => \"enable_desktop_notifications\"\n      355 => \"enable_production_mode\"\n      356 => \"enable_production_mode_successfully\"\n      357 => \"enable_real_time_notifications\"\n      358 => \"enable_recaptcha\"\n      359 => \"enable_webhook_access\"\n      360 => \"enable_webhooks_resend\"\n      361 => \"enable_whatsapp_chat_notification_sound\"\n      362 => \"enable_whatsapp_log\"\n      363 => \"enabling\"\n      364 => \"english\"\n      365 => \"english_to\"\n      366 => \"enter_email_address\"\n      367 => \"enter_first_name\"\n      368 => \"enter_last_name\"\n      369 => \"enter_password\"\n      370 => \"enter_role_name\"\n      371 => \"environment\"\n      372 => \"environment_file_changed_cache_cleared\"\n      373 => \"environment_updated\"\n      374 => \"error\"\n      375 => \"error_deleting_notes\"\n      376 => \"error_during_cleanup\"\n      377 => \"error_executing_shell_command\"\n      378 => \"error_fetching_language_key_value\"\n      379 => \"error_loading_language_file\"\n      380 => \"error_processing_ai_response\"\n      381 => \"executed\"\n      382 => \"expiry\"\n      383 => \"extension\"\n      384 => \"failed\"\n      385 => \"failed_to_clear_cache_after_env_change\"\n      386 => \"failed_to_decode_json_from\"\n      387 => \"failed_to_delete_backup\"\n      388 => \"failed_to_execute_cron_job\"\n      389 => \"failed_to_fetch_contact\"\n      390 => \"failed_to_fetch_sources\"\n      391 => \"failed_to_fetch_statuses\"\n      392 => \"failed_to_load_template\"\n      393 => \"failed_to_remove_profile_image\"\n      394 => \"failed_to_send_email\"\n      395 => \"failed_to_send_password_reset_link\"\n      396 => \"failed_to_send_test_mail\"\n      397 => \"failed_to_send_whatsapp_message\"\n      398 => \"favicon\"\n      399 => \"favicon_icon\"\n      400 => \"fb_app_id\"\n      401 => \"fb_app_secret\"\n      402 => \"features\"\n      403 => \"file_not_found\"\n      404 => \"file_selected\"\n      405 => \"file_upload\"\n      406 => \"file_upload_failed\"\n      407 => \"fill_required_pusher_credential\"\n      408 => \"filters\"\n      409 => \"firstname\"\n      410 => \"fix_spelling_and_grammar\"\n      411 => \"follow_documentation\"\n      412 => \"footer\"\n      413 => \"forgot_password_fp\"\n      414 => \"format_type\"\n      415 => \"found_messages\"\n      416 => \"framework_version\"\n      417 => \"free_total_disk_space\"\n      418 => \"from\"\n      419 => \"from_role\"\n      420 => \"from_your_gallery\"\n      421 => \"general\"\n      422 => \"general_settings\"\n      423 => \"generate_new_token\"\n      424 => \"get\"\n      425 => \"get_qr_code\"\n      426 => \"google_chrome\"\n      427 => \"groups\"\n      428 => \"header\"\n      429 => \"health_status_updated\"\n      430 => \"hello\"\n      431 => \"help\"\n      432 => \"here\"\n      433 => \"hours\"\n      434 => \"hours_ago\"\n      435 => \"hours_and\"\n      436 => \"ids\"\n      437 => \"ignore_scheduled_time_and_send_now\"\n      438 => \"image\"\n      439 => \"img_preview\"\n      440 => \"imp_info_description\"\n      441 => \"import_completed\"\n      442 => \"import_contact\"\n      443 => \"import_contact_camel\"\n      444 => \"import_contact_from_csv_file\"\n      445 => \"import_error\"\n      446 => \"import_failed\"\n      447 => \"important_information\"\n      448 => \"improvement\"\n      449 => \"in_progress\"\n      450 => \"in_this_campaign\"\n      451 => \"inactive\"\n      452 => \"increase_webhook_note\"\n      453 => \"info\"\n      454 => \"info_log\"\n      455 => \"install\"\n      456 => \"integrate_ai_tools\"\n      457 => \"interactions\"\n      458 => \"invalid_api_token\"\n      459 => \"invalid_csv_file\"\n      460 => \"invalid_email_address\"\n      461 => \"invalid_file_format\"\n      462 => \"invalid_json_format\"\n      463 => \"invalid_phone_number_format\"\n      464 => \"invalid_provider_format\"\n      465 => \"invalid_version_format\"\n      466 => \"is_Enable\"\n      467 => \"is_not_approved\"\n      468 => \"issued\"\n      469 => \"just_now\"\n      470 => \"language_added_successfully\"\n      471 => \"language_code\"\n      472 => \"language_delete_failed\"\n      473 => \"language_delete_successfully\"\n      474 => \"language_file_for\"\n      475 => \"language_handling_error\"\n      476 => \"language_is_not_allowed\"\n      477 => \"language_name\"\n      478 => \"language_update_successfully\"\n      479 => \"languages\"\n      480 => \"last\"\n      481 => \"last_activity\"\n      482 => \"last_checked_at\"\n      483 => \"last_week\"\n      484 => \"lastname\"\n      485 => \"latest_version\"\n      486 => \"lead\"\n      487 => \"lead_assigned\"\n      488 => \"lead_source\"\n      489 => \"lead_status\"\n      490 => \"leads\"\n      491 => \"lear_created_event_occurred\"\n      492 => \"leave_blank_for_default_cluster\"\n      493 => \"leave_blank_to_keep_current_password\"\n      494 => \"level\"\n      495 => \"light\"\n      496 => \"link\"\n      497 => \"link_description\"\n      498 => \"link_text\"\n      499 => \"load_template\"\n      500 => \"local\"\n      501 => \"localization\"\n      502 => \"log_deleted\"\n      503 => \"log_not_found\"\n      504 => \"log_viewer\"\n      505 => \"login\"\n      506 => \"login_lb\"\n      507 => \"logo_favicon\"\n      508 => \"logout\"\n      509 => \"logout_ve\"\n      510 => \"mail_sending_failed\"\n      511 => \"mail_successfully_sent\"\n      512 => \"manage_phone_numbers\"\n      513 => \"manage_web_hooks\"\n      514 => \"mark_as_default\"\n      515 => \"mark_email_as_verified_or_send_verification\"\n      516 => \"marketing\"\n      517 => \"max_allow_char_256\"\n      518 => \"max_allowed_char_20\"\n      519 => \"max_allowed_character_60\"\n      520 => \"merge_field_name_title\"\n      521 => \"merge_not_available\"\n      522 => \"merge_tag_title\"\n      523 => \"message\"\n      524 => \"message_bot\"\n      525 => \"message_bot_is_activated\"\n      526 => \"message_bot_is_deactivated\"\n      527 => \"message_bot_not_found\"\n      528 => \"message_bot_options\"\n      529 => \"message_bot_save_failed\"\n      530 => \"message_bot_saved_successfully\"\n      531 => \"message_bots\"\n      532 => \"message_deleted_successfully\"\n      533 => \"message_statistics\"\n      534 => \"message_template_required\"\n      535 => \"messages\"\n      536 => \"messages_delivered\"\n      537 => \"messages_sent_today\"\n      538 => \"messaging_limit\"\n      539 => \"meta_description\"\n      540 => \"meta_title\"\n      541 => \"min_ago\"\n      542 => \"minutes_remaining\"\n      543 => \"missing_required_columns\"\n      544 => \"module\"\n      545 => \"module_json_file_not_found\"\n      546 => \"module_manager\"\n      547 => \"module_must_have_service_provider\"\n      548 => \"modules\"\n      549 => \"month\"\n      550 => \"more\"\n      551 => \"msg_for_admins\"\n      552 => \"msg_for_assignees\"\n      553 => \"must_implement\"\n      554 => \"name\"\n      555 => \"navigate_to_whatsmark_system\"\n      556 => \"never\"\n      557 => \"new_campaign\"\n      558 => \"new_contact_button\"\n      559 => \"new_feature\"\n      560 => \"new_password\"\n      561 => \"new_password_changed\"\n      562 => \"new_role\"\n      563 => \"new_source\"\n      564 => \"new_status\"\n      565 => \"new_user\"\n      566 => \"next\"\n      567 => \"no_activity_log_found\"\n      568 => \"no_contact_selected\"\n      569 => \"no_country_found\"\n      570 => \"no_found_in_whatsapp_business_account\"\n      571 => \"no_log_entries\"\n      572 => \"no_log_entries_found_the_file_may_be_empty\"\n      573 => \"no_log_file_selected\"\n      574 => \"no_log_files\"\n      575 => \"no_merge_fields_available\"\n      576 => \"no_notes_available\"\n      577 => \"no_permission_to_perform_action\"\n      578 => \"no_result_found\"\n      579 => \"not_allowed_to_view\"\n      580 => \"not_assigned\"\n      581 => \"not_found\"\n      582 => \"note\"\n      583 => \"note_added_successfully\"\n      584 => \"note_delete_successfully\"\n      585 => \"note_will_be_available_in_contact\"\n      586 => \"notes_title\"\n      587 => \"nothing_selected\"\n      588 => \"notice\"\n      589 => \"notification\"\n      590 => \"notification_delete_successfully\"\n      591 => \"notification_marked_as_read\"\n      592 => \"notification_sound\"\n      593 => \"number_id\"\n      594 => \"number_id_of_the_whatsapp\"\n      595 => \"obtain_credential\"\n      596 => \"of\"\n      597 => \"of_total_leads\"\n      598 => \"off\"\n      599 => \"on\"\n      600 => \"open_sidebar\"\n      601 => \"open_user_menu\"\n      602 => \"openai_secret_key\"\n      603 => \"option2_button_name\"\n      604 => \"or\"\n      605 => \"original_file_not_found\"\n      606 => \"other_details\"\n      607 => \"other_fields\"\n      608 => \"other_group\"\n      609 => \"other_information\"\n      610 => \"out_of_the\"\n      611 => \"overall_health\"\n      612 => \"overall_health_send_message\"\n      613 => \"packages_installed\"\n      614 => \"password\"\n      615 => \"password_changed_error\"\n      616 => \"password_changed_success\"\n      617 => \"password_mismatch\"\n      618 => \"password_updated\"\n      619 => \"pause_campaign\"\n      620 => \"per_page\"\n      621 => \"performance_optimization\"\n      622 => \"performance_security_tip\"\n      623 => \"permission\"\n      624 => \"permission_scopes\"\n      625 => \"permissions\"\n      626 => \"personal_information\"\n      627 => \"phone\"\n      628 => \"phone_req_description\"\n      629 => \"phone_requirement_column\"\n      630 => \"phone_sample\"\n      631 => \"phone_validation\"\n      632 => \"php_max_execution_time\"\n      633 => \"please_add_valid_number_in_csv_file\"\n      634 => \"please_check_cron_setup\"\n      635 => \"please_copy_your_new_api_token_now\"\n      636 => \"please_select_an_option\"\n      637 => \"please_select_csv_file\"\n      638 => \"please_specify_days_to_keep\"\n      639 => \"please_upload_valid_csv_file\"\n      640 => \"post\"\n      641 => \"potential_json_injection\"\n      642 => \"potential_sql_injection\"\n      643 => \"prepare_to_send_campaign_message\"\n      644 => \"preview\"\n      645 => \"previous\"\n      646 => \"processing\"\n      647 => \"processing_cache_clearing\"\n      648 => \"production\"\n      649 => \"profile\"\n      650 => \"profile_image\"\n      651 => \"profile_image_remove_success\"\n      652 => \"profile_image_removed_successfully\"\n      653 => \"profile_image_upload_error\"\n      654 => \"profile_information\"\n      655 => \"profile_settings\"\n      656 => \"profile_update\"\n      657 => \"profile_update_failed\"\n      658 => \"profile_update_successfully\"\n      659 => \"prompt_action\"\n      660 => \"provided_credential_not_match\"\n      661 => \"public\"\n      662 => \"purchase_key\"\n      663 => \"pusher\"\n      664 => \"pusher_account_setup\"\n      665 => \"pusher_account_setup_description\"\n      666 => \"pusher_batch_trigger_failed\"\n      667 => \"pusher_connection_test_failed\"\n      668 => \"pusher_connection_test_successful\"\n      669 => \"pusher_initialization_failed\"\n      670 => \"pusher_inti_failed\"\n      671 => \"pusher_is_not_initialized\"\n      672 => \"pusher_link\"\n      673 => \"pusher_not_initialized\"\n      674 => \"pusher_test_connection_error\"\n      675 => \"pusher_test_connection_failed\"\n      676 => \"pusher_trigger_failed\"\n      677 => \"qr_code\"\n      678 => \"qr_code_to_invite_people\"\n      679 => \"qr_code_to_start_chat\"\n      680 => \"quality\"\n      681 => \"queue_connection\"\n      682 => \"queue_size\"\n      683 => \"rate\"\n      684 => \"raw_content\"\n      685 => \"re_captcha\"\n      686 => \"read_the_whatsmark_documentation\"\n      687 => \"ready_by\"\n      688 => \"real_time_event_broadcasting\"\n      689 => \"real_time_event_broadcasting_description\"\n      690 => \"real_time_notification_require_pusher_integration\"\n      691 => \"recaptcha_site_key\"\n      692 => \"recaptcha_site_secret\"\n      693 => \"recaptcha_verification_failed\"\n      694 => \"recent_activities\"\n      695 => \"recipient_phone_number_required\"\n      696 => \"recommended\"\n      697 => \"record_audio\"\n      698 => \"record_successfully_inserted\"\n      699 => \"records_are_valid\"\n      700 => \"records_in_your_csv_file\"\n      701 => \"records_processed\"\n      702 => \"records_with_error\"\n      703 => \"redis_connection_failed\"\n      704 => \"refresh\"\n      705 => \"refresh_health_status\"\n      706 => \"register\"\n      707 => \"relation_type\"\n      708 => \"remember_me\"\n      709 => \"remove\"\n      710 => \"remove_chat\"\n      711 => \"remove_img\"\n      712 => \"remove_successfully\"\n      713 => \"renew_support\"\n      714 => \"reply\"\n      715 => \"reply_button\"\n      716 => \"reply_button_option1\"\n      717 => \"reply_text\"\n      718 => \"reply_type\"\n      719 => \"reply_within\"\n      720 => \"replying_to\"\n      721 => \"required_fields\"\n      722 => \"resend_campaign\"\n      723 => \"resend_email_verification\"\n      724 => \"reset\"\n      725 => \"reset_password_rp\"\n      726 => \"response\"\n      727 => \"response_code\"\n      728 => \"restart_bots_after\"\n      729 => \"restrict_chat_access\"\n      730 => \"resume_campaign\"\n      731 => \"role\"\n      732 => \"role_delete_successfully\"\n      733 => \"role_in_use_notify\"\n      734 => \"role_name\"\n      735 => \"role_permission_warning\"\n      736 => \"role_save_failed\"\n      737 => \"role_save_successfully\"\n      738 => \"roles_and_permissions\"\n      739 => \"run_cleanup_now\"\n      740 => \"run_cron_manually\"\n      741 => \"run_tool\"\n      742 => \"running\"\n      743 => \"runs\"\n      744 => \"sample_data\"\n      745 => \"sample_file_not_found\"\n      746 => \"save\"\n      747 => \"save_changes_button\"\n      748 => \"save_template\"\n      749 => \"saved\"\n      750 => \"saving\"\n      751 => \"schedule_send_time\"\n      752 => \"scheduled_at_capital\"\n      753 => \"scheduled_tasks_management\"\n      754 => \"search\"\n      755 => \"search_engine_optimization\"\n      756 => \"searching\"\n      757 => \"select_all_leads\"\n      758 => \"select_all_relation_type\"\n      759 => \"select_assign\"\n      760 => \"select_default_language\"\n      761 => \"select_document\"\n      762 => \"select_group\"\n      763 => \"select_image\"\n      764 => \"select_language\"\n      765 => \"select_model\"\n      766 => \"select_or_browse_to\"\n      767 => \"select_role\"\n      768 => \"select_smtp_encryption\"\n      769 => \"select_smtp_protocol\"\n      770 => \"select_source\"\n      771 => \"select_status\"\n      772 => \"select_type\"\n      773 => \"select_video\"\n      774 => \"send_campaign\"\n      775 => \"send_message\"\n      776 => \"send_test_email\"\n      777 => \"send_test_email_description\"\n      778 => \"send_verification_mail\"\n      779 => \"send_welcome_mail\"\n      780 => \"sender_email\"\n      781 => \"sender_name\"\n      782 => \"sending\"\n      783 => \"sending_campaign\"\n      784 => \"sending_to\"\n      785 => \"sends_welcome_email_to_new_user\"\n      786 => \"sent_status\"\n      787 => \"seo\"\n      788 => \"seo_description\"\n      789 => \"server_os\"\n      790 => \"server_software\"\n      791 => \"session_driver\"\n      792 => \"set_background_color\"\n      793 => \"set_link_message_color\"\n      794 => \"set_link_text_color\"\n      795 => \"set_mail_callback_error\"\n      796 => \"set_verification_url_callback_error\"\n      797 => \"setting_save_successfully\"\n      798 => \"setting_up_the_cronjob\"\n      799 => \"settings\"\n      800 => \"settings_webhook\"\n      801 => \"setup\"\n      802 => \"setup_auto_clear_chat\"\n      803 => \"share\"\n      804 => \"showing_page\"\n      805 => \"simplify_language\"\n      806 => \"site_description\"\n      807 => \"site_logo\"\n      808 => \"site_name\"\n      809 => \"size\"\n      810 => \"smtp\"\n      811 => \"smtp_encryption\"\n      812 => \"smtp_host\"\n      813 => \"smtp_password\"\n      814 => \"smtp_port\"\n      815 => \"smtp_protocol\"\n      816 => \"smtp_username\"\n      817 => \"software_update_management\"\n      818 => \"software_update_management_description\"\n      819 => \"something_went_wrong\"\n      820 => \"source\"\n      821 => \"source_create\"\n      822 => \"source_delete\"\n      823 => \"source_delete_failed\"\n      824 => \"source_delete_successfully\"\n      825 => \"source_in_use_notify\"\n      826 => \"source_read\"\n      827 => \"source_save_failed\"\n      828 => \"source_saved_successfully\"\n      829 => \"source_update\"\n      830 => \"source_update_successfully\"\n      831 => \"sources\"\n      832 => \"sql_injection_error\"\n      833 => \"ssl\"\n      834 => \"ssl_required_for_desktop_notifications\"\n      835 => \"state\"\n      836 => \"status\"\n      837 => \"status_as_at\"\n      838 => \"status_capital\"\n      839 => \"status_color\"\n      840 => \"status_color_placeholder\"\n      841 => \"status_create\"\n      842 => \"status_delete\"\n      843 => \"status_delete_failed\"\n      844 => \"status_delete_in_use_notify\"\n      845 => \"status_delete_successfully\"\n      846 => \"status_read\"\n      847 => \"status_save_failed\"\n      848 => \"status_save_successfully\"\n      849 => \"status_update\"\n      850 => \"status_update_successfully\"\n      851 => \"statuses\"\n      852 => \"stop_bot\"\n      853 => \"stop_bots_keyword\"\n      854 => \"storage_dir_writable\"\n      855 => \"subject\"\n      856 => \"submit\"\n      857 => \"success\"\n      858 => \"success_percentage\"\n      859 => \"support\"\n      860 => \"support_agent\"\n      861 => \"support_agent_assigned_successfully\"\n      862 => \"support_agent_feature_info\"\n      863 => \"support_has_already_expired\"\n      864 => \"system\"\n      865 => \"system_core_settings\"\n      866 => \"system_core_settings_description\"\n      867 => \"system_default\"\n      868 => \"system_environment\"\n      869 => \"system_information\"\n      870 => \"system_logs\"\n      871 => \"system_setting\"\n      872 => \"system_status\"\n      873 => \"system_update\"\n      874 => \"tap_on_a_feature_to_view_permissions\"\n      875 => \"template\"\n      876 => \"template_activate_successfully\"\n      877 => \"template_bot\"\n      878 => \"template_bot_activate\"\n      879 => \"template_bot_deactivate\"\n      880 => \"template_bot_delete_successfully\"\n      881 => \"template_bot_not_found\"\n      882 => \"template_bot_saved_successfully\"\n      883 => \"template_capital\"\n      884 => \"template_deactivate_successfully\"\n      885 => \"template_id\"\n      886 => \"template_management\"\n      887 => \"template_name\"\n      888 => \"template_not_found\"\n      889 => \"template_type\"\n      890 => \"templates\"\n      891 => \"test\"\n      892 => \"test_connection\"\n      893 => \"test_message\"\n      894 => \"test_pusher\"\n      895 => \"thank_you_for_signing_up\"\n      896 => \"the_last_template_message_still_be_sent\"\n      897 => \"the_translation_cannot_be_a_JSON_object_or_array\"\n      898 => \"there\"\n      899 => \"these_are_the_default_permissions_for_api_access\"\n      900 => \"this_campaign_send_to\"\n      901 => \"this_field_is_required\"\n      902 => \"this_may_take_a_few_moments\"\n      903 => \"this_trigger_already_exists\"\n      904 => \"this_week\"\n      905 => \"time_format\"\n      906 => \"timezone\"\n      907 => \"tip\"\n      908 => \"title\"\n      909 => \"tls\"\n      910 => \"to_find_specific_permissions\"\n      911 => \"today\"\n      912 => \"toggle_admin\"\n      913 => \"toggle_switch\"\n      914 => \"token_abilities\"\n      915 => \"token_not_have_required_abilities\"\n      916 => \"total\"\n      917 => \"total_contacts\"\n      918 => \"total_delivered\"\n      919 => \"total_fail\"\n      920 => \"total_failed\"\n      921 => \"total_parameter\"\n      922 => \"total_read\"\n      923 => \"total_read_messages\"\n      924 => \"total_send_campaign_list\"\n      925 => \"translate\"\n      926 => \"translate_language\"\n      927 => \"translation_management\"\n      928 => \"translation_updated_successfully\"\n      929 => \"trigger_keyword\"\n      930 => \"type\"\n      931 => \"type_and_press_enter\"\n      932 => \"type_customer\"\n      933 => \"type_lead\"\n      934 => \"unable_to_read_composer_lock\"\n      935 => \"unable_to_retrieve_database_information\"\n      936 => \"update_button\"\n      937 => \"update_password\"\n      938 => \"update_password_message\"\n      939 => \"update_profile_information\"\n      940 => \"update_staff_permissions\"\n      941 => \"upload\"\n      942 => \"upload_module\"\n      943 => \"upload_now\"\n      944 => \"uploading\"\n      945 => \"url_for_qr_image\"\n      946 => \"use_browser_search\"\n      947 => \"user\"\n      948 => \"user_access_token_info\"\n      949 => \"user_activated_successfully\"\n      950 => \"user_create_success\"\n      951 => \"user_deactivated_message_in_login\"\n      952 => \"user_deactivated_successfully\"\n      953 => \"user_delete_confirm\"\n      954 => \"user_delete_success\"\n      955 => \"user_delete_successfully\"\n      956 => \"user_fields\"\n      957 => \"user_in_use_notify\"\n      958 => \"user_info\"\n      959 => \"user_information\"\n      960 => \"user_not_found\"\n      961 => \"user_not_verified\"\n      962 => \"user_save_failed\"\n      963 => \"user_save_successfully\"\n      964 => \"user_update_success\"\n      965 => \"user_update_successfully\"\n      966 => \"username\"\n      967 => \"users\"\n      968 => \"users_using_this_role\"\n      969 => \"v3_setup_description\"\n      970 => \"validation_confirmed\"\n      971 => \"validation_email\"\n      972 => \"validation_failed_due_to_unexpected_error\"\n      973 => \"validation_min\"\n      974 => \"validation_required\"\n      975 => \"validation_unique\"\n      976 => \"variable\"\n      977 => \"variable_description\"\n      978 => \"variable_not_available_for_this_template\"\n      979 => \"variables\"\n      980 => \"varify_email\"\n      981 => \"verification_error\"\n      982 => \"verification_link_sent\"\n      983 => \"verification_link_sent_to_email\"\n      984 => \"verification_url_generation_error\"\n      985 => \"verified_name\"\n      986 => \"verify_email\"\n      987 => \"verify_email_notification_error\"\n      988 => \"verify_webhook\"\n      989 => \"version\"\n      990 => \"version_information\"\n      991 => \"video\"\n      992 => \"view\"\n      993 => \"view_all\"\n      994 => \"view_all_reports\"\n      995 => \"view_text\"\n      996 => \"waba\"\n      997 => \"warning\"\n      998 => \"web_hooks\"\n      999 => \"webhook\"\n      1000 => \"webhook_abilities\"\n      1001 => \"webhook_connect_failed\"\n      1002 => \"webhook_connect_successfully\"\n      1003 => \"webhook_disconnect_failed\"\n      1004 => \"webhook_discoonected_successfully\"\n      1005 => \"webhook_integrations\"\n      1006 => \"webhook_integrations_description\"\n      1007 => \"webhook_resend_method\"\n      1008 => \"webhook_url\"\n      1009 => \"website\"\n      1010 => \"welcome_back\"\n      1011 => \"welcome_message\"\n      1012 => \"what_is_this\"\n      1013 => \"whatsapp_access_token\"\n      1014 => \"whatsapp_auto_lead\"\n      1015 => \"whatsapp_block_message_24_hours_after\"\n      1016 => \"whatsapp_business_account\"\n      1017 => \"whatsapp_business_id\"\n      1018 => \"whatsapp_business_update\"\n      1019 => \"whatsapp_connection\"\n      1020 => \"whatsapp_log_updated\"\n      1021 => \"whatsapp_message_failed\"\n      1022 => \"whatsapp_message_failed_permanently\"\n      1023 => \"whatsapp_message_job_failed\"\n      1024 => \"whatsapp_message_sent_successfully\"\n      1025 => \"whatsapp_now\"\n      1026 => \"whatsapp_received_data_resend_to\"\n      1027 => \"whatsapp_template\"\n      1028 => \"whatsapp_url\"\n      1029 => \"whatsmark\"\n      1030 => \"whatsmark_settings\"\n      1031 => \"where_to_find_secret_key\"\n      1032 => \"wp_access_token\"\n      1033 => \"wp_business_id\"\n      1034 => \"wp_integration_step2\"\n      1035 => \"wp_message\"\n      1036 => \"wp_number\"\n      1037 => \"yesterday\"\n      1038 => \"you_cant_resend_this_campaign\"\n      1039 => \"your_account_is_discconected\"\n      1040 => \"your_broser_not_support_video_tag\"\n      1041 => \"your_campaign_is_already_executed\"\n      1042 => \"your_company_name\"\n      1043 => \"your_real_time_notification\"\n      1044 => \"your_version\"\n      1045 => \"zip_code\"\n    ]\n    \"enabledFilters\" => []\n    \"select\" => []\n    \"showFilters\" => false\n    \"withoutResourcesActions\" => false\n    \"additionalCacheKey\" => \"\"\n    \"persist\" => []\n    \"persistPrefix\" => \"\"\n    \"radio\" => false\n    \"radioAttribute\" => \"id\"\n    \"selectedRow\" => \"\"\n    \"softDeletes\" => \"\"\n    \"sortDirection\" => \"asc\"\n    \"multiSort\" => false\n    \"sortArray\" => []\n    \"headerTotalColumn\" => false\n    \"footerTotalColumn\" => false\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n    \"batchExporting\" => false\n    \"batchFinished\" => false\n    \"batchId\" => \"\"\n    \"batchName\" => \"PowerGrid batch export\"\n    \"showExporting\" => true\n    \"batchProgress\" => 0\n    \"exportedFiles\" => []\n    \"exportableJobClass\" => \"PowerComponents\\LivewirePowerGrid\\Jobs\\ExportJob\"\n    \"batchErrors\" => false\n  ]\n  \"name\" => \"admin.table.lanuage-line-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Table\\LanuageLineTable\"\n  \"id\" => \"5jbw6njwZOFn04CkXZ3Y\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Table\\LanuageLineTable@fetchDatasource<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/power-components/livewire-powergrid/src/PowerGridComponent.php:79-82</a>", "middleware": "web", "duration": "2.48s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1101341273 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1101341273\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-540677127 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"11992 characters\">{&quot;data&quot;:{&quot;tableName&quot;:&quot;lanuage-line-table-uarxp3-table&quot;,&quot;deferLoading&quot;:true,&quot;loadingComponent&quot;:&quot;components.custom-loading&quot;,&quot;primaryKey&quot;:&quot;key&quot;,&quot;sortField&quot;:&quot;key&quot;,&quot;value&quot;:null,&quot;languageCode&quot;:&quot;ar&quot;,&quot;theme&quot;:[{&quot;name&quot;:&quot;tailwind&quot;,&quot;root&quot;:&quot;livewire-powergrid::components.frameworks.tailwind&quot;,&quot;table&quot;:[{&quot;layout&quot;:[{&quot;base&quot;:&quot;p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8&quot;,&quot;div&quot;:&quot;rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;table&quot;:&quot;min-w-full dark:!bg-primary-800&quot;,&quot;container&quot;:&quot;-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8&quot;,&quot;actions&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;header&quot;:[{&quot;thead&quot;:&quot;shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900&quot;,&quot;tr&quot;:&quot;&quot;,&quot;th&quot;:&quot;font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300&quot;,&quot;thAction&quot;:&quot;!font-bold&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;body&quot;:[{&quot;tbody&quot;:&quot;text-pg-primary-800&quot;,&quot;tbodyEmpty&quot;:&quot;&quot;,&quot;tr&quot;:&quot;border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700&quot;,&quot;td&quot;:&quot;px-3 py-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdEmpty&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdSummarize&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2&quot;,&quot;trSummarize&quot;:&quot;&quot;,&quot;tdFilters&quot;:&quot;&quot;,&quot;trFilters&quot;:&quot;&quot;,&quot;tdActionsContainer&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;footer&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;,&quot;footer&quot;:&quot;border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;footer_with_pagination&quot;:&quot;md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;cols&quot;:[{&quot;div&quot;:&quot;select-none flex items-center gap-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.editable&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;layout&quot;:[{&quot;table&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.table-base&quot;,&quot;header&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.header&quot;,&quot;pagination&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.pagination&quot;,&quot;footer&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;toggleable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.toggleable&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;checkbox&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;radio&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-radio rounded-full transition ease-in-out duration-100&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterBoolean&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.boolean&quot;,&quot;base&quot;:&quot;min-w-[5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterDatePicker&quot;:[{&quot;base&quot;:&quot;&quot;,&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.date-picker&quot;,&quot;input&quot;:&quot;flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterMultiSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.multi-select&quot;,&quot;base&quot;:&quot;inline-block relative w-full&quot;,&quot;select&quot;:&quot;mt-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterNumber&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.number&quot;,&quot;input&quot;:&quot;w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.select&quot;,&quot;base&quot;:&quot;&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterInputText&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.input-text&quot;,&quot;base&quot;:&quot;min-w-[9.5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;searchBox&quot;:[{&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8&quot;,&quot;iconClose&quot;:&quot;text-pg-primary-400 dark:text-pg-primary-200&quot;,&quot;iconSearch&quot;:&quot;text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;primaryKeyAlias&quot;:null,&quot;ignoreTablePrefix&quot;:true,&quot;setUp&quot;:[{&quot;header&quot;:[{&quot;name&quot;:&quot;header&quot;,&quot;searchInput&quot;:true,&quot;toggleColumns&quot;:true,&quot;softDeletes&quot;:false,&quot;showMessageSoftDeletes&quot;:false,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;wireLoading&quot;:false},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Header&quot;,&quot;s&quot;:&quot;wrbl&quot;}],&quot;footer&quot;:[{&quot;name&quot;:&quot;footer&quot;,&quot;perPage&quot;:10,&quot;perPageValues&quot;:[[10,25,50,100,0],{&quot;s&quot;:&quot;arr&quot;}],&quot;recordCount&quot;:&quot;full&quot;,&quot;pagination&quot;:null,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;pageName&quot;:&quot;page&quot;},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Footer&quot;,&quot;s&quot;:&quot;wrbl&quot;}],&quot;exportable&quot;:[{&quot;name&quot;:&quot;exportable&quot;,&quot;csvSeparator&quot;:&quot;,&quot;,&quot;csvDelimiter&quot;:&quot;\\&quot;&quot;,&quot;type&quot;:[[&quot;csv&quot;],{&quot;s&quot;:&quot;arr&quot;}],&quot;striped&quot;:&quot;d0d3d8&quot;,&quot;columnWidth&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;deleteFileAfterSend&quot;:true,&quot;batchExport&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;stripTags&quot;:false,&quot;fileName&quot;:&quot;export-language&quot;},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Exportable&quot;,&quot;s&quot;:&quot;wrbl&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;showErrorBag&quot;:false,&quot;rowIndex&quot;:true,&quot;readyToLoad&quot;:false,&quot;columns&quot;:[[[{&quot;title&quot;:&quot;English&quot;,&quot;field&quot;:&quot;english&quot;,&quot;dataField&quot;:&quot;english&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;style&quot;,&quot;bodyStyle&quot;:&quot;width: calc(25 * 3ch); word-wrap: break-word; white-space: normal; line-height: 1.8;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Arabic&quot;,&quot;field&quot;:&quot;value&quot;,&quot;dataField&quot;:&quot;value&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;text-wrap&quot;,&quot;headerStyle&quot;:&quot;white-space: normal;&quot;,&quot;bodyClass&quot;:&quot;style&quot;,&quot;bodyStyle&quot;:&quot;max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[{&quot;hasPermission&quot;:true,&quot;fallback&quot;:null,&quot;saveOnMouseOut&quot;:true},{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;headers&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;search&quot;:&quot;&quot;,&quot;currentTable&quot;:&quot;&quot;,&quot;total&quot;:0,&quot;totalCurrentPage&quot;:0,&quot;supportModel&quot;:true,&quot;paginateRaw&quot;:false,&quot;measurePerformance&quot;:false,&quot;checkbox&quot;:false,&quot;checkboxAll&quot;:false,&quot;checkboxValues&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;checkboxAttribute&quot;:&quot;id&quot;,&quot;filters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filtered&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;enabledFilters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;select&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;showFilters&quot;:false,&quot;withoutResourcesActions&quot;:false,&quot;additionalCacheKey&quot;:&quot;&quot;,&quot;persist&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;persistPrefix&quot;:&quot;&quot;,&quot;radio&quot;:false,&quot;radioAttribute&quot;:&quot;id&quot;,&quot;selectedRow&quot;:&quot;&quot;,&quot;softDeletes&quot;:&quot;&quot;,&quot;sortDirection&quot;:&quot;asc&quot;,&quot;multiSort&quot;:false,&quot;sortArray&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerTotalColumn&quot;:false,&quot;footerTotalColumn&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;batchExporting&quot;:false,&quot;batchFinished&quot;:false,&quot;batchId&quot;:&quot;&quot;,&quot;batchName&quot;:&quot;PowerGrid batch export&quot;,&quot;showExporting&quot;:true,&quot;batchProgress&quot;:0,&quot;exportedFiles&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;exportableJobClass&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Jobs\\\\ExportJob&quot;,&quot;batchErrors&quot;:false},&quot;memo&quot;:{&quot;id&quot;:&quot;5jbw6njwZOFn04CkXZ3Y&quot;,&quot;name&quot;:&quot;admin.table.lanuage-line-table&quot;,&quot;path&quot;:&quot;admin\\/languages\\/ar\\/translations&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;6f56ccdfb4d54afdb76d80c2b09ffda0c2baefffe7c9766032ba05de1ee5a48c&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">fetchDatasource</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540677127\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1727554565 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">13105</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/languages/ar/translations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjIxb0huMzB5OUFGWjBNQWJ1cnlpZWc9PSIsInZhbHVlIjoiSU9rditGemxRN0R4WWpNbnZ3Tlgrc1RNTUpaZ2xXY1dvL2ZMMVNjbzdpVzNnbFFKZ25WeG5WcTlNWUR4STdkSDdDZlNCeDRDdVNoTjNCSnZOTG81K3UrcGFhNlJQY3prSzg2OG5FcldBV3RXeDhWSUkwNzFad095TG9BbWxZMmQiLCJtYWMiOiJkZDhmOWEyMjgzZGMwMTQzODJlNTFhYTA3Mjg3ZDQwYmY3NjI1NjY2YzIzZjMwYjU1NzFkZTE2Y2JhMzliMjg1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImIxVlVyenh6dzZneTNQZnc4T1htUUE9PSIsInZhbHVlIjoiT2pHaU53V0VlN2ViaUNtVUVjaVZ0ZjFuRVI0WEdWNE5CQUNWd0VuZC9zdE9PWEpWZFRuT1NLejNBTFFjOUVyM1AwL05QcVNmY2NQN0F2MUxKdFEvVlNyU0czckZsQTVFSVZNV0U0c0w5V0Qxd3krb3pPeFhxTXVocEpiUGNTTFUiLCJtYWMiOiJlYTFhNzc5ZDM0ZDIzYThiYTdmNjM4NTMzZjE5OGE5MTczNWNhYTU5OWFiOGYwYWYxMjdlM2FmNTgwNmVhMGVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1727554565\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1942907876 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942907876\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1706452532 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:45:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1706452532\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-19448895 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://127.0.0.1:8000/admin/languages/ar/translations</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-19448895\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}