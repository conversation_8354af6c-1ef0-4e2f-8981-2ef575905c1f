{"__meta": {"id": "01JZZGB2627DFHK64W806A90P7", "datetime": "2025-07-12 04:24:02", "utime": **********.243578, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752330241.50489, "end": **********.24359, "duration": 0.7387001514434814, "duration_str": "739ms", "measures": [{"label": "Booting", "start": 1752330241.50489, "relative_start": 0, "end": **********.07532, "relative_end": **********.07532, "duration": 0.****************, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.07533, "relative_start": 0.****************, "end": **********.243592, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "168ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.092365, "relative_start": 0.***************, "end": **********.094907, "relative_end": **********.094907, "duration": 0.0025420188903808594, "duration_str": "2.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.table-base", "start": **********.19396, "relative_start": 0.****************, "end": **********.19396, "relative_end": **********.19396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header", "start": **********.194613, "relative_start": 0.***************, "end": **********.194613, "relative_end": **********.194613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "start": **********.19531, "relative_start": 0.6904201507568359, "end": **********.19531, "relative_end": **********.19531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye-off", "start": **********.196947, "relative_start": 0.6920571327209473, "end": **********.196947, "relative_end": **********.196947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.197636, "relative_start": 0.6927459239959717, "end": **********.197636, "relative_end": **********.197636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.198068, "relative_start": 0.6931779384613037, "end": **********.198068, "relative_end": **********.198068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.198399, "relative_start": 0.6935091018676758, "end": **********.198399, "relative_end": **********.198399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.198683, "relative_start": 0.6937930583953857, "end": **********.198683, "relative_end": **********.198683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.198964, "relative_start": 0.6940741539001465, "end": **********.198964, "relative_end": **********.198964, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.199243, "relative_start": 0.6943531036376953, "end": **********.199243, "relative_end": **********.199243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.199522, "relative_start": 0.6946320533752441, "end": **********.199522, "relative_end": **********.199522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.199852, "relative_start": 0.6949620246887207, "end": **********.199852, "relative_end": **********.199852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "start": **********.200221, "relative_start": 0.6953310966491699, "end": **********.200221, "relative_end": **********.200221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.filters", "start": **********.202917, "relative_start": 0.6980271339416504, "end": **********.202917, "relative_end": **********.202917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.filter", "start": **********.203476, "relative_start": 0.6985859870910645, "end": **********.203476, "relative_end": **********.203476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.search", "start": **********.204064, "relative_start": 0.6991739273071289, "end": **********.204064, "relative_end": **********.204064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.search", "start": **********.205389, "relative_start": 0.7004990577697754, "end": **********.205389, "relative_end": **********.205389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "start": **********.207064, "relative_start": 0.7021739482879639, "end": **********.207064, "relative_end": **********.207064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "start": **********.207906, "relative_start": 0.7030160427093506, "end": **********.207906, "relative_end": **********.207906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.filter", "start": **********.208454, "relative_start": 0.703563928604126, "end": **********.208454, "relative_end": **********.208454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.filters.select", "start": **********.20932, "relative_start": 0.704430103302002, "end": **********.20932, "relative_end": **********.20932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.down", "start": **********.210238, "relative_start": 0.705348014831543, "end": **********.210238, "relative_end": **********.210238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.filters.select", "start": **********.210641, "relative_start": 0.7057509422302246, "end": **********.210641, "relative_end": **********.210641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.down", "start": **********.211207, "relative_start": 0.7063169479370117, "end": **********.211207, "relative_end": **********.211207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.filters.date-picker", "start": **********.211604, "relative_start": 0.7067141532897949, "end": **********.211604, "relative_end": **********.211604, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table", "start": **********.212515, "relative_start": 0.707625150680542, "end": **********.212515, "relative_end": **********.212515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.213401, "relative_start": 0.7085111141204834, "end": **********.213401, "relative_end": **********.213401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.214225, "relative_start": 0.7093350887298584, "end": **********.214225, "relative_end": **********.214225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.216029, "relative_start": 0.7111389636993408, "end": **********.216029, "relative_end": **********.216029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.21648, "relative_start": 0.711590051651001, "end": **********.21648, "relative_end": **********.21648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.216859, "relative_start": 0.7119691371917725, "end": **********.216859, "relative_end": **********.216859, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.217365, "relative_start": 0.712475061416626, "end": **********.217365, "relative_end": **********.217365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.217727, "relative_start": 0.7128369808197021, "end": **********.217727, "relative_end": **********.217727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.218006, "relative_start": 0.713115930557251, "end": **********.218006, "relative_end": **********.218006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.218504, "relative_start": 0.7136139869689941, "end": **********.218504, "relative_end": **********.218504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.218868, "relative_start": 0.7139780521392822, "end": **********.218868, "relative_end": **********.218868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.219142, "relative_start": 0.7142519950866699, "end": **********.219142, "relative_end": **********.219142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.219671, "relative_start": 0.7147810459136963, "end": **********.219671, "relative_end": **********.219671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.220056, "relative_start": 0.7151660919189453, "end": **********.220056, "relative_end": **********.220056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.220445, "relative_start": 0.71555495262146, "end": **********.220445, "relative_end": **********.220445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.221109, "relative_start": 0.7162189483642578, "end": **********.221109, "relative_end": **********.221109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.222448, "relative_start": 0.7175581455230713, "end": **********.222448, "relative_end": **********.222448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.22286, "relative_start": 0.7179701328277588, "end": **********.22286, "relative_end": **********.22286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.223427, "relative_start": 0.7185370922088623, "end": **********.223427, "relative_end": **********.223427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.223814, "relative_start": 0.7189240455627441, "end": **********.223814, "relative_end": **********.223814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.224108, "relative_start": 0.7192180156707764, "end": **********.224108, "relative_end": **********.224108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.224599, "relative_start": 0.7197089195251465, "end": **********.224599, "relative_end": **********.224599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.224973, "relative_start": 0.7200829982757568, "end": **********.224973, "relative_end": **********.224973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.225261, "relative_start": 0.7203710079193115, "end": **********.225261, "relative_end": **********.225261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f80280cbef3647ab5b62bdedf0ef25a1", "start": **********.226074, "relative_start": 0.7211840152740479, "end": **********.226074, "relative_end": **********.226074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-down", "start": **********.229959, "relative_start": 0.7250690460205078, "end": **********.229959, "relative_end": **********.229959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.230635, "relative_start": 0.7257449626922607, "end": **********.230635, "relative_end": **********.230635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.custom-loading", "start": **********.231364, "relative_start": 0.7264740467071533, "end": **********.231364, "relative_end": **********.231364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.inline-filters", "start": **********.232102, "relative_start": 0.7272119522094727, "end": **********.232102, "relative_end": **********.232102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.th-empty", "start": **********.233026, "relative_start": 0.7281360626220703, "end": **********.233026, "relative_end": **********.233026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.no-data-label", "start": **********.233654, "relative_start": 0.7287640571594238, "end": **********.233654, "relative_end": **********.233654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table-base", "start": **********.233979, "relative_start": 0.7290890216827393, "end": **********.233979, "relative_end": **********.233979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.footer", "start": **********.234654, "relative_start": 0.7297639846801758, "end": **********.234654, "relative_end": **********.234654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.down", "start": **********.235354, "relative_start": 0.730463981628418, "end": **********.235354, "relative_end": **********.235354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.pagination", "start": **********.23592, "relative_start": 0.7310299873352051, "end": **********.23592, "relative_end": **********.23592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.240641, "relative_start": 0.7357511520385742, "end": **********.24159, "relative_end": **********.24159, "duration": 0.0009489059448242188, "duration_str": "949μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 37596656, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 60, "nb_templates": 60, "templates": [{"name": "1x livewire-powergrid::components.frameworks.tailwind.table-base", "param_count": null, "params": [], "start": **********.193918, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/table-base.blade.phplivewire-powergrid::components.frameworks.tailwind.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header", "param_count": null, "params": [], "start": **********.194579, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header.blade.phplivewire-powergrid::components.frameworks.tailwind.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "param_count": null, "params": [], "start": **********.195279, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/toggle-columns.blade.phplivewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Ftoggle-columns.blade.php&line=1", "ajax": false, "filename": "toggle-columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.toggle-columns"}, {"name": "1x livewire-powergrid::components.icons.eye-off", "param_count": null, "params": [], "start": **********.196914, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye-off.blade.phplivewire-powergrid::components.icons.eye-off", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye-off.blade.php&line=1", "ajax": false, "filename": "eye-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.eye-off"}, {"name": "8x livewire-powergrid::components.icons.eye", "param_count": null, "params": [], "start": **********.197604, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye.blade.phplivewire-powergrid::components.icons.eye", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye.blade.php&line=1", "ajax": false, "filename": "eye.blade.php", "line": "?"}, "render_count": 8, "name_original": "livewire-powergrid::components.icons.eye"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "param_count": null, "params": [], "start": **********.200187, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsoft-deletes.blade.php&line=1", "ajax": false, "filename": "soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.soft-deletes"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.filters", "param_count": null, "params": [], "start": **********.202885, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/filters.blade.phplivewire-powergrid::components.frameworks.tailwind.header.filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Ffilters.blade.php&line=1", "ajax": false, "filename": "filters.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.filters"}, {"name": "1x livewire-powergrid::components.icons.filter", "param_count": null, "params": [], "start": **********.203444, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/filter.blade.phplivewire-powergrid::components.icons.filter", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Ffilter.blade.php&line=1", "ajax": false, "filename": "filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.filter"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.search", "param_count": null, "params": [], "start": **********.204021, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/search.blade.phplivewire-powergrid::components.frameworks.tailwind.header.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.search"}, {"name": "1x livewire-powergrid::components.icons.search", "param_count": null, "params": [], "start": **********.205342, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/search.blade.phplivewire-powergrid::components.icons.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.search"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "param_count": null, "params": [], "start": **********.207023, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/enabled-filters.blade.phplivewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fenabled-filters.blade.php&line=1", "ajax": false, "filename": "enabled-filters.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.enabled-filters"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "param_count": null, "params": [], "start": **********.207866, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/message-soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fmessage-soft-deletes.blade.php&line=1", "ajax": false, "filename": "message-soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.filter", "param_count": null, "params": [], "start": **********.208411, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/filter.blade.phplivewire-powergrid::components.frameworks.tailwind.filter", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffilter.blade.php&line=1", "ajax": false, "filename": "filter.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.filter"}, {"name": "2x livewire-powergrid::components.frameworks.tailwind.filters.select", "param_count": null, "params": [], "start": **********.209284, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/filters/select.blade.phplivewire-powergrid::components.frameworks.tailwind.filters.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffilters%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.frameworks.tailwind.filters.select"}, {"name": "3x livewire-powergrid::components.icons.down", "param_count": null, "params": [], "start": **********.210201, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/down.blade.phplivewire-powergrid::components.icons.down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fdown.blade.php&line=1", "ajax": false, "filename": "down.blade.php", "line": "?"}, "render_count": 3, "name_original": "livewire-powergrid::components.icons.down"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.filters.date-picker", "param_count": null, "params": [], "start": **********.211569, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/filters/date-picker.blade.phplivewire-powergrid::components.frameworks.tailwind.filters.date-picker", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffilters%2Fdate-picker.blade.php&line=1", "ajax": false, "filename": "date-picker.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.filters.date-picker"}, {"name": "1x livewire-powergrid::components.table", "param_count": null, "params": [], "start": **********.21248, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table.blade.phplivewire-powergrid::components.table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table"}, {"name": "2x livewire-powergrid::components.table.tr", "param_count": null, "params": [], "start": **********.213367, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/tr.blade.phplivewire-powergrid::components.table.tr", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Ftr.blade.php&line=1", "ajax": false, "filename": "tr.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.table.tr"}, {"name": "8x livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.214155, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}, "render_count": 8, "name_original": "livewire-powergrid::components.cols"}, {"name": "7x __components::6da562a044c365ee08fb55b6325b90a1", "param_count": null, "params": [], "start": **********.215995, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6da562a044c365ee08fb55b6325b90a1.blade.php__components::6da562a044c365ee08fb55b6325b90a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6da562a044c365ee08fb55b6325b90a1.blade.php&line=1", "ajax": false, "filename": "6da562a044c365ee08fb55b6325b90a1.blade.php", "line": "?"}, "render_count": 7, "name_original": "__components::6da562a044c365ee08fb55b6325b90a1"}, {"name": "7x livewire-powergrid::components.icons.chevron-up-down", "param_count": null, "params": [], "start": **********.216445, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-up-down.blade.phplivewire-powergrid::components.icons.chevron-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-up-down.blade.php&line=1", "ajax": false, "filename": "chevron-up-down.blade.php", "line": "?"}, "render_count": 7, "name_original": "livewire-powergrid::components.icons.chevron-up-down"}, {"name": "1x __components::f80280cbef3647ab5b62bdedf0ef25a1", "param_count": null, "params": [], "start": **********.226043, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f80280cbef3647ab5b62bdedf0ef25a1.blade.php__components::f80280cbef3647ab5b62bdedf0ef25a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff80280cbef3647ab5b62bdedf0ef25a1.blade.php&line=1", "ajax": false, "filename": "f80280cbef3647ab5b62bdedf0ef25a1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f80280cbef3647ab5b62bdedf0ef25a1"}, {"name": "1x livewire-powergrid::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.229912, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-down.blade.phplivewire-powergrid::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.chevron-down"}, {"name": "1x components.custom-loading", "param_count": null, "params": [], "start": **********.23133, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/custom-loading.blade.phpcomponents.custom-loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcustom-loading.blade.php&line=1", "ajax": false, "filename": "custom-loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.custom-loading"}, {"name": "1x livewire-powergrid::components.inline-filters", "param_count": null, "params": [], "start": **********.232068, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/inline-filters.blade.phplivewire-powergrid::components.inline-filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Finline-filters.blade.php&line=1", "ajax": false, "filename": "inline-filters.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.inline-filters"}, {"name": "1x livewire-powergrid::components.table.th-empty", "param_count": null, "params": [], "start": **********.23299, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/th-empty.blade.phplivewire-powergrid::components.table.th-empty", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fth-empty.blade.php&line=1", "ajax": false, "filename": "th-empty.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table.th-empty"}, {"name": "1x livewire-powergrid::components.table.no-data-label", "param_count": null, "params": [], "start": **********.233618, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/no-data-label.blade.phplivewire-powergrid::components.table.no-data-label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fno-data-label.blade.php&line=1", "ajax": false, "filename": "no-data-label.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table.no-data-label"}, {"name": "1x livewire-powergrid::components.table-base", "param_count": null, "params": [], "start": **********.233945, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table-base.blade.phplivewire-powergrid::components.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.footer", "param_count": null, "params": [], "start": **********.234619, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/footer.blade.phplivewire-powergrid::components.frameworks.tailwind.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.footer"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.pagination", "param_count": null, "params": [], "start": **********.235881, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/pagination.blade.phplivewire-powergrid::components.frameworks.tailwind.pagination", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.pagination"}]}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0044599999999999996, "accumulated_duration_str": "4.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.0974228, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 12.78}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.112659, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 12.78, "width_percent": 27.578}, {"sql": "select `template_id`, `template_name` from `whatsapp_templates`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/Admin/Table/CampaignTable.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Table\\CampaignTable.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Builder.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Builder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/ModelProcessor.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\ModelProcessor.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/ModelProcessor.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\ModelProcessor.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/ProcessDataSource.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\ProcessDataSource.php", "line": 47}], "start": **********.172858, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "CampaignTable.php:159", "source": {"index": 15, "namespace": null, "name": "app/Livewire/Admin/Table/CampaignTable.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Table\\CampaignTable.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FTable%2FCampaignTable.php&line=159", "ajax": false, "filename": "CampaignTable.php", "line": "159"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 40.359, "width_percent": 19.283}, {"sql": "select count(*) as aggregate from `campaigns` left join `whatsapp_templates` on `campaigns`.`template_id` = `whatsapp_templates`.`template_id`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/ModelProcessor.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\ModelProcessor.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/ProcessDataSource.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\ProcessDataSource.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 174}, {"index": 20, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 140}], "start": **********.1809108, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DataSourceBase.php:85", "source": {"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FDataSource%2FProcessors%2FDataSourceBase.php&line=85", "ajax": false, "filename": "DataSourceBase.php", "line": "85"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 59.641, "width_percent": 21.3}, {"sql": "select `template_id`, `template_name` from `whatsapp_templates`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/Admin/Table/CampaignTable.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Table\\CampaignTable.php", "line": 159}, {"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/Concerns/Filter.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Concerns\\Filter.php", "line": 323}, {"index": 17, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 288}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.185042, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CampaignTable.php:159", "source": {"index": 15, "namespace": null, "name": "app/Livewire/Admin/Table/CampaignTable.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Table\\CampaignTable.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FTable%2FCampaignTable.php&line=159", "ajax": false, "filename": "CampaignTable.php", "line": "159"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 80.942, "width_percent": 9.417}, {"sql": "select `template_id`, `template_name` from `whatsapp_templates`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/Admin/Table/CampaignTable.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Table\\CampaignTable.php", "line": 159}, {"index": 16, "namespace": "view", "name": "livewire-powergrid::components.frameworks.tailwind.header", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header.blade.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.2007189, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CampaignTable.php:159", "source": {"index": 15, "namespace": null, "name": "app/Livewire/Admin/Table/CampaignTable.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Table\\CampaignTable.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FTable%2FCampaignTable.php&line=159", "ajax": false, "filename": "CampaignTable.php", "line": "159"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 90.359, "width_percent": 9.641}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.table.campaign-table #hERkpAzSwhmesnRY3EgV": "array:4 [\n  \"data\" => array:44 [\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"DESC\"\n    \"tableName\" => \"campaign-table-r3hjpl-table\"\n    \"showFilters\" => false\n    \"deferLoading\" => true\n    \"loadingComponent\" => \"components.custom-loading\"\n    \"theme\" => array:17 [\n      \"name\" => \"tailwind\"\n      \"root\" => \"livewire-powergrid::components.frameworks.tailwind\"\n      \"table\" => array:3 [\n        \"layout\" => array:5 [\n          \"base\" => \"p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8\"\n          \"div\" => \"rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n          \"table\" => \"min-w-full dark:!bg-primary-800\"\n          \"container\" => \"-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8\"\n          \"actions\" => \"flex gap-2\"\n        ]\n        \"header\" => array:4 [\n          \"thead\" => \"shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900\"\n          \"tr\" => \"\"\n          \"th\" => \"font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300\"\n          \"thAction\" => \"!font-bold\"\n        ]\n        \"body\" => array:10 [\n          \"tbody\" => \"text-pg-primary-800\"\n          \"tbodyEmpty\" => \"\"\n          \"tr\" => \"border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700\"\n          \"td\" => \"px-3 py-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdEmpty\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdSummarize\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2\"\n          \"trSummarize\" => \"\"\n          \"tdFilters\" => \"\"\n          \"trFilters\" => \"\"\n          \"tdActionsContainer\" => \"flex gap-2\"\n        ]\n      ]\n      \"footer\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n        \"footer\" => \"border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n        \"footer_with_pagination\" => \"md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900\"\n      ]\n      \"cols\" => array:1 [\n        \"div\" => \"select-none flex items-center gap-1\"\n      ]\n      \"editable\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.editable\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"layout\" => array:4 [\n        \"table\" => \"livewire-powergrid::components.frameworks.tailwind.table-base\"\n        \"header\" => \"livewire-powergrid::components.frameworks.tailwind.header\"\n        \"pagination\" => \"livewire-powergrid::components.frameworks.tailwind.pagination\"\n        \"footer\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n      ]\n      \"toggleable\" => array:1 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.toggleable\"\n      ]\n      \"checkbox\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900\"\n      ]\n      \"radio\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-radio rounded-full transition ease-in-out duration-100\"\n      ]\n      \"filterBoolean\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.boolean\"\n        \"base\" => \"min-w-[5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterDatePicker\" => array:3 [\n        \"base\" => \"\"\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.date-picker\"\n        \"input\" => \"flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n      ]\n      \"filterMultiSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.multi-select\"\n        \"base\" => \"inline-block relative w-full\"\n        \"select\" => \"mt-1\"\n      ]\n      \"filterNumber\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.number\"\n        \"input\" => \"w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6\"\n      ]\n      \"filterSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.select\"\n        \"base\" => \"\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterInputText\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.input-text\"\n        \"base\" => \"min-w-[9.5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"searchBox\" => array:3 [\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8\"\n        \"iconClose\" => \"text-pg-primary-400 dark:text-pg-primary-200\"\n        \"iconSearch\" => \"text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200\"\n      ]\n    ]\n    \"primaryKey\" => \"id\"\n    \"primaryKeyAlias\" => null\n    \"ignoreTablePrefix\" => true\n    \"setUp\" => array:2 [\n      \"header\" => array:8 [\n        \"name\" => \"header\"\n        \"searchInput\" => true\n        \"toggleColumns\" => true\n        \"softDeletes\" => false\n        \"showMessageSoftDeletes\" => false\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"wireLoading\" => false\n      ]\n      \"footer\" => array:8 [\n        \"name\" => \"footer\"\n        \"perPage\" => 10\n        \"perPageValues\" => array:5 [\n          0 => 10\n          1 => 25\n          2 => 50\n          3 => 100\n          4 => 0\n        ]\n        \"recordCount\" => \"full\"\n        \"pagination\" => null\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"pageName\" => \"page\"\n      ]\n    ]\n    \"showErrorBag\" => false\n    \"rowIndex\" => true\n    \"readyToLoad\" => true\n    \"columns\" => array:8 [\n      0 => array:25 [\n        \"title\" => \"ID\"\n        \"field\" => \"id\"\n        \"dataField\" => \"id\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      1 => array:25 [\n        \"title\" => \"Campaign Name\"\n        \"field\" => \"name\"\n        \"dataField\" => \"name\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      2 => array:25 [\n        \"title\" => \"Template\"\n        \"field\" => \"template_name\"\n        \"dataField\" => \"template_name\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => array:16 [\n          \"className\" => \"PowerComponents\\LivewirePowerGrid\\Components\\Filters\\FilterSelect\"\n          \"builder\" => null\n          \"collection\" => null\n          \"component\" => \"\"\n          \"attributes\" => []\n          \"baseClass\" => \"\"\n          \"filterRelation\" => []\n          \"column\" => \"template_name\"\n          \"field\" => \"whatsapp_templates.template_id\"\n          \"key\" => \"select\"\n          \"dataSource\" => []\n          \"optionValue\" => \"template_id\"\n          \"optionLabel\" => \"template_name\"\n          \"depends\" => []\n          \"params\" => []\n          \"computedDatasource\" => \"\"\n        ]\n        \"customContent\" => []\n      ]\n      3 => array:25 [\n        \"title\" => \"Relation Type\"\n        \"field\" => \"rel_type\"\n        \"dataField\" => \"rel_type\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => array:16 [\n          \"className\" => \"PowerComponents\\LivewirePowerGrid\\Components\\Filters\\FilterSelect\"\n          \"builder\" => null\n          \"collection\" => null\n          \"component\" => \"\"\n          \"attributes\" => []\n          \"baseClass\" => \"\"\n          \"filterRelation\" => []\n          \"column\" => \"rel_type\"\n          \"field\" => \"campaigns.rel_type\"\n          \"key\" => \"select\"\n          \"dataSource\" => array:2 [\n            0 => array:2 [\n              \"value\" => \"lead\"\n              \"label\" => \"Lead\"\n            ]\n            1 => array:2 [\n              \"value\" => \"customer\"\n              \"label\" => \"Customer\"\n            ]\n          ]\n          \"optionValue\" => \"value\"\n          \"optionLabel\" => \"label\"\n          \"depends\" => []\n          \"params\" => []\n          \"computedDatasource\" => \"\"\n        ]\n        \"customContent\" => []\n      ]\n      4 => array:25 [\n        \"title\" => \"Total \"\n        \"field\" => \"sending_count\"\n        \"dataField\" => \"sending_count\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      5 => array:25 [\n        \"title\" => \"Delivered To\"\n        \"field\" => \"delivered\"\n        \"dataField\" => \"delivered\"\n        \"placeholder\" => \"\"\n        \"searchable\" => false\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      6 => array:25 [\n        \"title\" => \"Read By\"\n        \"field\" => \"read_by\"\n        \"dataField\" => \"read_by\"\n        \"placeholder\" => \"\"\n        \"searchable\" => false\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      7 => array:25 [\n        \"title\" => \"Created At\"\n        \"field\" => \"created_at_formatted\"\n        \"dataField\" => \"created_at\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => array:11 [\n          \"className\" => \"PowerComponents\\LivewirePowerGrid\\Components\\Filters\\FilterDatePicker\"\n          \"builder\" => null\n          \"collection\" => null\n          \"component\" => \"\"\n          \"attributes\" => []\n          \"baseClass\" => \"\"\n          \"filterRelation\" => []\n          \"column\" => \"created_at\"\n          \"field\" => \"campaigns.created_at\"\n          \"key\" => \"date\"\n          \"params\" => []\n        ]\n        \"customContent\" => []\n      ]\n    ]\n    \"headers\" => []\n    \"search\" => \"\"\n    \"currentTable\" => \"campaigns\"\n    \"total\" => 0\n    \"totalCurrentPage\" => 0\n    \"supportModel\" => true\n    \"paginateRaw\" => false\n    \"measurePerformance\" => false\n    \"checkbox\" => false\n    \"checkboxAll\" => false\n    \"checkboxValues\" => []\n    \"checkboxAttribute\" => \"id\"\n    \"filters\" => []\n    \"filtered\" => []\n    \"enabledFilters\" => []\n    \"select\" => []\n    \"withoutResourcesActions\" => false\n    \"additionalCacheKey\" => \"\"\n    \"persist\" => []\n    \"persistPrefix\" => \"\"\n    \"radio\" => false\n    \"radioAttribute\" => \"id\"\n    \"selectedRow\" => \"\"\n    \"softDeletes\" => \"\"\n    \"multiSort\" => false\n    \"sortArray\" => []\n    \"headerTotalColumn\" => false\n    \"footerTotalColumn\" => false\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"admin.table.campaign-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Table\\CampaignTable\"\n  \"id\" => \"hERkpAzSwhmesnRY3EgV\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Table\\CampaignTable@fetchDatasource<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/power-components/livewire-powergrid/src/PowerGridComponent.php:79-82</a>", "middleware": "web", "duration": "739ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1113925889 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1113925889\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-618709849 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"15957 characters\">{&quot;data&quot;:{&quot;sortField&quot;:&quot;created_at&quot;,&quot;sortDirection&quot;:&quot;DESC&quot;,&quot;tableName&quot;:&quot;campaign-table-r3hjpl-table&quot;,&quot;showFilters&quot;:false,&quot;deferLoading&quot;:true,&quot;loadingComponent&quot;:&quot;components.custom-loading&quot;,&quot;theme&quot;:[{&quot;name&quot;:&quot;tailwind&quot;,&quot;root&quot;:&quot;livewire-powergrid::components.frameworks.tailwind&quot;,&quot;table&quot;:[{&quot;layout&quot;:[{&quot;base&quot;:&quot;p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8&quot;,&quot;div&quot;:&quot;rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;table&quot;:&quot;min-w-full dark:!bg-primary-800&quot;,&quot;container&quot;:&quot;-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8&quot;,&quot;actions&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;header&quot;:[{&quot;thead&quot;:&quot;shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900&quot;,&quot;tr&quot;:&quot;&quot;,&quot;th&quot;:&quot;font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300&quot;,&quot;thAction&quot;:&quot;!font-bold&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;body&quot;:[{&quot;tbody&quot;:&quot;text-pg-primary-800&quot;,&quot;tbodyEmpty&quot;:&quot;&quot;,&quot;tr&quot;:&quot;border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700&quot;,&quot;td&quot;:&quot;px-3 py-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdEmpty&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdSummarize&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2&quot;,&quot;trSummarize&quot;:&quot;&quot;,&quot;tdFilters&quot;:&quot;&quot;,&quot;trFilters&quot;:&quot;&quot;,&quot;tdActionsContainer&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;footer&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;,&quot;footer&quot;:&quot;border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;footer_with_pagination&quot;:&quot;md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;cols&quot;:[{&quot;div&quot;:&quot;select-none flex items-center gap-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.editable&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;layout&quot;:[{&quot;table&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.table-base&quot;,&quot;header&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.header&quot;,&quot;pagination&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.pagination&quot;,&quot;footer&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;toggleable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.toggleable&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;checkbox&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;radio&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-radio rounded-full transition ease-in-out duration-100&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterBoolean&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.boolean&quot;,&quot;base&quot;:&quot;min-w-[5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterDatePicker&quot;:[{&quot;base&quot;:&quot;&quot;,&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.date-picker&quot;,&quot;input&quot;:&quot;flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterMultiSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.multi-select&quot;,&quot;base&quot;:&quot;inline-block relative w-full&quot;,&quot;select&quot;:&quot;mt-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterNumber&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.number&quot;,&quot;input&quot;:&quot;w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.select&quot;,&quot;base&quot;:&quot;&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterInputText&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.input-text&quot;,&quot;base&quot;:&quot;min-w-[9.5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;searchBox&quot;:[{&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8&quot;,&quot;iconClose&quot;:&quot;text-pg-primary-400 dark:text-pg-primary-200&quot;,&quot;iconSearch&quot;:&quot;text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;primaryKey&quot;:&quot;id&quot;,&quot;primaryKeyAlias&quot;:null,&quot;ignoreTablePrefix&quot;:true,&quot;setUp&quot;:[{&quot;header&quot;:[{&quot;name&quot;:&quot;header&quot;,&quot;searchInput&quot;:true,&quot;toggleColumns&quot;:true,&quot;softDeletes&quot;:false,&quot;showMessageSoftDeletes&quot;:false,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;wireLoading&quot;:false},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Header&quot;,&quot;s&quot;:&quot;wrbl&quot;}],&quot;footer&quot;:[{&quot;name&quot;:&quot;footer&quot;,&quot;perPage&quot;:10,&quot;perPageValues&quot;:[[10,25,50,100,0],{&quot;s&quot;:&quot;arr&quot;}],&quot;recordCount&quot;:&quot;full&quot;,&quot;pagination&quot;:null,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;pageName&quot;:&quot;page&quot;},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Footer&quot;,&quot;s&quot;:&quot;wrbl&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;showErrorBag&quot;:false,&quot;rowIndex&quot;:true,&quot;readyToLoad&quot;:false,&quot;columns&quot;:[[[{&quot;title&quot;:&quot;ID&quot;,&quot;field&quot;:&quot;id&quot;,&quot;dataField&quot;:&quot;id&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Campaign Name&quot;,&quot;field&quot;:&quot;name&quot;,&quot;dataField&quot;:&quot;name&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Template&quot;,&quot;field&quot;:&quot;template_name&quot;,&quot;dataField&quot;:&quot;template_name&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:[{&quot;className&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\Filters\\\\FilterSelect&quot;,&quot;builder&quot;:null,&quot;collection&quot;:null,&quot;component&quot;:&quot;&quot;,&quot;attributes&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;baseClass&quot;:&quot;&quot;,&quot;filterRelation&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;column&quot;:&quot;template_name&quot;,&quot;field&quot;:&quot;whatsapp_templates.template_id&quot;,&quot;key&quot;:&quot;select&quot;,&quot;dataSource&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;optionValue&quot;:&quot;template_id&quot;,&quot;optionLabel&quot;:&quot;template_name&quot;,&quot;depends&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;params&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;computedDatasource&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Relation Type&quot;,&quot;field&quot;:&quot;rel_type&quot;,&quot;dataField&quot;:&quot;rel_type&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:[{&quot;className&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\Filters\\\\FilterSelect&quot;,&quot;builder&quot;:null,&quot;collection&quot;:null,&quot;component&quot;:&quot;&quot;,&quot;attributes&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;baseClass&quot;:&quot;&quot;,&quot;filterRelation&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;column&quot;:&quot;rel_type&quot;,&quot;field&quot;:&quot;campaigns.rel_type&quot;,&quot;key&quot;:&quot;select&quot;,&quot;dataSource&quot;:[[[{&quot;value&quot;:&quot;lead&quot;,&quot;label&quot;:&quot;Lead&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;value&quot;:&quot;customer&quot;,&quot;label&quot;:&quot;Customer&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;optionValue&quot;:&quot;value&quot;,&quot;optionLabel&quot;:&quot;label&quot;,&quot;depends&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;params&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;computedDatasource&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Total &quot;,&quot;field&quot;:&quot;sending_count&quot;,&quot;dataField&quot;:&quot;sending_count&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Delivered To&quot;,&quot;field&quot;:&quot;delivered&quot;,&quot;dataField&quot;:&quot;delivered&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:false,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Read By&quot;,&quot;field&quot;:&quot;read_by&quot;,&quot;dataField&quot;:&quot;read_by&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:false,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Created At&quot;,&quot;field&quot;:&quot;created_at_formatted&quot;,&quot;dataField&quot;:&quot;created_at&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:[{&quot;className&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\Filters\\\\FilterDatePicker&quot;,&quot;builder&quot;:null,&quot;collection&quot;:null,&quot;component&quot;:&quot;&quot;,&quot;attributes&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;baseClass&quot;:&quot;&quot;,&quot;filterRelation&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;column&quot;:&quot;created_at&quot;,&quot;field&quot;:&quot;campaigns.created_at&quot;,&quot;key&quot;:&quot;date&quot;,&quot;params&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;headers&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;search&quot;:&quot;&quot;,&quot;currentTable&quot;:&quot;&quot;,&quot;total&quot;:0,&quot;totalCurrentPage&quot;:0,&quot;supportModel&quot;:true,&quot;paginateRaw&quot;:false,&quot;measurePerformance&quot;:false,&quot;checkbox&quot;:false,&quot;checkboxAll&quot;:false,&quot;checkboxValues&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;checkboxAttribute&quot;:&quot;id&quot;,&quot;filters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filtered&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;enabledFilters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;select&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;withoutResourcesActions&quot;:false,&quot;additionalCacheKey&quot;:&quot;&quot;,&quot;persist&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;persistPrefix&quot;:&quot;&quot;,&quot;radio&quot;:false,&quot;radioAttribute&quot;:&quot;id&quot;,&quot;selectedRow&quot;:&quot;&quot;,&quot;softDeletes&quot;:&quot;&quot;,&quot;multiSort&quot;:false,&quot;sortArray&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerTotalColumn&quot;:false,&quot;footerTotalColumn&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;hERkpAzSwhmesnRY3EgV&quot;,&quot;name&quot;:&quot;admin.table.campaign-table&quot;,&quot;path&quot;:&quot;admin\\/campaigns&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;0c04bb7c4f4025007b3892bdfab1486d8a7097d6dfafab69d5e48a48c9e02020&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">fetchDatasource</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618709849\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-374252272 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">17804</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/campaigns</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ijh6bjN4V3ZFUDNGYkgwM1hBV2sxcGc9PSIsInZhbHVlIjoiaHgxY2I5L3lQcHBhRm5hNlhOa3draTlzc0RDRDRpcm9xSnN2M1NBd1VtNHVlVEwzdkRRdjJBU1JEamVBeEVHaUl0bUpMdytkRkJ4WDNTWERrYUNXNkcwb0Q0TGVTS0orL1d6cVArQTZYMjE3eEp5aFNSYXJvcHJ3UXVOUG8xRDAiLCJtYWMiOiIyYzNhMTMzNDc5OWNkNWRmMDQ3Yjg2MWUxNzI5ZDg0YzE0YjU3NzAxZmM3OWNhMTM1ODYxZmI1NDRhM2VlMDRkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilh4ZzhJMURNZUcvazFuRENBS2k4aUE9PSIsInZhbHVlIjoiTWlpL1V0NS9TTG5MZ1ZnRXFySGFTNWxYeTdOMCtKVS9YeDFiVEFIK1RWanZ1WEx6MHRTMkFJYVRTdExuT1l6d2hralA5L0VXZDdOcTdjT1doY0JxbjVTODIyTnp6VE45ZmtSbHNMSnpwNWRrZmlMTEFXczZpYWR1cmtQNjZ6T0MiLCJtYWMiOiJhOTcyM2I1OGI5YmExOTAwYWJhNThmNjc1YWJjMjEzMjk0OTc0NWRiYmUzNjM5YWZkZTQ5YjNiOTcxOThjY2EwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374252272\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1507613910 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507613910\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-413311375 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:24:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413311375\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1637066227 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/campaigns</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637066227\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}