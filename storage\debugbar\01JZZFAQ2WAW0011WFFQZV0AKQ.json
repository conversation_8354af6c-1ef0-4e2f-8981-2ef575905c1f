{"__meta": {"id": "01JZZFAQ2WAW0011WFFQZV0AKQ", "datetime": "2025-07-13 02:06:22", "utime": **********.301674, "method": "POST", "uri": "/install/user", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.057735, "end": **********.30169, "duration": 2.243955135345459, "duration_str": "2.24s", "measures": [{"label": "Booting", "start": **********.057735, "relative_start": 0, "end": **********.625814, "relative_end": **********.625814, "duration": 0.****************, "duration_str": "568ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.625835, "relative_start": 0.****************, "end": **********.301692, "relative_end": 1.9073486328125e-06, "duration": 1.****************, "duration_str": "1.68s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.662053, "relative_start": 0.****************, "end": **********.664415, "relative_end": **********.664415, "duration": 0.002361774444580078, "duration_str": "2.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.298099, "relative_start": 2.****************, "end": **********.298652, "relative_end": **********.298652, "duration": 0.0005528926849365234, "duration_str": "553μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Wallis", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 96, "nb_statements": 96, "nb_visible_statements": 96, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07173999999999998, "accumulated_duration_str": "71.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9oefF5mPoeYxbiKHXQcHAQVPigwxNXWD1CRqBPR2' limit 1", "type": "query", "params": [], "bindings": ["9oefF5mPoeYxbiKHXQcHAQVPigwxNXWD1CRqBPR2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.6677, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 0.697}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'whatsmark_non_saas' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 696}], "start": **********.3393312, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0.697, "width_percent": 5.841}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'whatsmark_non_saas' and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 696}], "start": **********.345498, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 6.537, "width_percent": 1.227}, {"sql": "select `migration` from `migrations` order by `batch` asc, `migration` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 696}], "start": **********.3475778, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 7.764, "width_percent": 2.035}, {"sql": "select `migration` from `migrations` order by `batch` asc, `migration` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 696}], "start": **********.351941, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BoundMethod.php:36", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FBoundMethod.php&line=36", "ajax": false, "filename": "BoundMethod.php", "line": "36"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 9.799, "width_percent": 0.864}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'whatsmark_non_saas' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 136}, {"index": 15, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 40}, {"index": 16, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 193}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.970885, "duration": 0.00637, "duration_str": "6.37ms", "memory": 0, "memory_str": null, "filename": "InstallFinalizer.php:136", "source": {"index": 14, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 136}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FInstallFinalizer.php&line=136", "ajax": false, "filename": "InstallFinalizer.php", "line": "136"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 10.664, "width_percent": 8.879}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'whatsmark_non_saas' and table_name = 'users' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 150}, {"index": 15, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 40}, {"index": 16, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 193}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.978893, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "InstallFinalizer.php:150", "source": {"index": 14, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FInstallFinalizer.php&line=150", "ajax": false, "filename": "InstallFinalizer.php", "line": "150"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 19.543, "width_percent": 2.258}, {"sql": "select `name`, `payload` from `settings` where `group` = 'general'", "type": "query", "params": [], "bindings": ["general"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 277}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 108}], "start": **********.982232, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:28", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=28", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "28"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 21.801, "width_percent": 0.502}, {"sql": "select `name` from `settings` where `group` = 'general' and `locked` = 1", "type": "query", "params": [], "bindings": ["general", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 108}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsConfig.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsConfig.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsConfig.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsConfig.php", "line": 86}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 59}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 59}], "start": **********.998181, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:108", "source": {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=108", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "108"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 22.303, "width_percent": 0.892}, {"sql": "insert into `settings` (`created_at`, `group`, `name`, `payload`, `updated_at`) values ('2025-07-13 02:06:22', 'general', 'site_name', '\\\"Whatsmark\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'site_description', '\\\"Whatsapp marketing website\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'timezone', '\\\"Pacific\\\\/Tahiti\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'date_format', '\\\"d-m-Y\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'time_format', '\\\"12\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'site_logo', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'cover_page_image', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'favicon', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'site_dark_logo', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'general', 'active_language', '\\\"en\\\"', '2025-07-13 02:06:22') on duplicate key update `payload` = values(`payload`), `updated_at` = values(`updated_at`)", "type": "query", "params": [], "bindings": ["2025-07-13 02:06:22", "general", "site_name", "\"Whatsmark\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "site_description", "\"Whatsapp marketing website\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "timezone", "\"Pacific\\/Tahiti\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "date_format", "\"d-m-Y\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "time_format", "\"12\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "site_logo", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "cover_page_image", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "favicon", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "site_dark_logo", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "general", "active_language", "\"en\"", "2025-07-13 02:06:22"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 76}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 75}, {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 187}, {"index": 14, "namespace": null, "name": "app/Helpers/SettingsHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\SettingsHelper.php", "line": 113}, {"index": 16, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 40}], "start": **********.000476, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:76", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=76", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "76"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 23.195, "width_percent": 5.45}, {"sql": "select `name`, `payload` from `settings` where `group` = 'general'", "type": "query", "params": [], "bindings": ["general"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 187}, {"index": 19, "namespace": null, "name": "app/Helpers/SettingsHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\SettingsHelper.php", "line": 113}], "start": **********.006006, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:28", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=28", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "28"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 28.645, "width_percent": 0.613}, {"sql": "select * from `users` where (`email` = '<EMAIL>') limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 157}, {"index": 22, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 40}, {"index": 23, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 193}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.076189, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "InstallFinalizer.php:157", "source": {"index": 21, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 157}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FInstallFinalizer.php&line=157", "ajax": false, "filename": "InstallFinalizer.php", "line": "157"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 29.258, "width_percent": 1.659}, {"sql": "update `users` set `firstname` = 'reber', `email_verified_at` = '2025-07-13 02:06:21', `password` = '$2y$12$E2/8HWmbhoJMka/IJFpRDeb3b5ZNyQ/ao1gKhrPpYTCE2M14EhhhO', `users`.`updated_at` = '2025-07-13 02:06:22' where `id` = 1", "type": "query", "params": [], "bindings": ["reber", "2025-07-13 02:06:21", "$2y$12$E2/8HWmbhoJMka/IJFpRDeb3b5ZNyQ/ao1gKhrPpYTCE2M14EhhhO", "2025-07-13 02:06:22", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 157}, {"index": 21, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 40}, {"index": 22, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 193}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.080678, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "InstallFinalizer.php:157", "source": {"index": 20, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/InstallFinalizer.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\InstallFinalizer.php", "line": 157}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FInstallFinalizer.php&line=157", "ajax": false, "filename": "InstallFinalizer.php", "line": "157"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 30.917, "width_percent": 2.147}, {"sql": "select `name`, `payload` from `settings` where `group` = 'whats-mark'", "type": "query", "params": [], "bindings": ["whats-mark"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 277}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 108}], "start": **********.127728, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:28", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=28", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "28"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 33.064, "width_percent": 0.753}, {"sql": "select `name` from `settings` where `group` = 'whats-mark' and `locked` = 1", "type": "query", "params": [], "bindings": ["whats-mark", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 108}, {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsConfig.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsConfig.php", "line": 125}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsConfig.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsConfig.php", "line": 86}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 59}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 59}], "start": **********.130512, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:108", "source": {"index": 14, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=108", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "108"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 33.817, "width_percent": 0.558}, {"sql": "insert into `settings` (`created_at`, `group`, `name`, `payload`, `updated_at`) values ('2025-07-13 02:06:22', 'whats-mark', 'auto_lead_enabled', 'false', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'lead_status', 'null', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'lead_source', 'null', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'lead_assigned_to', 'null', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'enable_webhook_resend', 'false', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'webhook_resend_method', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'whatsapp_data_resend_to', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'stop_bots_keyword', '[]', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'restart_bots_after', 'null', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'only_agents_can_chat', 'false', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'enable_chat_notification_sound', 'false', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'enable_openai_in_chat', 'false', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'openai_secret_key', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'chat_model', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'is_open_ai_key_verify', 'false', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'enable_auto_clear_chat', 'false', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'auto_clear_history_time', 'null', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'wm_version', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'whatsmark_latest_version', '\\\"1.0.0\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'wm_verification_id', '\\\"ZGVmYXVsdC12ZXJpZmljYXRpb24taWQ=\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'wm_verification_token', '\\\"ZGVmYXVsdC12ZXJpZmljYXRpb24taWQ=|default-token\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'wm_last_verification', '\\\"**********\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'wm_support_until', '\\\"\\\"', '2025-07-13 02:06:22'), ('2025-07-13 02:06:22', 'whats-mark', 'wm_validate', 'true', '2025-07-13 02:06:22') on duplicate key update `payload` = values(`payload`), `updated_at` = values(`updated_at`)", "type": "query", "params": [], "bindings": ["2025-07-13 02:06:22", "whats-mark", "auto_lead_enabled", "false", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "lead_status", "null", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "lead_source", "null", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "lead_assigned_to", "null", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "enable_webhook_resend", "false", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "webhook_resend_method", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "whatsapp_data_resend_to", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "stop_bots_keyword", "[]", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "restart_bots_after", "null", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "only_agents_can_chat", "false", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "enable_chat_notification_sound", "false", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "enable_openai_in_chat", "false", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "openai_secret_key", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "chat_model", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "is_open_ai_key_verify", "false", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "enable_auto_clear_chat", "false", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "auto_clear_history_time", "null", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "wm_version", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "whatsmark_latest_version", "\"1.0.0\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "wm_verification_id", "\"ZGVmYXVsdC12ZXJpZmljYXRpb24taWQ=\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "wm_verification_token", "\"ZGVmYXVsdC12ZXJpZmljYXRpb24taWQ=|default-token\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "wm_last_verification", "\"**********\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "wm_support_until", "\"\"", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "whats-mark", "wm_validate", "true", "2025-07-13 02:06:22"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 76}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 75}, {"index": 13, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 187}, {"index": 14, "namespace": null, "name": "app/Helpers/SettingsHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\SettingsHelper.php", "line": 151}, {"index": 16, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 193}], "start": **********.132545, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:76", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=76", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "76"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 34.374, "width_percent": 6.05}, {"sql": "select `name`, `payload` from `settings` where `group` = 'whats-mark'", "type": "query", "params": [], "bindings": ["whats-mark"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 89}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 187}, {"index": 19, "namespace": null, "name": "app/Helpers/SettingsHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\SettingsHelper.php", "line": 151}], "start": **********.1396332, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:28", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=28", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "28"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 40.424, "width_percent": 0.878}, {"sql": "select * from `sources` where (`id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/DatabaseSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\DatabaseSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.145983, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "DatabaseSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/DatabaseSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\DatabaseSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FDatabaseSeeder.php&line=24", "ajax": false, "filename": "DatabaseSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 41.302, "width_percent": 2.467}, {"sql": "select * from `sources` where (`id` = 2) limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/DatabaseSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\DatabaseSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.149461, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/DatabaseSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\DatabaseSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FDatabaseSeeder.php&line=24", "ajax": false, "filename": "DatabaseSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 43.769, "width_percent": 0.432}, {"sql": "select * from `sources` where (`id` = 3) limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/DatabaseSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\DatabaseSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.151034, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/DatabaseSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\DatabaseSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FDatabaseSeeder.php&line=24", "ajax": false, "filename": "DatabaseSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 44.201, "width_percent": 0.488}, {"sql": "select * from `statuses` where (`name` = 'New') limit 1", "type": "query", "params": [], "bindings": ["New"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.156839, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "StatusSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FStatusSeeder.php&line=24", "ajax": false, "filename": "StatusSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 44.689, "width_percent": 1.478}, {"sql": "select * from `statuses` where (`name` = 'In Progress') limit 1", "type": "query", "params": [], "bindings": ["In Progress"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.159053, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "StatusSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FStatusSeeder.php&line=24", "ajax": false, "filename": "StatusSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 46.167, "width_percent": 0.432}, {"sql": "select * from `statuses` where (`name` = 'Contacted') limit 1", "type": "query", "params": [], "bindings": ["Contacted"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.160309, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "StatusSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FStatusSeeder.php&line=24", "ajax": false, "filename": "StatusSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 46.599, "width_percent": 0.404}, {"sql": "select * from `statuses` where (`name` = 'Qualified') limit 1", "type": "query", "params": [], "bindings": ["Qualified"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1615949, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "StatusSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FStatusSeeder.php&line=24", "ajax": false, "filename": "StatusSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 47.003, "width_percent": 0.293}, {"sql": "select * from `statuses` where (`name` = 'Closed') limit 1", "type": "query", "params": [], "bindings": ["Closed"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.162862, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "StatusSeeder.php:24", "source": {"index": 21, "namespace": null, "name": "database/seeders/StatusSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\StatusSeeder.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FStatusSeeder.php&line=24", "ajax": false, "filename": "StatusSeeder.php", "line": "24"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 47.296, "width_percent": 0.404}, {"sql": "select exists(select * from `permissions` where (`name` = 'source.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["source.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.167317, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 47.7, "width_percent": 1.018}, {"sql": "select exists(select * from `permissions` where (`name` = 'source.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["source.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.169395, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 48.718, "width_percent": 0.781}, {"sql": "select exists(select * from `permissions` where (`name` = 'source.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["source.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.170957, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 49.498, "width_percent": 0.753}, {"sql": "select exists(select * from `permissions` where (`name` = 'source.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["source.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.172525, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 50.251, "width_percent": 0.348}, {"sql": "select exists(select * from `permissions` where (`name` = 'ai_prompt.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["ai_prompt.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.173723, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 50.599, "width_percent": 0.404}, {"sql": "select exists(select * from `permissions` where (`name` = 'ai_prompt.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["ai_prompt.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.174972, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 51.004, "width_percent": 0.376}, {"sql": "select exists(select * from `permissions` where (`name` = 'ai_prompt.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["ai_prompt.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1762261, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 51.38, "width_percent": 0.39}, {"sql": "select exists(select * from `permissions` where (`name` = 'ai_prompt.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["ai_prompt.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.177512, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 51.77, "width_percent": 0.446}, {"sql": "select exists(select * from `permissions` where (`name` = 'canned_reply.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["canned_reply.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.178783, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 52.216, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'canned_reply.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["canned_reply.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.180142, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 52.718, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'canned_reply.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["canned_reply.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.181477, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 53.22, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'canned_reply.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["canned_reply.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1830618, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 53.722, "width_percent": 0.558}, {"sql": "select exists(select * from `permissions` where (`name` = 'connect_account.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["connect_account.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.184544, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 54.279, "width_percent": 0.488}, {"sql": "select exists(select * from `permissions` where (`name` = 'connect_account.connect' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["connect_account.connect", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.186285, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 54.767, "width_percent": 0.516}, {"sql": "select exists(select * from `permissions` where (`name` = 'connect_account.disconnect' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["connect_account.disconnect", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.187677, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 55.283, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'message_bot.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["message_bot.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.18904, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 55.785, "width_percent": 0.516}, {"sql": "select exists(select * from `permissions` where (`name` = 'message_bot.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["message_bot.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1903539, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 56.301, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'message_bot.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["message_bot.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.191783, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 56.802, "width_percent": 0.516}, {"sql": "select exists(select * from `permissions` where (`name` = 'message_bot.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["message_bot.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.193167, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 57.318, "width_percent": 2.021}, {"sql": "select exists(select * from `permissions` where (`name` = 'message_bot.clone' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["message_bot.clone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.195731, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 59.339, "width_percent": 0.711}, {"sql": "select exists(select * from `permissions` where (`name` = 'template_bot.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["template_bot.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.197219, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 60.05, "width_percent": 0.572}, {"sql": "select exists(select * from `permissions` where (`name` = 'template_bot.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["template_bot.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.198608, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 60.622, "width_percent": 0.544}, {"sql": "select exists(select * from `permissions` where (`name` = 'template_bot.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["template_bot.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.199962, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 61.165, "width_percent": 0.544}, {"sql": "select exists(select * from `permissions` where (`name` = 'template_bot.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["template_bot.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.201334, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 61.709, "width_percent": 0.53}, {"sql": "select exists(select * from `permissions` where (`name` = 'template_bot.clone' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["template_bot.clone", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2030501, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 62.239, "width_percent": 0.906}, {"sql": "select exists(select * from `permissions` where (`name` = 'template.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["template.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.205233, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 63.145, "width_percent": 0.627}, {"sql": "select exists(select * from `permissions` where (`name` = 'template.load_template' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["template.load_template", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.206793, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 63.772, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'campaigns.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["campaigns.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.208271, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 64.274, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'campaigns.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["campaigns.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.209723, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 64.776, "width_percent": 0.516}, {"sql": "select exists(select * from `permissions` where (`name` = 'campaigns.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["campaigns.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2111769, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 65.291, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'campaigns.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["campaigns.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.212758, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 65.793, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'campaigns.show_campaign' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["campaigns.show_campaign", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.214225, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 66.295, "width_percent": 0.781}, {"sql": "select exists(select * from `permissions` where (`name` = 'chat.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["chat.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.215754, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 67.076, "width_percent": 0.544}, {"sql": "select exists(select * from `permissions` where (`name` = 'chat.read_only' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["chat.read_only", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.217085, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 67.619, "width_percent": 0.516}, {"sql": "select exists(select * from `permissions` where (`name` = 'activity_log.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["activity_log.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.218425, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 68.135, "width_percent": 0.446}, {"sql": "select exists(select * from `permissions` where (`name` = 'activity_log.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["activity_log.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.220517, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 68.581, "width_percent": 1.255}, {"sql": "select exists(select * from `permissions` where (`name` = 'whatsmark_settings.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["whatsmark_settings.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.222585, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 69.836, "width_percent": 0.488}, {"sql": "select exists(select * from `permissions` where (`name` = 'whatsmark_settings.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["whatsmark_settings.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.223905, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 70.323, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'bulk_campaigns.send' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["bulk_campaigns.send", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.22523, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 70.825, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'role.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["role.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.226569, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 71.285, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'role.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["role.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.227853, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 71.745, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'role.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["role.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.229398, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 72.205, "width_percent": 1.045}, {"sql": "select exists(select * from `permissions` where (`name` = 'role.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["role.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.231173, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 73.251, "width_percent": 0.502}, {"sql": "select exists(select * from `permissions` where (`name` = 'status.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["status.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.232678, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 73.752, "width_percent": 0.53}, {"sql": "select exists(select * from `permissions` where (`name` = 'status.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["status.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2340312, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 74.282, "width_percent": 0.613}, {"sql": "select exists(select * from `permissions` where (`name` = 'status.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["status.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.235661, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 74.895, "width_percent": 0.697}, {"sql": "select exists(select * from `permissions` where (`name` = 'status.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["status.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.237381, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 75.592, "width_percent": 0.585}, {"sql": "select exists(select * from `permissions` where (`name` = 'contact.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["contact.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.238781, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 76.178, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'contact.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["contact.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.240112, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 76.638, "width_percent": 0.446}, {"sql": "select exists(select * from `permissions` where (`name` = 'contact.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["contact.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.241463, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 77.084, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'contact.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["contact.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2427871, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 77.544, "width_percent": 0.39}, {"sql": "select exists(select * from `permissions` where (`name` = 'contact.bulk_import' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["contact.bulk_import", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.244052, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 77.934, "width_percent": 0.599}, {"sql": "select exists(select * from `permissions` where (`name` = 'system_settings.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["system_settings.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.245593, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 78.534, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'system_settings.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["system_settings.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.24701, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 78.994, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'user.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["user.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.248428, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 79.454, "width_percent": 0.446}, {"sql": "select exists(select * from `permissions` where (`name` = 'user.create' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["user.create", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2498481, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 79.9, "width_percent": 0.446}, {"sql": "select exists(select * from `permissions` where (`name` = 'user.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["user.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.251322, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 80.346, "width_percent": 1.018}, {"sql": "select exists(select * from `permissions` where (`name` = 'user.delete' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["user.delete", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.253503, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 81.363, "width_percent": 0.474}, {"sql": "select exists(select * from `permissions` where (`name` = 'email_template.view' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["email_template.view", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.254931, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 81.837, "width_percent": 0.46}, {"sql": "select exists(select * from `permissions` where (`name` = 'email_template.edit' and `guard_name` = 'web')) as `exists`", "type": "query", "params": [], "bindings": ["email_template.edit", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.25638, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PermissionSeeder.php:98", "source": {"index": 16, "namespace": null, "name": "database/seeders/PermissionSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\PermissionSeeder.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FPermissionSeeder.php&line=98", "ajax": false, "filename": "PermissionSeeder.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 82.297, "width_percent": 0.46}, {"sql": "select exists(select * from `email_templates` where (`slug` = 'smtp-test-mail')) as `exists`", "type": "query", "params": [], "bindings": ["smtp-test-mail"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.260444, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 82.757, "width_percent": 1.575}, {"sql": "update `email_templates` set `name` = 'SMTP Test Mail', `subject` = 'SMTP Test Mail', `slug` = 'smtp-test-mail', `message` = '<p>This is a test email to confirm that your SMTP settings are correctly configured.</p><p>If you have received this email, your SMTP setup is working properly.</p><p>If you did not request this test or face any issues, please review your SMTP configuration or contact support at <a href=\\\"mailto:{company_email}\\\">{company_email}</a>.</p>', `is_active` = 1, `merge_fields_groups` = '[\\\"other-group\\\"]', `created_at` = '2025-07-13 02:06:22', `updated_at` = '2025-07-13 02:06:22' where (`slug` = 'smtp-test-mail') limit 1", "type": "query", "params": [], "bindings": ["SMTP Test Mail", "SMTP Test Mail", "smtp-test-mail", "<p>This is a test email to confirm that your SMTP settings are correctly configured.</p><p>If you have received this email, your SMTP setup is working properly.</p><p>If you did not request this test or face any issues, please review your SMTP configuration or contact support at <a href=\"mailto:{company_email}\">{company_email}</a>.</p>", 1, "[\"other-group\"]", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "smtp-test-mail"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.262994, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 84.332, "width_percent": 2.662}, {"sql": "select exists(select * from `email_templates` where (`slug` = 'email-confirmation')) as `exists`", "type": "query", "params": [], "bindings": ["email-confirmation"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.265999, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 86.995, "width_percent": 0.39}, {"sql": "update `email_templates` set `name` = 'Email Confirmation', `subject` = 'Email Confirmation', `slug` = 'email-confirmation', `message` = '<p>Thank you for signing up with {site_name}, {first_name} {last_name}!</p><p>We\\'re thrilled to have you on board. Before you get started, we need to verify your email address to ensure the security of your account.</p><p>Please click the button below to verify your email:</p>', `is_active` = 1, `merge_fields_groups` = '[\\\"other-group\\\",\\\"user-group\\\"]', `created_at` = '2025-07-13 02:06:22', `updated_at` = '2025-07-13 02:06:22' where (`slug` = 'email-confirmation') limit 1", "type": "query", "params": [], "bindings": ["Email Confirmation", "Email Confirmation", "email-confirmation", "<p>Thank you for signing up with {site_name}, {first_name} {last_name}!</p><p>We're thrilled to have you on board. Before you get started, we need to verify your email address to ensure the security of your account.</p><p>Please click the button below to verify your email:</p>", 1, "[\"other-group\",\"user-group\"]", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "email-confirmation"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.267359, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 87.385, "width_percent": 2.188}, {"sql": "select exists(select * from `email_templates` where (`slug` = 'welcome-mail')) as `exists`", "type": "query", "params": [], "bindings": ["welcome-mail"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.270807, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 89.573, "width_percent": 0.948}, {"sql": "update `email_templates` set `name` = 'Welcome Email', `subject` = 'Welcome to {site_name}!', `slug` = 'welcome-mail', `message` = '<p>Dear {first_name} {last_name},</p><p>Welcome to {site_name}! We\\'re excited to have you on board. 🚀</p><p>Get ready to explore our amazing features and make your life easier.</p><p>If you have any questions, our support team at <a href=\\\"mailto:{company_email}\\\">{company_email}</a> is always here to help.</p><p>Start your journey here: <a href=\\\"{base_url}\\\">{base_url}</a></p><p>Looking forward to seeing you thrive!</p>', `is_active` = 1, `merge_fields_groups` = '[\\\"other-group\\\",\\\"user-group\\\"]', `created_at` = '2025-07-13 02:06:22', `updated_at` = '2025-07-13 02:06:22' where (`slug` = 'welcome-mail') limit 1", "type": "query", "params": [], "bindings": ["Welcome Email", "Welcome to {site_name}!", "welcome-mail", "<p>Dear {first_name} {last_name},</p><p>Welcome to {site_name}! We're excited to have you on board. 🚀</p><p>Get ready to explore our amazing features and make your life easier.</p><p>If you have any questions, our support team at <a href=\"mailto:{company_email}\">{company_email}</a> is always here to help.</p><p>Start your journey here: <a href=\"{base_url}\">{base_url}</a></p><p>Looking forward to seeing you thrive!</p>", 1, "[\"other-group\",\"user-group\"]", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "welcome-mail"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.27282, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 90.521, "width_percent": 2.565}, {"sql": "select exists(select * from `email_templates` where (`slug` = 'password-reset')) as `exists`", "type": "query", "params": [], "bindings": ["password-reset"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.276539, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 93.086, "width_percent": 0.641}, {"sql": "update `email_templates` set `name` = 'Password Reset', `subject` = 'Password Reset Request', `slug` = 'password-reset', `message` = '<p>Hello {first_name} {last_name},</p><p>We received a request to reset your password for your {site_name} account.</p><p>If you made this request, click the button below to reset your password:</p><p><a href=\\\"{reset_link}\\\">Reset Password</a></p><p>If you did not request a password reset, please ignore this email or contact support at <a href=\\\"mailto:{company_email}\\\">{company_email}</a>.</p>', `is_active` = 1, `merge_fields_groups` = '[\\\"other-group\\\",\\\"user-group\\\"]', `created_at` = '2025-07-13 02:06:22', `updated_at` = '2025-07-13 02:06:22' where (`slug` = 'password-reset') limit 1", "type": "query", "params": [], "bindings": ["Password Reset", "Password Reset Request", "password-reset", "<p>Hello {first_name} {last_name},</p><p>We received a request to reset your password for your {site_name} account.</p><p>If you made this request, click the button below to reset your password:</p><p><a href=\"{reset_link}\">Reset Password</a></p><p>If you did not request a password reset, please ignore this email or contact support at <a href=\"mailto:{company_email}\">{company_email}</a>.</p>", 1, "[\"other-group\",\"user-group\"]", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "password-reset"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.278296, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 93.727, "width_percent": 2.23}, {"sql": "select exists(select * from `email_templates` where (`slug` = 'new-contact-assigned')) as `exists`", "type": "query", "params": [], "bindings": ["new-contact-assigned"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.280985, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 11, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 95.958, "width_percent": 0.376}, {"sql": "update `email_templates` set `name` = 'New Contact Assigned', `subject` = '📌 New Contact Assigned to You', `slug` = 'new-contact-assigned', `message` = '<p>Hi {first_name} {last_name},</p><p>A new contact has been assigned to you. Here are the details:</p><ul><li><strong>Contact Name:</strong> {contact_first_name} {contact_last_name}</li><li><strong>Email:</strong> {contact_email}</li><li><strong>Phone:</strong> {contact_phone_number}</li><li><strong>Assigned By:</strong> {assigned_by}</li></ul><p>Please reach out to them promptly and ensure a smooth follow-up.</p><p>If you have any questions, feel free to get in touch.</p><p><strong>Best regards,</strong><br>{site_name}</p>', `is_active` = 1, `merge_fields_groups` = '[\\\"other-group\\\",\\\"user-group\\\",\\\"contact-group\\\"]', `created_at` = '2025-07-13 02:06:22', `updated_at` = '2025-07-13 02:06:22' where (`slug` = 'new-contact-assigned') limit 1", "type": "query", "params": [], "bindings": ["New Contact Assigned", "📌 New Contact Assigned to You", "new-contact-assigned", "<p>Hi {first_name} {last_name},</p><p>A new contact has been assigned to you. Here are the details:</p><ul><li><strong>Contact Name:</strong> {contact_first_name} {contact_last_name}</li><li><strong>Email:</strong> {contact_email}</li><li><strong>Phone:</strong> {contact_phone_number}</li><li><strong>Assigned By:</strong> {assigned_by}</li></ul><p>Please reach out to them promptly and ensure a smooth follow-up.</p><p>If you have any questions, feel free to get in touch.</p><p><strong>Best regards,</strong><br>{site_name}</p>", 1, "[\"other-group\",\"user-group\",\"contact-group\"]", "2025-07-13 02:06:22", "2025-07-13 02:06:22", "new-contact-assigned"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.28243, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EmailTemplatesSeeder.php:60", "source": {"index": 12, "namespace": null, "name": "database/seeders/EmailTemplatesSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\EmailTemplatesSeeder.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FEmailTemplatesSeeder.php&line=60", "ajax": false, "filename": "EmailTemplatesSeeder.php", "line": "60"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 96.334, "width_percent": 2.175}, {"sql": "select exists(select * from `languages` where `code` = 'en') as `exists`", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "database/seeders/LanguageSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\LanguageSeeder.php", "line": 15}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2875829, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "LanguageSeeder.php:15", "source": {"index": 10, "namespace": null, "name": "database/seeders/LanguageSeeder.php", "file": "C:\\laragon\\www\\whats\\database\\seeders\\LanguageSeeder.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fdatabase%2Fseeders%2FLanguageSeeder.php&line=15", "ajax": false, "filename": "LanguageSeeder.php", "line": "15"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 98.509, "width_percent": 1.491}]}, "models": {"data": {"Spatie\\LaravelSettings\\Models\\SettingsProperty": {"value": 68, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FModels%2FSettingsProperty.php&line=1", "ajax": false, "filename": "SettingsProperty.php", "line": "?"}}, "App\\Models\\Status": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FStatus.php&line=1", "ajax": false, "filename": "Status.php", "line": "?"}}, "App\\Models\\Source": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FSource.php&line=1", "ajax": false, "filename": "Source.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 77, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/install/user", "action_name": "install.user.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@userStore", "uri": "POST install/user", "controller": "Corbital\\Installer\\Http\\Controllers\\InstallController@userStore<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=177\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "install", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=177\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/corbital/installer/src/Http/Controllers/InstallController.php:177-212</a>", "middleware": "web, Corbital\\Installer\\Http\\Middleware\\CanInstall", "duration": "2.26s", "peak_memory": "48MB", "response": "Redirect to http://127.0.0.1:8000/install/finished", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1591852751 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1591852751\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2001322421 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoBgGDbe9s7gwviWjdHmUv9OfBIz8igqahoGVUk4</span>\"\n  \"<span class=sf-dump-key>firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>password_confirmation</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001322421\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-100618075 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">186</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/install/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkJUYzFPV1ZUK1hsTEZTQVA1VjIrZ2c9PSIsInZhbHVlIjoiQWxoL3I5NEw5TVR2VzJVcjltWHVHUk9FOHZza24xUDBSdjAwc1lKdnkwWlhEbzVEK1ZmdmtHeWFZRXBiOGFQZTg0eHlBcWdQOC9FVnBpQ2F1aUxyUXpDdG1GakZ4TVBGaWpkeElRVFJPS1Jhcy9kL0wyWTlleVJqdEZHblBIbmsiLCJtYWMiOiI2YjIzMTMxODhjNDlhODZhMzk5M2Y0NGViNDg4N2I4Zjk1ZjE0NzQ0NDYwMGFlYTgzZjdlZTk4ZDU1NmIyNGM1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ingybmo0eEFyRUhsWWRhVkxGdmhLanc9PSIsInZhbHVlIjoiemdvUmdoMG0rNU8zT0cxczFJOTlyK1lqT3h1UnN3ZzFEYzFNR2p2bmdlY0dCeHFySG96dDkzdW1ZT0s4Y1EyUG0yVHZidTZNYUNWRUtzOXRBcFpjNVlxNFdITUNRTkVpYVRaeFMrZ09SYmRHblhPdGgrRUJuSFdGcjI1R2lBZ2giLCJtYWMiOiJjYTY0MzUzZTdkZWI4OWZkZTg4Njc4OWEwMGFjOGJlMWY3MmE4OTMwZTdiNzU0NzFlMDljYzdiMGUyYWRiMmRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100618075\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-743786089 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoBgGDbe9s7gwviWjdHmUv9OfBIz8igqahoGVUk4</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9oefF5mPoeYxbiKHXQcHAQVPigwxNXWD1CRqBPR2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-743786089\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-466141277 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:06:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/install/finished</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466141277\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1959571500 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MoBgGDbe9s7gwviWjdHmUv9OfBIz8igqahoGVUk4</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/install</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">user</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>user</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#1330</a><samp data-depth=2 class=sf-dump-compact>\n    #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n    +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n    #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n    +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n    +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n      \"<span class=sf-dump-key>lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n      \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>default_language</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>profile_image_url</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-13 02:06:21</span>\"\n      \"<span class=sf-dump-key>last_login</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$E2/8HWmbhoJMka/IJFpRDeb3b5ZNyQ/ao1gKhrPpYTCE2M14EhhhO</span>\"\n      \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>is_admin</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>send_welcome_mail</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-12 16:15:17</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-13 02:06:22</span>\"\n      \"<span class=sf-dump-key>banned_at</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n      \"<span class=sf-dump-key>lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n      \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>default_language</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>profile_image_url</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-13 02:06:21</span>\"\n      \"<span class=sf-dump-key>last_login</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$E2/8HWmbhoJMka/IJFpRDeb3b5ZNyQ/ao1gKhrPpYTCE2M14EhhhO</span>\"\n      \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>is_admin</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>send_welcome_mail</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>active</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>remember_token</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-12 16:15:17</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-13 02:06:22</span>\"\n      \"<span class=sf-dump-key>banned_at</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">changes</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n      \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-13 02:06:21</span>\"\n      \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$E2/8HWmbhoJMka/IJFpRDeb3b5ZNyQ/ao1gKhrPpYTCE2M14EhhhO</span>\"\n      \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-13 02:06:22</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>email_verified_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n      \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">hashed</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n    #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n    +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n    +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n    #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n    #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">firstname</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">lastname</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"16 characters\">default_language</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"17 characters\">profile_image_url</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"7 characters\">role_id</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"8 characters\">is_admin</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"17 characters\">email_verified_at</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n    </samp>]\n    #<span class=sf-dump-protected title=\"Protected property\">authPasswordName</span>: \"<span class=sf-dump-str title=\"8 characters\">password</span>\"\n    #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>\n    -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardPermissionsIndex</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n  </samp>}\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959571500\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/install/user", "action_name": "install.user.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@userStore"}, "badge": "302 Found"}}