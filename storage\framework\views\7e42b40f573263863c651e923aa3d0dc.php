<!DOCTYPE html>
<html
  lang="<?php echo e(Auth::user() ? Session::get('locale', config('app.locale')) : get_setting('general.active_language')); ?>"
  class="h-full" x-data="{ theme: $persist('light') }"
  x-bind:class="{
      'dark': theme === 'dark' || (theme === 'system' && window.matchMedia(
              '(prefers-color-scheme: dark)')
          .matches)
  }">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- CSRF Token -->
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

  <title>
    <?php echo e((!empty(get_setting('seo.meta_title')) ? get_setting('seo.meta_title') : 'WhatsMark') . (isset($title) ? ' - ' . $title : '')); ?>

  </title>

  <meta name="description" content="<?php echo e(get_setting('seo.meta_description') ?? 'WhatsMark'); ?>" />

  <!-- Favicon -->
  <link rel="icon" type="image/png" sizes="16x16"
    href="<?php echo e(get_setting('general.favicon') ? Storage::url(get_setting('general.favicon')) : url('./img/favicon-16x16.png')); ?>">
  <link rel="icon" type="image/png" sizes="32x32"
    href="<?php echo e(get_setting('general.favicon') ? Storage::url(get_setting('general.favicon')) : url('./img/favicon-32x32.png')); ?>">
  <link rel="icon" type="image/png" sizes="192x192"
    href="<?php echo e(get_setting('general.favicon') ? Storage::url(get_setting('general.favicon')) : url('./img/favicon.png')); ?>">
  <link rel="apple-touch-icon"
    href="<?php echo e(get_setting('general.favicon') ? Storage::url(get_setting('general.favicon')) : url('./img/apple-touch-icon.png')); ?>">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Lexend:wght@100..900&display=swap">

  <!-- Styles -->

  <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

  <?php echo app('Illuminate\Foundation\Vite')('resources/css/app.css'); ?>
  <script>
    window.pusherConfig = {
      key: '<?php echo e(get_setting('pusher.app_key')); ?>',
      cluster: '<?php echo e(get_setting('pusher.cluster')); ?>',
      notification_enabled: <?php echo e(get_setting('pusher.real_time_notify') ? 'true' : 'false'); ?>,
      desktop_notification: <?php echo e(get_setting('pusher.desk_notify') ? 'true' : 'false'); ?>,
      auto_dismiss_notification: <?php echo e(!empty(get_setting('pusher.dismiss_desk_notification')) ? get_setting('pusher.dismiss_desk_notification') : 0); ?>

    };

    // Make date/time settings available to JavaScript
    window.dateTimeSettings = <?php echo json_encode($dateTimeSettings, 15, 512) ?>;
    var date_format = window.dateTimeSettings.dateFormat;
    var is24Hour = window.dateTimeSettings.is24Hour;
    var time_format = window.dateTimeSettings.is24Hour ? 'h:i' : 'h:i K';
  </script>
</head>

<body class="h-full antialiased bg-gray-50 font-sans dark:bg-slate-800" x-data="{ theme: $persist('light') }"
  x-init="if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      document.documentElement.classList.add('dark');
  } else {
      document.documentElement.classList.remove('dark');
  }">

  <div id="main" x-data="{ open: false }" @keydown.window.escape="open = false"
    class="min-h-full flex">

    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.sidebar-navigation', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2660820162-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    <div class="lg:pl-[15rem] flex flex-col w-0 flex-1">
      <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.header-navigation', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2660820162-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
      <main class="flex-1 overflow-x-hidden">
        <?php if(request()->routeIs('admin.chat')): ?>
          <?php echo $__env->yieldContent('chat'); ?>
        <?php else: ?>
          <div class=" <?php echo e(request()->routeIs('admin.chat') ? 'p-2' : 'py-6 px-2 md:p-6'); ?>">
            <?php echo e($slot); ?>


          </div>
        <?php endif; ?>

      </main>
      <?php if (isset($component)) { $__componentOriginal0d8d3c14ebd2b92d484be47e6c018839 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0d8d3c14ebd2b92d484be47e6c018839 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.notification','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('notification'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0d8d3c14ebd2b92d484be47e6c018839)): ?>
<?php $attributes = $__attributesOriginal0d8d3c14ebd2b92d484be47e6c018839; ?>
<?php unset($__attributesOriginal0d8d3c14ebd2b92d484be47e6c018839); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0d8d3c14ebd2b92d484be47e6c018839)): ?>
<?php $component = $__componentOriginal0d8d3c14ebd2b92d484be47e6c018839; ?>
<?php unset($__componentOriginal0d8d3c14ebd2b92d484be47e6c018839); ?>
<?php endif; ?>
    </div>

    <!-- Scripts -->
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


    <?php echo app('Illuminate\Foundation\Vite')('resources/js/app.js'); ?>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>

</html>
<?php /**PATH C:\laragon\www\whats\resources\views/layouts/app.blade.php ENDPATH**/ ?>