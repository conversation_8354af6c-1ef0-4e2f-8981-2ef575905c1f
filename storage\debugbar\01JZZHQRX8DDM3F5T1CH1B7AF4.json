{"__meta": {"id": "01JZZHQRX8DDM3F5T1CH1B7AF4", "datetime": "2025-07-12 04:48:27", "utime": **********.304912, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331706.636439, "end": **********.304922, "duration": 0.6684830188751221, "duration_str": "668ms", "measures": [{"label": "Booting", "start": 1752331706.636439, "relative_start": 0, "end": **********.161071, "relative_end": **********.161071, "duration": 0.****************, "duration_str": "525ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.161079, "relative_start": 0.****************, "end": **********.304924, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "144ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.174211, "relative_start": 0.****************, "end": **********.176164, "relative_end": **********.176164, "duration": 0.0019528865814208984, "duration_str": "1.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.settings.system.system-update-settings", "start": **********.240449, "relative_start": 0.****************, "end": **********.240449, "relative_end": **********.240449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.settings-heading", "start": **********.253367, "relative_start": 0.****************, "end": **********.253367, "relative_end": **********.253367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.admin-system-settings-navigation", "start": **********.253808, "relative_start": 0.6173689365386963, "end": **********.253808, "relative_end": **********.253808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3884f14aaba57a4f386448d1a7c68e96", "start": **********.257527, "relative_start": 0.6210880279541016, "end": **********.257527, "relative_end": **********.257527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.25891, "relative_start": 0.6224708557128906, "end": **********.25891, "relative_end": **********.25891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.259836, "relative_start": 0.6233968734741211, "end": **********.259836, "relative_end": **********.259836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.260622, "relative_start": 0.624182939529419, "end": **********.260622, "relative_end": **********.260622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.261505, "relative_start": 0.625065803527832, "end": **********.261505, "relative_end": **********.261505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.262255, "relative_start": 0.6258158683776855, "end": **********.262255, "relative_end": **********.262255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.263021, "relative_start": 0.6265819072723389, "end": **********.263021, "relative_end": **********.263021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.263786, "relative_start": 0.6273469924926758, "end": **********.263786, "relative_end": **********.263786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.264559, "relative_start": 0.628119945526123, "end": **********.264559, "relative_end": **********.264559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.265355, "relative_start": 0.6289160251617432, "end": **********.265355, "relative_end": **********.265355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.26612, "relative_start": 0.629680871963501, "end": **********.26612, "relative_end": **********.26612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.266882, "relative_start": 0.6304428577423096, "end": **********.266882, "relative_end": **********.266882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.267647, "relative_start": 0.6312079429626465, "end": **********.267647, "relative_end": **********.267647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.267903, "relative_start": 0.6314640045166016, "end": **********.267903, "relative_end": **********.267903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::22c5497599a0281ab25c8247b437d302", "start": **********.269082, "relative_start": 0.6326429843902588, "end": **********.269082, "relative_end": **********.269082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a432816b85ee65ed60368f9a3c94be48", "start": **********.270503, "relative_start": 0.6340639591217041, "end": **********.270503, "relative_end": **********.270503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46f5938c81ab183386dac5ba8284fe95", "start": **********.272135, "relative_start": 0.6356959342956543, "end": **********.272135, "relative_end": **********.272135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ce224f6c6bbe814e8fd6f78c6703ba06", "start": **********.273659, "relative_start": 0.6372199058532715, "end": **********.273659, "relative_end": **********.273659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1165f1d1cef30b0a01dd0d4b054072c1", "start": **********.275117, "relative_start": 0.6386778354644775, "end": **********.275117, "relative_end": **********.275117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e921a1eb2ea111d1003732b3951f1bd9", "start": **********.276588, "relative_start": 0.6401488780975342, "end": **********.276588, "relative_end": **********.276588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b402eeba76379a019857e2a4f3f270ea", "start": **********.278088, "relative_start": 0.6416490077972412, "end": **********.278088, "relative_end": **********.278088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b72a2d28d61b467c04005109a3e72ee0", "start": **********.279548, "relative_start": 0.6431088447570801, "end": **********.279548, "relative_end": **********.279548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::01ada89b1421c14d3d3bfdef1871a2a7", "start": **********.28105, "relative_start": 0.6446108818054199, "end": **********.28105, "relative_end": **********.28105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d6b2f7c1dc9e7fcc55a90d9e555aa73", "start": **********.282502, "relative_start": 0.6460628509521484, "end": **********.282502, "relative_end": **********.282502, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4439815e9fdb154c1dfeba18fb973a1b", "start": **********.284098, "relative_start": 0.6476588249206543, "end": **********.284098, "relative_end": **********.284098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::78f47a3fc9978bef41f40fdb7eb82ff0", "start": **********.285506, "relative_start": 0.6490669250488281, "end": **********.285506, "relative_end": **********.285506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.settings-heading", "start": **********.286817, "relative_start": 0.6503779888153076, "end": **********.286817, "relative_end": **********.286817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.settings-description", "start": **********.287592, "relative_start": 0.6511528491973877, "end": **********.287592, "relative_end": **********.287592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e3f7297abf85d8b4fa0b27d9f588b96", "start": **********.289477, "relative_start": 0.6530380249023438, "end": **********.289477, "relative_end": **********.289477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d231bde2c50418020678628f9ecb5e5e", "start": **********.29035, "relative_start": 0.6539108753204346, "end": **********.29035, "relative_end": **********.29035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6ec1be0947a4b87477d5e2dc1a19cf1b", "start": **********.292693, "relative_start": 0.****************, "end": **********.292693, "relative_end": **********.292693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.299786, "relative_start": 0.6633470058441162, "end": **********.299786, "relative_end": **********.299786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.301991, "relative_start": 0.6655519008636475, "end": **********.303092, "relative_end": **********.303092, "duration": 0.0011010169982910156, "duration_str": "1.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 37777032, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 35, "nb_templates": 35, "templates": [{"name": "livewire.admin.settings.system.system-update-settings", "param_count": null, "params": [], "start": **********.24041, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/settings/system/system-update-settings.blade.phplivewire.admin.settings.system.system-update-settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fsettings%2Fsystem%2Fsystem-update-settings.blade.php&line=1", "ajax": false, "filename": "system-update-settings.blade.php", "line": "?"}}, {"name": "components.settings-heading", "param_count": null, "params": [], "start": **********.253333, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/settings-heading.blade.phpcomponents.settings-heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fsettings-heading.blade.php&line=1", "ajax": false, "filename": "settings-heading.blade.php", "line": "?"}}, {"name": "components.admin-system-settings-navigation", "param_count": null, "params": [], "start": **********.253777, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/admin-system-settings-navigation.blade.phpcomponents.admin-system-settings-navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fadmin-system-settings-navigation.blade.php&line=1", "ajax": false, "filename": "admin-system-settings-navigation.blade.php", "line": "?"}}, {"name": "__components::3884f14aaba57a4f386448d1a7c68e96", "param_count": null, "params": [], "start": **********.257493, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/3884f14aaba57a4f386448d1a7c68e96.blade.php__components::3884f14aaba57a4f386448d1a7c68e96", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F3884f14aaba57a4f386448d1a7c68e96.blade.php&line=1", "ajax": false, "filename": "3884f14aaba57a4f386448d1a7c68e96.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.258876, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.259803, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.260589, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.261437, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.262221, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.262988, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.263753, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.264508, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.265323, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.266087, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.26685, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown-link", "param_count": null, "params": [], "start": **********.267614, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}}, {"name": "components.dropdown", "param_count": null, "params": [], "start": **********.267871, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown.blade.phpcomponents.dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}}, {"name": "__components::22c5497599a0281ab25c8247b437d302", "param_count": null, "params": [], "start": **********.26905, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/22c5497599a0281ab25c8247b437d302.blade.php__components::22c5497599a0281ab25c8247b437d302", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F22c5497599a0281ab25c8247b437d302.blade.php&line=1", "ajax": false, "filename": "22c5497599a0281ab25c8247b437d302.blade.php", "line": "?"}}, {"name": "__components::a432816b85ee65ed60368f9a3c94be48", "param_count": null, "params": [], "start": **********.270451, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/a432816b85ee65ed60368f9a3c94be48.blade.php__components::a432816b85ee65ed60368f9a3c94be48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fa432816b85ee65ed60368f9a3c94be48.blade.php&line=1", "ajax": false, "filename": "a432816b85ee65ed60368f9a3c94be48.blade.php", "line": "?"}}, {"name": "__components::46f5938c81ab183386dac5ba8284fe95", "param_count": null, "params": [], "start": **********.272104, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/46f5938c81ab183386dac5ba8284fe95.blade.php__components::46f5938c81ab183386dac5ba8284fe95", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F46f5938c81ab183386dac5ba8284fe95.blade.php&line=1", "ajax": false, "filename": "46f5938c81ab183386dac5ba8284fe95.blade.php", "line": "?"}}, {"name": "__components::ce224f6c6bbe814e8fd6f78c6703ba06", "param_count": null, "params": [], "start": **********.273628, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ce224f6c6bbe814e8fd6f78c6703ba06.blade.php__components::ce224f6c6bbe814e8fd6f78c6703ba06", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fce224f6c6bbe814e8fd6f78c6703ba06.blade.php&line=1", "ajax": false, "filename": "ce224f6c6bbe814e8fd6f78c6703ba06.blade.php", "line": "?"}}, {"name": "__components::1165f1d1cef30b0a01dd0d4b054072c1", "param_count": null, "params": [], "start": **********.275084, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/1165f1d1cef30b0a01dd0d4b054072c1.blade.php__components::1165f1d1cef30b0a01dd0d4b054072c1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F1165f1d1cef30b0a01dd0d4b054072c1.blade.php&line=1", "ajax": false, "filename": "1165f1d1cef30b0a01dd0d4b054072c1.blade.php", "line": "?"}}, {"name": "__components::e921a1eb2ea111d1003732b3951f1bd9", "param_count": null, "params": [], "start": **********.276556, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/e921a1eb2ea111d1003732b3951f1bd9.blade.php__components::e921a1eb2ea111d1003732b3951f1bd9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fe921a1eb2ea111d1003732b3951f1bd9.blade.php&line=1", "ajax": false, "filename": "e921a1eb2ea111d1003732b3951f1bd9.blade.php", "line": "?"}}, {"name": "__components::b402eeba76379a019857e2a4f3f270ea", "param_count": null, "params": [], "start": **********.278053, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b402eeba76379a019857e2a4f3f270ea.blade.php__components::b402eeba76379a019857e2a4f3f270ea", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb402eeba76379a019857e2a4f3f270ea.blade.php&line=1", "ajax": false, "filename": "b402eeba76379a019857e2a4f3f270ea.blade.php", "line": "?"}}, {"name": "__components::b72a2d28d61b467c04005109a3e72ee0", "param_count": null, "params": [], "start": **********.279516, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b72a2d28d61b467c04005109a3e72ee0.blade.php__components::b72a2d28d61b467c04005109a3e72ee0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb72a2d28d61b467c04005109a3e72ee0.blade.php&line=1", "ajax": false, "filename": "b72a2d28d61b467c04005109a3e72ee0.blade.php", "line": "?"}}, {"name": "__components::01ada89b1421c14d3d3bfdef1871a2a7", "param_count": null, "params": [], "start": **********.281018, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/01ada89b1421c14d3d3bfdef1871a2a7.blade.php__components::01ada89b1421c14d3d3bfdef1871a2a7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F01ada89b1421c14d3d3bfdef1871a2a7.blade.php&line=1", "ajax": false, "filename": "01ada89b1421c14d3d3bfdef1871a2a7.blade.php", "line": "?"}}, {"name": "__components::0d6b2f7c1dc9e7fcc55a90d9e555aa73", "param_count": null, "params": [], "start": **********.28247, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/0d6b2f7c1dc9e7fcc55a90d9e555aa73.blade.php__components::0d6b2f7c1dc9e7fcc55a90d9e555aa73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F0d6b2f7c1dc9e7fcc55a90d9e555aa73.blade.php&line=1", "ajax": false, "filename": "0d6b2f7c1dc9e7fcc55a90d9e555aa73.blade.php", "line": "?"}}, {"name": "__components::4439815e9fdb154c1dfeba18fb973a1b", "param_count": null, "params": [], "start": **********.284066, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4439815e9fdb154c1dfeba18fb973a1b.blade.php__components::4439815e9fdb154c1dfeba18fb973a1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4439815e9fdb154c1dfeba18fb973a1b.blade.php&line=1", "ajax": false, "filename": "4439815e9fdb154c1dfeba18fb973a1b.blade.php", "line": "?"}}, {"name": "__components::78f47a3fc9978bef41f40fdb7eb82ff0", "param_count": null, "params": [], "start": **********.285473, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/78f47a3fc9978bef41f40fdb7eb82ff0.blade.php__components::78f47a3fc9978bef41f40fdb7eb82ff0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F78f47a3fc9978bef41f40fdb7eb82ff0.blade.php&line=1", "ajax": false, "filename": "78f47a3fc9978bef41f40fdb7eb82ff0.blade.php", "line": "?"}}, {"name": "components.settings-heading", "param_count": null, "params": [], "start": **********.286783, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/settings-heading.blade.phpcomponents.settings-heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fsettings-heading.blade.php&line=1", "ajax": false, "filename": "settings-heading.blade.php", "line": "?"}}, {"name": "components.settings-description", "param_count": null, "params": [], "start": **********.287559, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/settings-description.blade.phpcomponents.settings-description", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fsettings-description.blade.php&line=1", "ajax": false, "filename": "settings-description.blade.php", "line": "?"}}, {"name": "__components::9e3f7297abf85d8b4fa0b27d9f588b96", "param_count": null, "params": [], "start": **********.289444, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/9e3f7297abf85d8b4fa0b27d9f588b96.blade.php__components::9e3f7297abf85d8b4fa0b27d9f588b96", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F9e3f7297abf85d8b4fa0b27d9f588b96.blade.php&line=1", "ajax": false, "filename": "9e3f7297abf85d8b4fa0b27d9f588b96.blade.php", "line": "?"}}, {"name": "__components::d231bde2c50418020678628f9ecb5e5e", "param_count": null, "params": [], "start": **********.290318, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/d231bde2c50418020678628f9ecb5e5e.blade.php__components::d231bde2c50418020678628f9ecb5e5e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fd231bde2c50418020678628f9ecb5e5e.blade.php&line=1", "ajax": false, "filename": "d231bde2c50418020678628f9ecb5e5e.blade.php", "line": "?"}}, {"name": "__components::6ec1be0947a4b87477d5e2dc1a19cf1b", "param_count": null, "params": [], "start": **********.292661, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6ec1be0947a4b87477d5e2dc1a19cf1b.blade.php__components::6ec1be0947a4b87477d5e2dc1a19cf1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6ec1be0947a4b87477d5e2dc1a19cf1b.blade.php&line=1", "ajax": false, "filename": "6ec1be0947a4b87477d5e2dc1a19cf1b.blade.php", "line": "?"}}, {"name": "components.card", "param_count": null, "params": [], "start": **********.299753, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0013, "accumulated_duration_str": "1.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.178241, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 47.692}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 20, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 318}, {"index": 23, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.248089, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 47.692, "width_percent": 52.308}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.settings.system.system-update-settings #DgsQfvhFvEbvmHhhNI88": "array:4 [\n  \"data\" => array:11 [\n    \"currentVersion\" => \"\"\n    \"latestVersion\" => \"1.0.4\"\n    \"purchase_key\" => null\n    \"username\" => null\n    \"update_id\" => \"29a9f03edda39454031c\"\n    \"has_sql_update\" => true\n    \"releases\" => []\n    \"update\" => array:7 [\n      \"current_version\" => \"1.0.3\"\n      \"latest_version\" => \"1.0.4\"\n      \"update_id\" => \"29a9f03edda39454031c\"\n      \"has_sql_update\" => true\n      \"release_date\" => \"2025-05-23\"\n      \"changelog\" => \"<p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">New Features</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Privacy &amp; Policy Page - Added a section to show privacy and policy details manage within permissions</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile – no switching tabs.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Delete Chat Permission - Only allowed users can delete chats now – more control and security.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Sidebar Collapse/Expand - You can hide or show the sidebar to make more space on your screen.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Download Campaign Reports - Easily download reports for your campaigns.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Module Concept - Module concept implemented</span></li></ol><p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Bug Fixes</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Fixed an issue where some templates were not loading from Meta – now all templates load properly.</span></li></ol><p><br></p><p><br></p>\"\n      \"summary\" => \"minor update\"\n    ]\n    \"token\" => \"ZGVmYXVsdC12ZXJpZmljYXRpb24taWQ=\"\n    \"support\" => []\n    \"versionLog\" => array:2 [\n      \"success\" => true\n      \"versions\" => array:5 [\n        0 => array:4 [\n          \"version\" => \"1.1.0\"\n          \"date\" => \"July 04, 2025\"\n          \"is_latest\" => true\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Bot Flow Builder</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Build automated conversation flows with a drag-and-drop interface.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Management</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Introduced module management feature including core module support for better scalability and organization.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Campaign New Layout</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Updated campaign UI with enhanced filters for better targeting and usability.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Optimized Chat Loading</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Improved chat load speed and performance for smoother user experience.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">New Line Message Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – You can now send messages with new lines in chat (Shift + Enter supported).</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Location &amp; Contacts Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Chat now supports receiving shared location and contact data for better communication.</span></li></ol>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Fixed several performance issues in chat and campaign sections for faster operation and better stability.</span></li></ol>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<p><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">New Feature</strong></p><p><br></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Bot Flow Builder</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Build automated conversation flows with a drag-and-drop interface.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Management</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Introduced module management feature including core module support for better scalability and organization.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Campaign New Layout</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Updated campaign UI with enhanced filters for better targeting and usability.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Optimized Chat Loading</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Improved chat load speed and performance for smoother user experience.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">New Line Message Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – You can now send messages with new lines in chat (Shift + Enter supported).</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Location &amp; Contacts Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Chat now supports receiving shared location and contact data for better communication.</span></li></ol><p><br></p><p><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Bug Fix</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> Fixed several performance issues in chat and campaign sections for faster operation and better stability.</span></li></ol>\"\n            ]\n          ]\n        ]\n        1 => array:4 [\n          \"version\" => \"1.0.5\"\n          \"date\" => \"July 03, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Module Management Preparation</strong><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\"> – Refactored internal structure to lay the groundwork for future advanced module features.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Codebase Cleanup</strong><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\"> – Removed deprecated logic and optimized backend structure related to module loading.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Improved Stability</strong><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\"> – Minor updates to enhance system reliability during module initialization.</span></li></ol><p><br></p>\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Management Preparation</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Refactored internal structure to lay the groundwork for future advanced module features.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Codebase Cleanup</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Removed deprecated logic and optimized backend structure related to module loading.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Improved Stability</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Minor updates to enhance system reliability during module initialization.</span></li></ol>\"\n            ]\n          ]\n        ]\n        2 => array:4 [\n          \"version\" => \"1.0.4\"\n          \"date\" => \"May 23, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Privacy &amp; Policy Page - Added a section to show privacy and policy details manage within permissions</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile – no switching tabs.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Delete Chat Permission - Only allowed users can delete chats now – more control and security.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Sidebar Collapse/Expand - You can hide or show the sidebar to make more space on your screen.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Download Campaign Reports - Easily download reports for your campaigns.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Concept - Module concept implemented</span></li></ol><p><br></p>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Fixed an issue where some templates were not loading from Meta – now all templates load properly.</span></li></ol>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">New Features</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Privacy &amp; Policy Page - Added a section to show privacy and policy details manage within permissions</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile – no switching tabs.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Delete Chat Permission - Only allowed users can delete chats now – more control and security.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Sidebar Collapse/Expand - You can hide or show the sidebar to make more space on your screen.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Download Campaign Reports - Easily download reports for your campaigns.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Module Concept - Module concept implemented</span></li></ol><p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Bug Fixes</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Fixed an issue where some templates were not loading from Meta – now all templates load properly.</span></li></ol><p><br></p><p><br></p>\"\n            ]\n          ]\n        ]\n        3 => array:4 [\n          \"version\" => \"1.0.3\"\n          \"date\" => \"April 22, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>The module concept will be implemented in upcoming features.</li></ol>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li></ol>\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fix the issue with updating the template.</li></ol>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<h3><strong>Features</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>The module concept will be implemented in upcoming features.</li></ol><p><br></p><h3><strong>Bug Fixes</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fix the issue with updating the template.</li></ol><p><br></p><h3><strong>Improvements</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li></ol><p><br></p>\"\n            ]\n          ]\n        ]\n        4 => array:4 [\n          \"version\" => \"1.0.2\"\n          \"date\" => \"April 14, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Option added to enable or disable environment mode.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Ability to toggle Laravel and WhatsApp logs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Refresh button added to all data tables.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Added functionality to resend failed campaigns.</li></ol><p><br></p>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Improved performance of campaign queue jobs and cron jobs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>General performance improvements across the system.</li></ol><p><br></p>\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fixed an issue where campaign statistics were displaying incorrect data.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>In some cases, campaign scheduling was not running properly.</li></ol><p><br></p>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<h3><strong>Features</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Option added to enable or disable environment mode.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Ability to toggle Laravel and WhatsApp logs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Refresh button added to all data tables.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Added functionality to resend failed campaigns.</li></ol><p><br></p><h3><strong>Bug Fixes</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fixed an issue where campaign statistics were displaying incorrect data.</li></ol><p><br></p><h3><strong>Improvements</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Improved performance of campaign queue jobs and cron jobs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>General performance improvements across the system.</li></ol><p><br></p>\"\n            ]\n          ]\n        ]\n      ]\n    ]\n  ]\n  \"name\" => \"admin.settings.system.system-update-settings\"\n  \"component\" => \"App\\Livewire\\Admin\\Settings\\System\\SystemUpdateSettings\"\n  \"id\" => \"DgsQfvhFvEbvmHhhNI88\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Settings\\System\\SystemUpdateSettings@save<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FSettings%2FSystem%2FSystemUpdateSettings.php&line=66\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FSettings%2FSystem%2FSystemUpdateSettings.php&line=66\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Settings/System/SystemUpdateSettings.php:66-80</a>", "middleware": "web", "duration": "669ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-668786688 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-668786688\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-841301660 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"20630 characters\">{&quot;data&quot;:{&quot;currentVersion&quot;:&quot;&quot;,&quot;latestVersion&quot;:&quot;1.0.4&quot;,&quot;purchase_key&quot;:null,&quot;username&quot;:null,&quot;update_id&quot;:&quot;29a9f03edda39454031c&quot;,&quot;has_sql_update&quot;:true,&quot;releases&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;update&quot;:[{&quot;current_version&quot;:&quot;1.0.3&quot;,&quot;latest_version&quot;:&quot;1.0.4&quot;,&quot;update_id&quot;:&quot;29a9f03edda39454031c&quot;,&quot;has_sql_update&quot;:true,&quot;release_date&quot;:&quot;2025-05-23&quot;,&quot;changelog&quot;:&quot;&lt;p&gt;&lt;strong style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;New Features&lt;\\/strong&gt;&lt;\\/p&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Privacy &amp;amp; Policy Page - Added a section to show privacy and policy details manage within permissions&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile \\u2013 no switching tabs.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Delete Chat Permission - Only allowed users can delete chats now \\u2013 more control and security.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Sidebar Collapse\\/Expand - You can hide or show the sidebar to make more space on your screen.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Download Campaign Reports - Easily download reports for your campaigns.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Module Concept - Module concept implemented&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;strong style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Bug Fixes&lt;\\/strong&gt;&lt;\\/p&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Fixed an issue where some templates were not loading from Meta \\u2013 now all templates load properly.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;,&quot;summary&quot;:&quot;minor update&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;token&quot;:&quot;ZGVmYXVsdC12ZXJpZmljYXRpb24taWQ=&quot;,&quot;support&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;versionLog&quot;:[{&quot;success&quot;:true,&quot;versions&quot;:[[[{&quot;version&quot;:&quot;1.1.0&quot;,&quot;date&quot;:&quot;July 04, 2025&quot;,&quot;is_latest&quot;:true,&quot;changes&quot;:[[[{&quot;type&quot;:&quot;feature&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Bot Flow Builder&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Build automated conversation flows with a drag-and-drop interface.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Module Management&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Introduced module management feature including core module support for better scalability and organization.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Campaign New Layout&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Updated campaign UI with enhanced filters for better targeting and usability.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Optimized Chat Loading&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Improved chat load speed and performance for smoother user experience.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;New Line Message Support&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 You can now send messages with new lines in chat (Shift + Enter supported).&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Location &amp;amp; Contacts Support&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Chat now supports receiving shared location and contact data for better communication.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;improvement&quot;,&quot;description&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;bug&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Fixed several performance issues in chat and campaign sections for faster operation and better stability.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;changelog&quot;,&quot;description&quot;:&quot;&lt;p&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;New Feature&lt;\\/strong&gt;&lt;\\/p&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Bot Flow Builder&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Build automated conversation flows with a drag-and-drop interface.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Module Management&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Introduced module management feature including core module support for better scalability and organization.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Campaign New Layout&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Updated campaign UI with enhanced filters for better targeting and usability.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Optimized Chat Loading&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Improved chat load speed and performance for smoother user experience.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;New Line Message Support&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 You can now send messages with new lines in chat (Shift + Enter supported).&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Location &amp;amp; Contacts Support&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Chat now supports receiving shared location and contact data for better communication.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;p&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; &lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Bug Fix&lt;\\/strong&gt;&lt;\\/p&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; Fixed several performance issues in chat and campaign sections for faster operation and better stability.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;version&quot;:&quot;1.0.5&quot;,&quot;date&quot;:&quot;July 03, 2025&quot;,&quot;is_latest&quot;:false,&quot;changes&quot;:[[[{&quot;type&quot;:&quot;feature&quot;,&quot;description&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;improvement&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Module Management Preparation&lt;\\/strong&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt; \\u2013 Refactored internal structure to lay the groundwork for future advanced module features.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Codebase Cleanup&lt;\\/strong&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt; \\u2013 Removed deprecated logic and optimized backend structure related to module loading.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Improved Stability&lt;\\/strong&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt; \\u2013 Minor updates to enhance system reliability during module initialization.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;bug&quot;,&quot;description&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;changelog&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Module Management Preparation&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Refactored internal structure to lay the groundwork for future advanced module features.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Codebase Cleanup&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Removed deprecated logic and optimized backend structure related to module loading.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;strong style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Improved Stability&lt;\\/strong&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt; \\u2013 Minor updates to enhance system reliability during module initialization.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;version&quot;:&quot;1.0.4&quot;,&quot;date&quot;:&quot;May 23, 2025&quot;,&quot;is_latest&quot;:false,&quot;changes&quot;:[[[{&quot;type&quot;:&quot;feature&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Privacy &amp;amp; Policy Page - Added a section to show privacy and policy details manage within permissions&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile \\u2013 no switching tabs.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Delete Chat Permission - Only allowed users can delete chats now \\u2013 more control and security.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Sidebar Collapse\\/Expand - You can hide or show the sidebar to make more space on your screen.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Download Campaign Reports - Easily download reports for your campaigns.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Module Concept - Module concept implemented&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;improvement&quot;,&quot;description&quot;:&quot;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;bug&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\\&quot;&gt;Fixed an issue where some templates were not loading from Meta \\u2013 now all templates load properly.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;changelog&quot;,&quot;description&quot;:&quot;&lt;p&gt;&lt;strong style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;New Features&lt;\\/strong&gt;&lt;\\/p&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Privacy &amp;amp; Policy Page - Added a section to show privacy and policy details manage within permissions&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile \\u2013 no switching tabs.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Delete Chat Permission - Only allowed users can delete chats now \\u2013 more control and security.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Sidebar Collapse\\/Expand - You can hide or show the sidebar to make more space on your screen.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Download Campaign Reports - Easily download reports for your campaigns.&lt;\\/span&gt;&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Module Concept - Module concept implemented&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;strong style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Bug Fixes&lt;\\/strong&gt;&lt;\\/p&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;&lt;span style=\\&quot;color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\\&quot;&gt;Fixed an issue where some templates were not loading from Meta \\u2013 now all templates load properly.&lt;\\/span&gt;&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;version&quot;:&quot;1.0.3&quot;,&quot;date&quot;:&quot;April 22, 2025&quot;,&quot;is_latest&quot;:false,&quot;changes&quot;:[[[{&quot;type&quot;:&quot;feature&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;The module concept will be implemented in upcoming features.&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;improvement&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Enhanced identification of potential bugs and issues.&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;bug&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Fix the issue with updating the template.&lt;\\/li&gt;&lt;\\/ol&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;changelog&quot;,&quot;description&quot;:&quot;&lt;h3&gt;&lt;strong&gt;Features&lt;\\/strong&gt;&lt;\\/h3&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;The module concept will be implemented in upcoming features.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;h3&gt;&lt;strong&gt;Bug Fixes&lt;\\/strong&gt;&lt;\\/h3&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Fix the issue with updating the template.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;h3&gt;&lt;strong&gt;Improvements&lt;\\/strong&gt;&lt;\\/h3&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Enhanced identification of potential bugs and issues.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;version&quot;:&quot;1.0.2&quot;,&quot;date&quot;:&quot;April 14, 2025&quot;,&quot;is_latest&quot;:false,&quot;changes&quot;:[[[{&quot;type&quot;:&quot;feature&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Option added to enable or disable environment mode.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Ability to toggle Laravel and WhatsApp logs.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Refresh button added to all data tables.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Added functionality to resend failed campaigns.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;improvement&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Enhanced identification of potential bugs and issues.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Improved performance of campaign queue jobs and cron jobs.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;General performance improvements across the system.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;bug&quot;,&quot;description&quot;:&quot;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Fixed an issue where campaign statistics were displaying incorrect data.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;In some cases, campaign scheduling was not running properly.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}],[{&quot;type&quot;:&quot;changelog&quot;,&quot;description&quot;:&quot;&lt;h3&gt;&lt;strong&gt;Features&lt;\\/strong&gt;&lt;\\/h3&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Option added to enable or disable environment mode.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Ability to toggle Laravel and WhatsApp logs.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Refresh button added to all data tables.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Added functionality to resend failed campaigns.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;h3&gt;&lt;strong&gt;Bug Fixes&lt;\\/strong&gt;&lt;\\/h3&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Fixed an issue where campaign statistics were displaying incorrect data.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&lt;h3&gt;&lt;strong&gt;Improvements&lt;\\/strong&gt;&lt;\\/h3&gt;&lt;ol&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Enhanced identification of potential bugs and issues.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;Improved performance of campaign queue jobs and cron jobs.&lt;\\/li&gt;&lt;li data-list=\\&quot;bullet\\&quot;&gt;&lt;span class=\\&quot;ql-ui\\&quot; contenteditable=\\&quot;false\\&quot;&gt;&lt;\\/span&gt;General performance improvements across the system.&lt;\\/li&gt;&lt;\\/ol&gt;&lt;p&gt;&lt;br&gt;&lt;\\/p&gt;&quot;},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;DgsQfvhFvEbvmHhhNI88&quot;,&quot;name&quot;:&quot;admin.settings.system.system-update-settings&quot;,&quot;path&quot;:&quot;admin\\/system-update&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;66dae400291becf8d9f58faa052ad895716e4bd94886d71d3fd5c2cd049db69c&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841301660\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2146654709 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">22599</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/system-update</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImVHNm9Fd205WElLcE1xMzRaMlBHMnc9PSIsInZhbHVlIjoibFNkdkZVWURJMnN4aFlFb1pCT2ZtQ0dhaGpkSXJ3VGZRbGV6QTFQTGR4cjJOZk5BdzZqSEZyMFpnc2ZMRHA5WlpsaTB0bUMzMmZsb2JIWWVXR2VUeHYrZWg0SjVycHlpc2VPV0VtNVZXUzZBM0lERExPUjg4c1MrRkdnb01VUzUiLCJtYWMiOiIzNDY3MDExZDhhN2Q4NzAzZjc5ZDBmNjU4MTYwOWFkOTMxYjU4MDcwYzQ1MzA2YjQyZjQ1NDE1ZDFmYjRkYmNhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlSNjdGNkRJdm92WE9HT2hHTG9Lanc9PSIsInZhbHVlIjoiSWFRejB1ZnNZZWxGYzZUT3hWTVpBUUFBZXQ4aTU3VzlMOGE1QTNmMVBsbHFQeGJIN3BobEp3c1VsQ1dmNVI4MVFhcXQ2M0JMNS9oY2F1Y1hnSURheXRaMm9GcEU4UjQ5R29KbmNrODAwMktiNVlRWDl1NWVvRWZJdlF2WWIzUDQiLCJtYWMiOiJhYTc2NTExYTFjODE3ZmZlMjdkZjdiNDk3NjNhMzNkNTYwNTFlMWM3NjM4M2JhYmQzZWZjMGJiYTM5OGQyMjkwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146654709\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-437890692 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437890692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-658181912 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:48:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-658181912\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1324404153 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://127.0.0.1:8000/admin/system-update</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324404153\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}