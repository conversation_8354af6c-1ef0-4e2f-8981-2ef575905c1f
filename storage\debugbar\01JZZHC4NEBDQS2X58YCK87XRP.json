{"__meta": {"id": "01JZZHC4NEBDQS2X58YCK87XRP", "datetime": "2025-07-12 04:42:06", "utime": **********.127423, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331324.762702, "end": **********.127437, "duration": 1.3647351264953613, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1752331324.762702, "relative_start": 0, "end": **********.841841, "relative_end": **********.841841, "duration": 1.***************, "duration_str": "1.08s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.841852, "relative_start": 1.****************, "end": **********.127439, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.874462, "relative_start": 1.***************, "end": **********.879655, "relative_end": **********.879655, "duration": 0.005192995071411133, "duration_str": "5.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.118757, "relative_start": 1.****************, "end": **********.120511, "relative_end": **********.120511, "duration": 0.0017540454864501953, "duration_str": "1.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.019030000000000002, "accumulated_duration_str": "19.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.884652, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 2.733}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9159, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 2.733, "width_percent": 2.26}, {"sql": "select count(*) as aggregate from `roles` where `name` = 'Admin'", "type": "query", "params": [], "bindings": ["Admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.970668, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 4.992, "width_percent": 9.354}, {"sql": "insert into `roles` (`guard_name`, `name`, `updated_at`, `created_at`) values ('web', 'Admin', '2025-07-12 04:42:06', '2025-07-12 04:42:06')", "type": "query", "params": [], "bindings": ["web", "Admin", "2025-07-12 04:42:06", "2025-07-12 04:42:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.009285, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "RoleCreator.php:56", "source": {"index": 15, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=56", "ajax": false, "filename": "RoleCreator.php", "line": "56"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 14.346, "width_percent": 13.295}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.063552, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 27.641, "width_percent": 4.572}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.070367, "duration": 0.0071600000000000006, "duration_str": "7.16ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 32.212, "width_percent": 37.625}, {"sql": "delete from `role_has_permissions` where `role_has_permissions`.`role_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 454}, {"index": 13, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 57}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.096301, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:454", "source": {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 454}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=454", "ajax": false, "filename": "HasPermissions.php", "line": "454"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 69.837, "width_percent": 2.575}, {"sql": "insert into `role_has_permissions` (`permission_id`, `role_id`) values (1, 1), (2, 1), (3, 1), (4, 1), (8, 1), (7, 1), (6, 1), (5, 1), (10, 1), (11, 1), (12, 1), (15, 1), (13, 1), (17, 1), (14, 1), (18, 1), (19, 1), (20, 1), (25, 1), (24, 1), (23, 1), (22, 1), (21, 1), (26, 1), (27, 1), (28, 1), (29, 1), (30, 1), (31, 1), (32, 1), (33, 1), (34, 1), (35, 1), (36, 1), (37, 1), (38, 1), (39, 1), (40, 1), (41, 1), (42, 1), (43, 1), (44, 1), (45, 1), (46, 1), (47, 1), (52, 1), (51, 1), (50, 1), (49, 1), (48, 1), (53, 1), (54, 1), (55, 1), (56, 1), (58, 1), (60, 1), (59, 1)", "type": "query", "params": [], "bindings": [1, 1, 2, 1, 3, 1, 4, 1, 8, 1, 7, 1, 6, 1, 5, 1, 10, 1, 11, 1, 12, 1, 15, 1, 13, 1, 17, 1, 14, 1, 18, 1, 19, 1, 20, 1, 25, 1, 24, 1, 23, 1, 22, 1, 21, 1, 26, 1, 27, 1, 28, 1, 29, 1, 30, 1, 31, 1, 32, 1, 33, 1, 34, 1, 35, 1, 36, 1, 37, 1, 38, 1, 39, 1, 40, 1, 41, 1, 42, 1, 43, 1, 44, 1, 45, 1, 46, 1, 47, 1, 52, 1, 51, 1, 50, 1, 49, 1, 48, 1, 53, 1, 54, 1, 55, 1, 56, 1, 58, 1, 60, 1, 59, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, {"index": 12, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 458}, {"index": 13, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 57}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.105443, "duration": 0.00525, "duration_str": "5.25ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:406", "source": {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 406}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=406", "ajax": false, "filename": "HasPermissions.php", "line": "406"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 72.412, "width_percent": 27.588}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 60, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 61, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Miscellaneous\\RoleCreator@save<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Miscellaneous/RoleCreator.php:50-71</a>", "middleware": "web", "duration": "1.37s", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-738061366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-738061366\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-994051307 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"533 characters\">{&quot;data&quot;:{&quot;role&quot;:[{&quot;name&quot;:null},{&quot;class&quot;:&quot;Spatie\\\\Permission\\\\Models\\\\Role&quot;,&quot;connection&quot;:null,&quot;s&quot;:&quot;elmdl&quot;}],&quot;selectedPermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;role_id&quot;:null,&quot;assigne_from_contact&quot;:null,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;ZDy8ElQ13HydXlDoPjTZ&quot;,&quot;name&quot;:&quot;admin.miscellaneous.role-creator&quot;,&quot;path&quot;:&quot;admin\\/roles\\/role&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-4117460410-0&quot;:[&quot;div&quot;,&quot;BvNYh3q22RO25ZjKRRnX&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;d565e664ad7c47c3210a8cd5828c33d6104d614329dda4557c7d8097b34b130b&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:58</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>role.name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Admin</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.0</span>\" => \"<span class=sf-dump-str title=\"11 characters\">source.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.1</span>\" => \"<span class=sf-dump-str title=\"13 characters\">source.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.2</span>\" => \"<span class=sf-dump-str title=\"11 characters\">source.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.3</span>\" => \"<span class=sf-dump-str title=\"13 characters\">source.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.4</span>\" => \"<span class=sf-dump-str title=\"16 characters\">ai_prompt.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.5</span>\" => \"<span class=sf-dump-str title=\"14 characters\">ai_prompt.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.6</span>\" => \"<span class=sf-dump-str title=\"16 characters\">ai_prompt.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.7</span>\" => \"<span class=sf-dump-str title=\"14 characters\">ai_prompt.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.8</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.9</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.10</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.11</span>\" => \"<span class=sf-dump-str title=\"26 characters\">connect_account.disconnect</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.12</span>\" => \"<span class=sf-dump-str title=\"20 characters\">connect_account.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.13</span>\" => \"<span class=sf-dump-str title=\"18 characters\">message_bot.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.14</span>\" => \"<span class=sf-dump-str title=\"23 characters\">connect_account.connect</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.15</span>\" => \"<span class=sf-dump-str title=\"16 characters\">message_bot.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.16</span>\" => \"<span class=sf-dump-str title=\"18 characters\">message_bot.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.17</span>\" => \"<span class=sf-dump-str title=\"17 characters\">message_bot.clone</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.18</span>\" => \"<span class=sf-dump-str title=\"18 characters\">template_bot.clone</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.19</span>\" => \"<span class=sf-dump-str title=\"19 characters\">template_bot.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.20</span>\" => \"<span class=sf-dump-str title=\"17 characters\">template_bot.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.21</span>\" => \"<span class=sf-dump-str title=\"19 characters\">template_bot.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.22</span>\" => \"<span class=sf-dump-str title=\"17 characters\">template_bot.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.23</span>\" => \"<span class=sf-dump-str title=\"13 characters\">template.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.24</span>\" => \"<span class=sf-dump-str title=\"22 characters\">template.load_template</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.25</span>\" => \"<span class=sf-dump-str title=\"14 characters\">campaigns.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.26</span>\" => \"<span class=sf-dump-str title=\"16 characters\">campaigns.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.27</span>\" => \"<span class=sf-dump-str title=\"14 characters\">campaigns.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.28</span>\" => \"<span class=sf-dump-str title=\"16 characters\">campaigns.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.29</span>\" => \"<span class=sf-dump-str title=\"23 characters\">campaigns.show_campaign</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.30</span>\" => \"<span class=sf-dump-str title=\"9 characters\">chat.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.31</span>\" => \"<span class=sf-dump-str title=\"14 characters\">chat.read_only</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.32</span>\" => \"<span class=sf-dump-str title=\"17 characters\">activity_log.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.33</span>\" => \"<span class=sf-dump-str title=\"19 characters\">activity_log.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.34</span>\" => \"<span class=sf-dump-str title=\"23 characters\">whatsmark_settings.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.35</span>\" => \"<span class=sf-dump-str title=\"23 characters\">whatsmark_settings.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.36</span>\" => \"<span class=sf-dump-str title=\"19 characters\">bulk_campaigns.send</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.37</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.38</span>\" => \"<span class=sf-dump-str title=\"11 characters\">role.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.39</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.40</span>\" => \"<span class=sf-dump-str title=\"11 characters\">role.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.41</span>\" => \"<span class=sf-dump-str title=\"11 characters\">status.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.42</span>\" => \"<span class=sf-dump-str title=\"13 characters\">status.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.43</span>\" => \"<span class=sf-dump-str title=\"11 characters\">status.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.44</span>\" => \"<span class=sf-dump-str title=\"13 characters\">status.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.45</span>\" => \"<span class=sf-dump-str title=\"19 characters\">contact.bulk_import</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.46</span>\" => \"<span class=sf-dump-str title=\"14 characters\">contact.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.47</span>\" => \"<span class=sf-dump-str title=\"12 characters\">contact.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.48</span>\" => \"<span class=sf-dump-str title=\"14 characters\">contact.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.49</span>\" => \"<span class=sf-dump-str title=\"12 characters\">contact.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.50</span>\" => \"<span class=sf-dump-str title=\"20 characters\">system_settings.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.51</span>\" => \"<span class=sf-dump-str title=\"20 characters\">system_settings.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.52</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.view</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.53</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user.create</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.54</span>\" => \"<span class=sf-dump-str title=\"11 characters\">user.delete</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.55</span>\" => \"<span class=sf-dump-str title=\"19 characters\">email_template.edit</span>\"\n        \"<span class=sf-dump-key>selectedPermissions.56</span>\" => \"<span class=sf-dump-str title=\"19 characters\">email_template.view</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994051307\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1449682950 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3266</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/roles/role</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjQ3V2hVdEhGbE1lOXpKR1pCM3E5VFE9PSIsInZhbHVlIjoiODlPTUJxQjNhZkJtYUN6NUdXOHBvNHVwYXQ5c1NxQ1EyR1psM1lwOVp1QmY0NytQS2xnR3VaUDBrbndsR1FrNEJOTC82Qnd4d3FSNHB3R29aaDM4TjYrd0Z6aUwyaVZNOGxDWTJOTkorejY3dm5LZU5oSlBjaW9zVmRzbWt5Ym8iLCJtYWMiOiJkNmFiMTg3NzMxMWYyZDQ5ZDFjODhlN2NhYTRhZmY3OTQ4NTliZTBiYTNmN2NjYzk2ZTIwOTc5YmM2YWFiYjJiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InU2T1NONVFHZmJvVDZrazdTcEUyRHc9PSIsInZhbHVlIjoiZW42VnNkNG1JOURDZU1mb3NZNVhnMDJ1L0ZFTDQ4OUlMdVFYYzBPOEhsK25tNlJqbEhOaDloRHgrem5wcjJZRTdaeTJXeTlVN3R3SVpMNWJVejhMLzF1eDJMbGI0SU8rSGpRN2R2dDdMTzZSdm43WDBrSnh2eWJITWszZnh1eksiLCJtYWMiOiJlOWExMGMzYjA1NDE0OGRkZTU3NzE3NzExZTQyYzhlM2FlYmE4YTZmZjlmZTA1NTZhYzJhMDcwM2FhMjE1Zjc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1449682950\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-359844517 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359844517\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-204972999 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:42:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-204972999\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1631451384 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/roles/role</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">notification</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>notification</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Role saved successfully</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631451384\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}