{"__meta": {"id": "01JZZHG0E4YMTQ46Y5MATRZGPJ", "datetime": "2025-07-12 04:44:12", "utime": **********.869639, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.209201, "end": **********.869691, "duration": 0.6604897975921631, "duration_str": "660ms", "measures": [{"label": "Booting", "start": **********.209201, "relative_start": 0, "end": **********.783532, "relative_end": **********.783532, "duration": 0.****************, "duration_str": "574ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.783563, "relative_start": 0.****************, "end": **********.869692, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "86.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.799067, "relative_start": 0.****************, "end": **********.802304, "relative_end": **********.802304, "duration": 0.003237009048461914, "duration_str": "3.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.chat.manage-canned-reply", "start": **********.836536, "relative_start": 0.****************, "end": **********.836536, "relative_end": **********.836536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f6087304347b91d5b52dd9ef7bce9ed", "start": **********.841372, "relative_start": 0.****************, "end": **********.841372, "relative_end": **********.841372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary", "start": **********.842386, "relative_start": 0.6331849098205566, "end": **********.842386, "relative_end": **********.842386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.843009, "relative_start": 0.633807897567749, "end": **********.843009, "relative_end": **********.843009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c72afa2d57b50a384b65435470599743", "start": **********.84416, "relative_start": 0.6349589824676514, "end": **********.84416, "relative_end": **********.84416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da8aafad19db3bbc97d2eb2356d7d4d9", "start": **********.845504, "relative_start": 0.6363029479980469, "end": **********.845504, "relative_end": **********.845504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary", "start": **********.845774, "relative_start": 0.6365728378295898, "end": **********.845774, "relative_end": **********.845774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.846191, "relative_start": 0.6369898319244385, "end": **********.846191, "relative_end": **********.846191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.84665, "relative_start": 0.637448787689209, "end": **********.84665, "relative_end": **********.84665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.848296, "relative_start": 0.6390948295593262, "end": **********.848296, "relative_end": **********.848296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.84901, "relative_start": 0.6398088932037354, "end": **********.84901, "relative_end": **********.84901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.84972, "relative_start": 0.6405189037322998, "end": **********.84972, "relative_end": **********.84972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.851263, "relative_start": 0.6420619487762451, "end": **********.851263, "relative_end": **********.851263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.textarea", "start": **********.851664, "relative_start": 0.642462968826294, "end": **********.851664, "relative_end": **********.851664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.85225, "relative_start": 0.6430490016937256, "end": **********.85225, "relative_end": **********.85225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.853206, "relative_start": 0.6440048217773438, "end": **********.853206, "relative_end": **********.853206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.853699, "relative_start": 0.6444978713989258, "end": **********.853699, "relative_end": **********.853699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.loading-button", "start": **********.854592, "relative_start": 0.6453909873962402, "end": **********.854592, "relative_end": **********.854592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83f96136a4af4da91c50b6760ff72460", "start": **********.855618, "relative_start": 0.6464169025421143, "end": **********.855618, "relative_end": **********.855618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.modal.custom-modal", "start": **********.855901, "relative_start": 0.6466999053955078, "end": **********.855901, "relative_end": **********.855901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.cancel-button", "start": **********.859051, "relative_start": 0.6498498916625977, "end": **********.859051, "relative_end": **********.859051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.delete-button", "start": **********.860036, "relative_start": 0.6508347988128662, "end": **********.860036, "relative_end": **********.860036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.modal.confirm-box", "start": **********.860473, "relative_start": 0.6512718200683594, "end": **********.860473, "relative_end": **********.860473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a8d9e8ed5d18da4324b870627e9cca5b", "start": **********.861615, "relative_start": 0.6524138450622559, "end": **********.861615, "relative_end": **********.861615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.86664, "relative_start": 0.6574389934539795, "end": **********.867688, "relative_end": **********.867688, "duration": 0.0010478496551513672, "duration_str": "1.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 37128776, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 24, "nb_templates": 24, "templates": [{"name": "livewire.admin.chat.manage-canned-reply", "param_count": null, "params": [], "start": **********.836496, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/chat/manage-canned-reply.blade.phplivewire.admin.chat.manage-canned-reply", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fchat%2Fmanage-canned-reply.blade.php&line=1", "ajax": false, "filename": "manage-canned-reply.blade.php", "line": "?"}}, {"name": "__components::1f6087304347b91d5b52dd9ef7bce9ed", "param_count": null, "params": [], "start": **********.841338, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/1f6087304347b91d5b52dd9ef7bce9ed.blade.php__components::1f6087304347b91d5b52dd9ef7bce9ed", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F1f6087304347b91d5b52dd9ef7bce9ed.blade.php&line=1", "ajax": false, "filename": "1f6087304347b91d5b52dd9ef7bce9ed.blade.php", "line": "?"}}, {"name": "components.button.primary", "param_count": null, "params": [], "start": **********.842351, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary.blade.phpcomponents.button.primary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary.blade.php&line=1", "ajax": false, "filename": "primary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.842976, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "__components::c72afa2d57b50a384b65435470599743", "param_count": null, "params": [], "start": **********.844127, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c72afa2d57b50a384b65435470599743.blade.php__components::c72afa2d57b50a384b65435470599743", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc72afa2d57b50a384b65435470599743.blade.php&line=1", "ajax": false, "filename": "c72afa2d57b50a384b65435470599743.blade.php", "line": "?"}}, {"name": "__components::da8aafad19db3bbc97d2eb2356d7d4d9", "param_count": null, "params": [], "start": **********.845471, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/da8aafad19db3bbc97d2eb2356d7d4d9.blade.php__components::da8aafad19db3bbc97d2eb2356d7d4d9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fda8aafad19db3bbc97d2eb2356d7d4d9.blade.php&line=1", "ajax": false, "filename": "da8aafad19db3bbc97d2eb2356d7d4d9.blade.php", "line": "?"}}, {"name": "components.button.primary", "param_count": null, "params": [], "start": **********.845742, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary.blade.phpcomponents.button.primary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary.blade.php&line=1", "ajax": false, "filename": "primary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.846158, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.card", "param_count": null, "params": [], "start": **********.846619, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "components.label", "param_count": null, "params": [], "start": **********.848237, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "components.input", "param_count": null, "params": [], "start": **********.848969, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.849661, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.label", "param_count": null, "params": [], "start": **********.85123, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "components.textarea", "param_count": null, "params": [], "start": **********.851633, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/textarea.blade.phpcomponents.textarea", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.852176, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.button.secondary", "param_count": null, "params": [], "start": **********.853174, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/secondary.blade.phpcomponents.button.secondary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fsecondary.blade.php&line=1", "ajax": false, "filename": "secondary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.853667, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.button.loading-button", "param_count": null, "params": [], "start": **********.85456, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/loading-button.blade.phpcomponents.button.loading-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Floading-button.blade.php&line=1", "ajax": false, "filename": "loading-button.blade.php", "line": "?"}}, {"name": "__components::83f96136a4af4da91c50b6760ff72460", "param_count": null, "params": [], "start": **********.855587, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83f96136a4af4da91c50b6760ff72460.blade.php__components::83f96136a4af4da91c50b6760ff72460", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83f96136a4af4da91c50b6760ff72460.blade.php&line=1", "ajax": false, "filename": "83f96136a4af4da91c50b6760ff72460.blade.php", "line": "?"}}, {"name": "components.modal.custom-modal", "param_count": null, "params": [], "start": **********.855867, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/modal/custom-modal.blade.phpcomponents.modal.custom-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fcustom-modal.blade.php&line=1", "ajax": false, "filename": "custom-modal.blade.php", "line": "?"}}, {"name": "components.button.cancel-button", "param_count": null, "params": [], "start": **********.859015, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/cancel-button.blade.phpcomponents.button.cancel-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fcancel-button.blade.php&line=1", "ajax": false, "filename": "cancel-button.blade.php", "line": "?"}}, {"name": "components.button.delete-button", "param_count": null, "params": [], "start": **********.860002, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/delete-button.blade.phpcomponents.button.delete-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fdelete-button.blade.php&line=1", "ajax": false, "filename": "delete-button.blade.php", "line": "?"}}, {"name": "components.modal.confirm-box", "param_count": null, "params": [], "start": **********.86044, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/modal/confirm-box.blade.phpcomponents.modal.confirm-box", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm-box.blade.php&line=1", "ajax": false, "filename": "confirm-box.blade.php", "line": "?"}}, {"name": "__components::a8d9e8ed5d18da4324b870627e9cca5b", "param_count": null, "params": [], "start": **********.861581, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/a8d9e8ed5d18da4324b870627e9cca5b.blade.php__components::a8d9e8ed5d18da4324b870627e9cca5b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fa8d9e8ed5d18da4324b870627e9cca5b.blade.php&line=1", "ajax": false, "filename": "a8d9e8ed5d18da4324b870627e9cca5b.blade.php", "line": "?"}}]}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0011099999999999999, "accumulated_duration_str": "1.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.804605, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 41.441}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.818656, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 41.441, "width_percent": 58.559}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.chat.manage-canned-reply #J6ljbhFOJaCr6S9t7Xsq": "array:4 [\n  \"data\" => array:5 [\n    \"canned\" => App\\Models\\CannedReply {#2023\n      #connection: null\n      #table: null\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: false\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:2 [\n        \"title\" => null\n        \"description\" => null\n      ]\n      #original: []\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"title\"\n        1 => \"description\"\n        2 => \"is_public\"\n        3 => \"added_from\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"showCannedModal\" => false\n    \"confirmingDeletion\" => false\n    \"canned_id\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.chat.manage-canned-reply\"\n  \"component\" => \"App\\Livewire\\Admin\\Chat\\ManageCannedReply\"\n  \"id\" => \"J6ljbhFOJaCr6S9t7Xsq\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Chat\\ManageCannedReply@refreshTable<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FChat%2FManageCannedReply.php&line=145\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FChat%2FManageCannedReply.php&line=145\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Chat/ManageCannedReply.php:145-148</a>", "middleware": "web", "duration": "661ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1182251549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1182251549\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-76757872 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"532 characters\">{&quot;data&quot;:{&quot;canned&quot;:[{&quot;title&quot;:null,&quot;description&quot;:null},{&quot;class&quot;:&quot;App\\\\Models\\\\CannedReply&quot;,&quot;connection&quot;:null,&quot;s&quot;:&quot;elmdl&quot;}],&quot;showCannedModal&quot;:false,&quot;confirmingDeletion&quot;:false,&quot;canned_id&quot;:null,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;J6ljbhFOJaCr6S9t7Xsq&quot;,&quot;name&quot;:&quot;admin.chat.manage-canned-reply&quot;,&quot;path&quot;:&quot;admin\\/canned-reply&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-3170399155-0&quot;:[&quot;div&quot;,&quot;kQveuSlJcb6pbklTtmY8&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b0b73981159b016b1056744772237b1920da3a576b7dcb03ca9de5a1a5c09c74&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"12 characters\">refreshTable</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76757872\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-317332335 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">762</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/canned-reply</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZrZTlBWm4rczV2N2FIbkdxWmMzWnc9PSIsInZhbHVlIjoidU8zTVdGUkVwS1UxNndsKzZxdCsxaUJZaFlaY0ZKQWczUlFnZWN1clJpUkhTaHRPSUM0ZDJ3MFZsVXRKZFk5UWRvNURiVW81YmxqL0x4aCtFdG9CK3Q1ankzd0VZRWpleFZUamRIQml3S0VIQ3hVWXZLTUlvUytJeGdPakpQVWIiLCJtYWMiOiJiYjY0ZWZlOWExNTAzYTFjNWU2MzE5YTVhMTcyNTQ4YWVjNjM5ZjA0NWU1ODMxMWEzNmFhMjNjZGQ4OWViMjVkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilh6NTEyMGJYSWdHNldhU0F0WWMwN2c9PSIsInZhbHVlIjoiRVNGQzRDZWZaU0FwVlhsMjZzSTdKQkJiQkRrMmpHRzkyT0ZLeGx6bk5UZzNMeDl0S3lZZGU2aGVDaXZtcUd1ODRoL3Fsb1JsWmoyRGk0VmFld2tCRm5zSEQ4WVZWVWRDcVVFbW5wREFCZFhadEJtYWlaREJJVmRvMllMWVRhcXYiLCJtYWMiOiJhNTRiZjMxYTk2OWQ1YzUwOWU1NDNiY2ZhMTExZTkzZGFlNzRkZTFkNDdkMGNiYWVmOTU0NTgzNTY3ZTkwMzc0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317332335\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1563472377 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563472377\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1525545614 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:44:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525545614\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-268676495 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/canned-reply</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268676495\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}