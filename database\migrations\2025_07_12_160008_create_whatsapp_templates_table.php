<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_templates', function (Blueprint $table) {
            $table->id();
            $table->integer('template_id'); // id from api
            $table->string('template_name');
            $table->string('language');
            $table->string('status');
            $table->string('category');
            $table->string('header_data_format')->nullable();
            $table->text('header_data_text')->nullable();
            $table->integer('header_params_count')->nullable();
            $table->text('body_data');
            $table->integer('body_params_count')->nullable();
            $table->text('footer_data')->nullable();
            $table->integer('footer_params_count')->nullable();
            $table->text('buttons_data')->nullable();
            $table->timestamps();

            // Add index on template_id for better performance
            $table->index('template_id');
        });

        Schema::create('message_bots', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('rel_type');
            $table->text('reply_text');
            $table->integer('reply_type');
            $table->json('trigger')->nullable();
            $table->string('bot_header')->nullable();
            $table->string('bot_footer')->nullable();
            $table->string('button1')->nullable();
            $table->string('button1_id')->nullable();
            $table->string('button2')->nullable();
            $table->string('button2_id')->nullable();
            $table->string('button3')->nullable();
            $table->string('button3_id')->nullable();
            $table->string('button_name')->nullable();
            $table->string('button_url')->nullable();
            $table->integer('addedfrom');
            $table->integer('is_bot_active')->default(1);
            $table->integer('sending_count')->default(0);
            $table->string('filename')->nullable();
            $table->timestamps();
        });

        Schema::create('template_bots', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('rel_type');
            $table->integer('template_id');
            $table->json('header_params')->nullable();
            $table->json('body_params')->nullable();
            $table->json('footer_params')->nullable();
            $table->string('filename')->nullable();
            $table->json('trigger')->nullable();
            $table->integer('reply_type');
            $table->integer('is_bot_active')->default(1);
            $table->timestamps();

            // Add index on template_id for better performance
            $table->index('template_id');
        });

        Schema::create('wm_activity_logs', function (Blueprint $table) {
            $table->id();
            $table->string('phone_number_id')->nullable();
            $table->text('access_token')->nullable();
            $table->string('business_account_id')->nullable();
            $table->string('response_code');
            $table->integer('client_id')->nullable();
            $table->text('response_data')->nullable();
            $table->string('category')->nullable();
            $table->integer('category_id')->nullable();
            $table->string('rel_type')->nullable();
            $table->integer('rel_id')->nullable();
            $table->text('category_params')->nullable();
            $table->text('raw_data')->nullable();
            $table->timestamp('recorded_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wm_activity_logs');
        Schema::dropIfExists('template_bots');
        Schema::dropIfExists('message_bots');
        Schema::dropIfExists('whatsapp_templates');
    }
};
