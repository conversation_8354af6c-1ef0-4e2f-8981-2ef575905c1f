<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_templates', function (Blueprint $table) {
            $table->id();
            $table->integer('template_id'); // id from api
            $table->string('template_name');
            $table->string('language');
            $table->string('status');
            $table->string('category');
            $table->string('header_data_format')->nullable();
            $table->text('header_data_text')->nullable();
            $table->integer('header_params_count')->nullable();
            $table->text('body_data');
            $table->integer('body_params_count')->nullable();
            $table->text('footer_data')->nullable();
            $table->integer('footer_params_count')->nullable();
            $table->text('buttons_data')->nullable();
            $table->timestamps();

            // Add index on template_id for better performance
            $table->index('template_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_templates');
    }
};
