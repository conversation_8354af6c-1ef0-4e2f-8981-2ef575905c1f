{"__meta": {"id": "01JZZHG4XSJXQKHKYGXYPY6W48", "datetime": "2025-07-12 04:44:17", "utime": **********.466513, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331456.83369, "end": **********.466524, "duration": 0.6328339576721191, "duration_str": "633ms", "measures": [{"label": "Booting", "start": 1752331456.83369, "relative_start": 0, "end": **********.384135, "relative_end": **********.384135, "duration": 0.****************, "duration_str": "550ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.384143, "relative_start": 0.****************, "end": **********.466525, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "82.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.398488, "relative_start": 0.**************, "end": **********.400913, "relative_end": **********.400913, "duration": 0.002424955368041992, "duration_str": "2.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.chat.manage-canned-reply", "start": **********.435478, "relative_start": 0.****************, "end": **********.435478, "relative_end": **********.435478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f6087304347b91d5b52dd9ef7bce9ed", "start": **********.440156, "relative_start": 0.****************, "end": **********.440156, "relative_end": **********.440156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary", "start": **********.441009, "relative_start": 0.6073191165924072, "end": **********.441009, "relative_end": **********.441009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.44158, "relative_start": 0.6078901290893555, "end": **********.44158, "relative_end": **********.44158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c72afa2d57b50a384b65435470599743", "start": **********.442724, "relative_start": 0.6090340614318848, "end": **********.442724, "relative_end": **********.442724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da8aafad19db3bbc97d2eb2356d7d4d9", "start": **********.444054, "relative_start": 0.6103639602661133, "end": **********.444054, "relative_end": **********.444054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary", "start": **********.444326, "relative_start": 0.6106359958648682, "end": **********.444326, "relative_end": **********.444326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.44469, "relative_start": 0.6110000610351562, "end": **********.44469, "relative_end": **********.44469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.445164, "relative_start": 0.6114740371704102, "end": **********.445164, "relative_end": **********.445164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.44661, "relative_start": 0.6129200458526611, "end": **********.44661, "relative_end": **********.44661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.447099, "relative_start": 0.6134090423583984, "end": **********.447099, "relative_end": **********.447099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.447553, "relative_start": 0.6138629913330078, "end": **********.447553, "relative_end": **********.447553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.448464, "relative_start": 0.6147739887237549, "end": **********.448464, "relative_end": **********.448464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.textarea", "start": **********.448937, "relative_start": 0.6152470111846924, "end": **********.448937, "relative_end": **********.448937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.449528, "relative_start": 0.6158380508422852, "end": **********.449528, "relative_end": **********.449528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.450546, "relative_start": 0.6168560981750488, "end": **********.450546, "relative_end": **********.450546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.451047, "relative_start": 0.6173570156097412, "end": **********.451047, "relative_end": **********.451047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.loading-button", "start": **********.452264, "relative_start": 0.6185741424560547, "end": **********.452264, "relative_end": **********.452264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83f96136a4af4da91c50b6760ff72460", "start": **********.453144, "relative_start": 0.6194541454315186, "end": **********.453144, "relative_end": **********.453144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.modal.custom-modal", "start": **********.453433, "relative_start": 0.6197431087493896, "end": **********.453433, "relative_end": **********.453433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.cancel-button", "start": **********.456488, "relative_start": 0.622797966003418, "end": **********.456488, "relative_end": **********.456488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.delete-button", "start": **********.457493, "relative_start": 0.6238031387329102, "end": **********.457493, "relative_end": **********.457493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.modal.confirm-box", "start": **********.457875, "relative_start": 0.6241850852966309, "end": **********.457875, "relative_end": **********.457875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a8d9e8ed5d18da4324b870627e9cca5b", "start": **********.459084, "relative_start": 0.6253941059112549, "end": **********.459084, "relative_end": **********.459084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.463826, "relative_start": 0.6301360130310059, "end": **********.46462, "relative_end": **********.46462, "duration": 0.0007941722869873047, "duration_str": "794μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 37157472, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 24, "nb_templates": 24, "templates": [{"name": "livewire.admin.chat.manage-canned-reply", "param_count": null, "params": [], "start": **********.435438, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/chat/manage-canned-reply.blade.phplivewire.admin.chat.manage-canned-reply", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fchat%2Fmanage-canned-reply.blade.php&line=1", "ajax": false, "filename": "manage-canned-reply.blade.php", "line": "?"}}, {"name": "__components::1f6087304347b91d5b52dd9ef7bce9ed", "param_count": null, "params": [], "start": **********.440123, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/1f6087304347b91d5b52dd9ef7bce9ed.blade.php__components::1f6087304347b91d5b52dd9ef7bce9ed", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F1f6087304347b91d5b52dd9ef7bce9ed.blade.php&line=1", "ajax": false, "filename": "1f6087304347b91d5b52dd9ef7bce9ed.blade.php", "line": "?"}}, {"name": "components.button.primary", "param_count": null, "params": [], "start": **********.440976, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary.blade.phpcomponents.button.primary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary.blade.php&line=1", "ajax": false, "filename": "primary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.441548, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "__components::c72afa2d57b50a384b65435470599743", "param_count": null, "params": [], "start": **********.442669, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c72afa2d57b50a384b65435470599743.blade.php__components::c72afa2d57b50a384b65435470599743", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc72afa2d57b50a384b65435470599743.blade.php&line=1", "ajax": false, "filename": "c72afa2d57b50a384b65435470599743.blade.php", "line": "?"}}, {"name": "__components::da8aafad19db3bbc97d2eb2356d7d4d9", "param_count": null, "params": [], "start": **********.444005, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/da8aafad19db3bbc97d2eb2356d7d4d9.blade.php__components::da8aafad19db3bbc97d2eb2356d7d4d9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fda8aafad19db3bbc97d2eb2356d7d4d9.blade.php&line=1", "ajax": false, "filename": "da8aafad19db3bbc97d2eb2356d7d4d9.blade.php", "line": "?"}}, {"name": "components.button.primary", "param_count": null, "params": [], "start": **********.444295, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary.blade.phpcomponents.button.primary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary.blade.php&line=1", "ajax": false, "filename": "primary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.444658, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.card", "param_count": null, "params": [], "start": **********.445133, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "components.label", "param_count": null, "params": [], "start": **********.446578, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "components.input", "param_count": null, "params": [], "start": **********.447068, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.447522, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.label", "param_count": null, "params": [], "start": **********.448428, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "components.textarea", "param_count": null, "params": [], "start": **********.448905, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/textarea.blade.phpcomponents.textarea", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.44948, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.button.secondary", "param_count": null, "params": [], "start": **********.450513, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/secondary.blade.phpcomponents.button.secondary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fsecondary.blade.php&line=1", "ajax": false, "filename": "secondary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.451015, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.button.loading-button", "param_count": null, "params": [], "start": **********.452194, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/loading-button.blade.phpcomponents.button.loading-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Floading-button.blade.php&line=1", "ajax": false, "filename": "loading-button.blade.php", "line": "?"}}, {"name": "__components::83f96136a4af4da91c50b6760ff72460", "param_count": null, "params": [], "start": **********.453113, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83f96136a4af4da91c50b6760ff72460.blade.php__components::83f96136a4af4da91c50b6760ff72460", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83f96136a4af4da91c50b6760ff72460.blade.php&line=1", "ajax": false, "filename": "83f96136a4af4da91c50b6760ff72460.blade.php", "line": "?"}}, {"name": "components.modal.custom-modal", "param_count": null, "params": [], "start": **********.4534, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/modal/custom-modal.blade.phpcomponents.modal.custom-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fcustom-modal.blade.php&line=1", "ajax": false, "filename": "custom-modal.blade.php", "line": "?"}}, {"name": "components.button.cancel-button", "param_count": null, "params": [], "start": **********.456455, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/cancel-button.blade.phpcomponents.button.cancel-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fcancel-button.blade.php&line=1", "ajax": false, "filename": "cancel-button.blade.php", "line": "?"}}, {"name": "components.button.delete-button", "param_count": null, "params": [], "start": **********.45746, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/delete-button.blade.phpcomponents.button.delete-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fdelete-button.blade.php&line=1", "ajax": false, "filename": "delete-button.blade.php", "line": "?"}}, {"name": "components.modal.confirm-box", "param_count": null, "params": [], "start": **********.457843, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/modal/confirm-box.blade.phpcomponents.modal.confirm-box", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm-box.blade.php&line=1", "ajax": false, "filename": "confirm-box.blade.php", "line": "?"}}, {"name": "__components::a8d9e8ed5d18da4324b870627e9cca5b", "param_count": null, "params": [], "start": **********.459052, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/a8d9e8ed5d18da4324b870627e9cca5b.blade.php__components::a8d9e8ed5d18da4324b870627e9cca5b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fa8d9e8ed5d18da4324b870627e9cca5b.blade.php&line=1", "ajax": false, "filename": "a8d9e8ed5d18da4324b870627e9cca5b.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0016099999999999999, "accumulated_duration_str": "1.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.403375, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 26.087}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.416631, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 26.087, "width_percent": 30.435}, {"sql": "select * from `canned_replies` where `canned_replies`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/Admin/Chat/ManageCannedReply.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Chat\\ManageCannedReply.php", "line": 98}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.429169, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ManageCannedReply.php:98", "source": {"index": 21, "namespace": null, "name": "app/Livewire/Admin/Chat/ManageCannedReply.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Chat\\ManageCannedReply.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FChat%2FManageCannedReply.php&line=98", "ajax": false, "filename": "ManageCannedReply.php", "line": "98"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 56.522, "width_percent": 43.478}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\CannedReply": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FCannedReply.php&line=1", "ajax": false, "filename": "CannedReply.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": {"admin.chat.manage-canned-reply #J6ljbhFOJaCr6S9t7Xsq": "array:4 [\n  \"data\" => array:5 [\n    \"canned\" => App\\Models\\CannedReply {#1003\n      #connection: \"mysql\"\n      #table: \"canned_replies\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:7 [\n        \"id\" => 1\n        \"title\" => \"Welcome Message\"\n        \"description\" => \"Hello! Welcome to our WhatsApp support. How can I assist you today?\"\n        \"is_public\" => 1\n        \"added_from\" => 1\n        \"created_at\" => \"2025-07-12 04:31:46\"\n        \"updated_at\" => \"2025-07-12 04:31:46\"\n      ]\n      #original: array:7 [\n        \"id\" => 1\n        \"title\" => \"Welcome Message\"\n        \"description\" => \"Hello! Welcome to our WhatsApp support. How can I assist you today?\"\n        \"is_public\" => 1\n        \"added_from\" => 1\n        \"created_at\" => \"2025-07-12 04:31:46\"\n        \"updated_at\" => \"2025-07-12 04:31:46\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:4 [\n        0 => \"title\"\n        1 => \"description\"\n        2 => \"is_public\"\n        3 => \"added_from\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"showCannedModal\" => true\n    \"confirmingDeletion\" => false\n    \"canned_id\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.chat.manage-canned-reply\"\n  \"component\" => \"App\\Livewire\\Admin\\Chat\\ManageCannedReply\"\n  \"id\" => \"J6ljbhFOJaCr6S9t7Xsq\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "633ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1552905061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1552905061\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-139576306 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"532 characters\">{&quot;data&quot;:{&quot;canned&quot;:[{&quot;title&quot;:null,&quot;description&quot;:null},{&quot;class&quot;:&quot;App\\\\Models\\\\CannedReply&quot;,&quot;connection&quot;:null,&quot;s&quot;:&quot;elmdl&quot;}],&quot;showCannedModal&quot;:false,&quot;confirmingDeletion&quot;:false,&quot;canned_id&quot;:null,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;J6ljbhFOJaCr6S9t7Xsq&quot;,&quot;name&quot;:&quot;admin.chat.manage-canned-reply&quot;,&quot;path&quot;:&quot;admin\\/canned-reply&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-3170399155-0&quot;:[&quot;div&quot;,&quot;kQveuSlJcb6pbklTtmY8&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b0b73981159b016b1056744772237b1920da3a576b7dcb03ca9de5a1a5c09c74&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__dispatch</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">editCannedPage</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>cannedId</span>\" => <span class=sf-dump-num>1</span>\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139576306\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1893225847 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">791</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/canned-reply</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjFLUEFZOEtubEtONnBoeGZsclhhSEE9PSIsInZhbHVlIjoiSFkvaHdqdzdDdHg0NnZRNU9BU0lJK1JTMC9yWWdlOEVXY2RkelFLVUtDd2g2bG5yOGVpaVRFaTJFUzNmcFExYTJRMDNwUDNCOTVSNGNEVDExckFUcEN2cUFzd1h6Y0UrVGVEWnVIaWRvbTkvSmJQUUxEdG9tam55MEQwZjI4TUUiLCJtYWMiOiIzZTBlMDE2YmRkYmE5MmZjYjlhZTAyNzJjZmIwMDU1ZTNmOWYzZWE4NGMyY2E3YWI5MTY2ZTVmMTE0ZDI1ZDVhIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkRRZ0xJcDhodTlSKy9TSGIyQm1jZlE9PSIsInZhbHVlIjoidTRxMnpsNEhLYVIrQ2FQbzNIalNnelg3WGpoQlVheCtCMWdIUTlXK1RjY2szSmZlOHA4Qk5ncUo0RXA1c1lWMUlKL3VCT3hIdHhxSy9RMGFlS1JuTTY1NUNId1hWTnNBWnVsUlVGTEsrWDZ6NkxHZ0NQZXFOOEswbHF4c1lTK1giLCJtYWMiOiI2MGM5NjQxY2RiYmIwZjY0NzA3OWI1NmE0Y2JmNTA0YWZkYWFlMDk3NzMyZWY2MWVjMzY5ZmEzMDg0ZjI3YWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893225847\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-796939265 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-796939265\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-117814164 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:44:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-117814164\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2080545228 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/canned-reply</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080545228\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}