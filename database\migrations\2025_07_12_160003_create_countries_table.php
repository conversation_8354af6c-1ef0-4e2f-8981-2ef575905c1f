<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('iso2', 2);
            $table->string('short_name');
            $table->string('long_name');
            $table->string('iso3', 3);
            $table->string('numcode', 3);
            $table->enum('un_member', ['yes', 'no'])->default('yes');
            $table->string('calling_code');
            $table->string('cctld');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};
