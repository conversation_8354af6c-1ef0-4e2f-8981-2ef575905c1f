{"__meta": {"id": "01JZZHEJB1M5D2N6XP7ERKCDG6", "datetime": "2025-07-12 04:43:25", "utime": **********.665815, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331404.9688, "end": **********.665825, "duration": 0.6970248222351074, "duration_str": "697ms", "measures": [{"label": "Booting", "start": 1752331404.9688, "relative_start": 0, "end": **********.548035, "relative_end": **********.548035, "duration": 0.****************, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.548043, "relative_start": 0.****************, "end": **********.665827, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.562856, "relative_start": 0.****************, "end": **********.565403, "relative_end": **********.565403, "duration": 0.002547025680541992, "duration_str": "2.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.contact.manage-source", "start": **********.632711, "relative_start": 0.****************, "end": **********.632711, "relative_end": **********.632711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1f6087304347b91d5b52dd9ef7bce9ed", "start": **********.639121, "relative_start": 0.***************, "end": **********.639121, "relative_end": **********.639121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary", "start": **********.640947, "relative_start": 0.6721470355987549, "end": **********.640947, "relative_end": **********.640947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.641656, "relative_start": 0.6728558540344238, "end": **********.641656, "relative_end": **********.641656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c72afa2d57b50a384b65435470599743", "start": **********.642893, "relative_start": 0.6740930080413818, "end": **********.642893, "relative_end": **********.642893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::da8aafad19db3bbc97d2eb2356d7d4d9", "start": **********.644256, "relative_start": 0.6754560470581055, "end": **********.644256, "relative_end": **********.644256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary", "start": **********.644579, "relative_start": 0.675778865814209, "end": **********.644579, "relative_end": **********.644579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.644991, "relative_start": 0.6761908531188965, "end": **********.644991, "relative_end": **********.644991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.645493, "relative_start": 0.6766929626464844, "end": **********.645493, "relative_end": **********.645493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.647311, "relative_start": 0.6785109043121338, "end": **********.647311, "relative_end": **********.647311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.647824, "relative_start": 0.6790239810943604, "end": **********.647824, "relative_end": **********.647824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.648328, "relative_start": 0.679527997970581, "end": **********.648328, "relative_end": **********.648328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.649338, "relative_start": 0.6805379390716553, "end": **********.649338, "relative_end": **********.649338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.64983, "relative_start": 0.6810300350189209, "end": **********.64983, "relative_end": **********.64983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.loading-button", "start": **********.650996, "relative_start": 0.6821959018707275, "end": **********.650996, "relative_end": **********.650996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83f96136a4af4da91c50b6760ff72460", "start": **********.654027, "relative_start": 0.6852269172668457, "end": **********.654027, "relative_end": **********.654027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.modal.custom-modal", "start": **********.65474, "relative_start": 0.6859400272369385, "end": **********.65474, "relative_end": **********.65474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.cancel-button", "start": **********.659125, "relative_start": 0.6903250217437744, "end": **********.659125, "relative_end": **********.659125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.delete-button", "start": **********.660056, "relative_start": 0.691256046295166, "end": **********.660056, "relative_end": **********.660056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.modal.confirm-box", "start": **********.660425, "relative_start": 0.6916248798370361, "end": **********.660425, "relative_end": **********.660425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a8d9e8ed5d18da4324b870627e9cca5b", "start": **********.661635, "relative_start": 0.6928348541259766, "end": **********.661635, "relative_end": **********.661635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.663467, "relative_start": 0.694666862487793, "end": **********.664223, "relative_end": **********.664223, "duration": 0.0007560253143310547, "duration_str": "756μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 38791872, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 21, "nb_templates": 21, "templates": [{"name": "livewire.admin.contact.manage-source", "param_count": null, "params": [], "start": **********.632669, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/contact/manage-source.blade.phplivewire.admin.contact.manage-source", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fcontact%2Fmanage-source.blade.php&line=1", "ajax": false, "filename": "manage-source.blade.php", "line": "?"}}, {"name": "__components::1f6087304347b91d5b52dd9ef7bce9ed", "param_count": null, "params": [], "start": **********.639086, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/1f6087304347b91d5b52dd9ef7bce9ed.blade.php__components::1f6087304347b91d5b52dd9ef7bce9ed", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F1f6087304347b91d5b52dd9ef7bce9ed.blade.php&line=1", "ajax": false, "filename": "1f6087304347b91d5b52dd9ef7bce9ed.blade.php", "line": "?"}}, {"name": "components.button.primary", "param_count": null, "params": [], "start": **********.64091, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary.blade.phpcomponents.button.primary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary.blade.php&line=1", "ajax": false, "filename": "primary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.641624, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "__components::c72afa2d57b50a384b65435470599743", "param_count": null, "params": [], "start": **********.642861, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c72afa2d57b50a384b65435470599743.blade.php__components::c72afa2d57b50a384b65435470599743", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc72afa2d57b50a384b65435470599743.blade.php&line=1", "ajax": false, "filename": "c72afa2d57b50a384b65435470599743.blade.php", "line": "?"}}, {"name": "__components::da8aafad19db3bbc97d2eb2356d7d4d9", "param_count": null, "params": [], "start": **********.644224, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/da8aafad19db3bbc97d2eb2356d7d4d9.blade.php__components::da8aafad19db3bbc97d2eb2356d7d4d9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fda8aafad19db3bbc97d2eb2356d7d4d9.blade.php&line=1", "ajax": false, "filename": "da8aafad19db3bbc97d2eb2356d7d4d9.blade.php", "line": "?"}}, {"name": "components.button.primary", "param_count": null, "params": [], "start": **********.644547, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary.blade.phpcomponents.button.primary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary.blade.php&line=1", "ajax": false, "filename": "primary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.644957, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.card", "param_count": null, "params": [], "start": **********.645461, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "components.label", "param_count": null, "params": [], "start": **********.647278, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "components.input", "param_count": null, "params": [], "start": **********.647792, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}}, {"name": "components.input-error", "param_count": null, "params": [], "start": **********.648295, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}}, {"name": "components.button.secondary", "param_count": null, "params": [], "start": **********.649305, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/secondary.blade.phpcomponents.button.secondary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fsecondary.blade.php&line=1", "ajax": false, "filename": "secondary.blade.php", "line": "?"}}, {"name": "components.button", "param_count": null, "params": [], "start": **********.649798, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}}, {"name": "components.button.loading-button", "param_count": null, "params": [], "start": **********.650949, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/loading-button.blade.phpcomponents.button.loading-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Floading-button.blade.php&line=1", "ajax": false, "filename": "loading-button.blade.php", "line": "?"}}, {"name": "__components::83f96136a4af4da91c50b6760ff72460", "param_count": null, "params": [], "start": **********.653961, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83f96136a4af4da91c50b6760ff72460.blade.php__components::83f96136a4af4da91c50b6760ff72460", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83f96136a4af4da91c50b6760ff72460.blade.php&line=1", "ajax": false, "filename": "83f96136a4af4da91c50b6760ff72460.blade.php", "line": "?"}}, {"name": "components.modal.custom-modal", "param_count": null, "params": [], "start": **********.654688, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/modal/custom-modal.blade.phpcomponents.modal.custom-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fcustom-modal.blade.php&line=1", "ajax": false, "filename": "custom-modal.blade.php", "line": "?"}}, {"name": "components.button.cancel-button", "param_count": null, "params": [], "start": **********.659089, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/cancel-button.blade.phpcomponents.button.cancel-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fcancel-button.blade.php&line=1", "ajax": false, "filename": "cancel-button.blade.php", "line": "?"}}, {"name": "components.button.delete-button", "param_count": null, "params": [], "start": **********.660022, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/delete-button.blade.phpcomponents.button.delete-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fdelete-button.blade.php&line=1", "ajax": false, "filename": "delete-button.blade.php", "line": "?"}}, {"name": "components.modal.confirm-box", "param_count": null, "params": [], "start": **********.660392, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/modal/confirm-box.blade.phpcomponents.modal.confirm-box", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm-box.blade.php&line=1", "ajax": false, "filename": "confirm-box.blade.php", "line": "?"}}, {"name": "__components::a8d9e8ed5d18da4324b870627e9cca5b", "param_count": null, "params": [], "start": **********.661552, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/a8d9e8ed5d18da4324b870627e9cca5b.blade.php__components::a8d9e8ed5d18da4324b870627e9cca5b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fa8d9e8ed5d18da4324b870627e9cca5b.blade.php&line=1", "ajax": false, "filename": "a8d9e8ed5d18da4324b870627e9cca5b.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00293, "accumulated_duration_str": "2.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.5689309, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 25.939}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5823119, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 25.939, "width_percent": 20.137}, {"sql": "select * from `sources` where `sources`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportLegacyModels/EloquentModelSynth.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\EloquentModelSynth.php", "line": 213}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportLegacyModels/EloquentModelSynth.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\EloquentModelSynth.php", "line": 72}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 214}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 192}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php", "line": 136}], "start": **********.5899322, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "EloquentModelSynth.php:213", "source": {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Features/SupportLegacyModels/EloquentModelSynth.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Features\\SupportLegacyModels\\EloquentModelSynth.php", "line": 213}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportLegacyModels%2FEloquentModelSynth.php&line=213", "ajax": false, "filename": "EloquentModelSynth.php", "line": "213"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 46.075, "width_percent": 33.788}, {"sql": "select count(*) as aggregate from `sources` where `name` = 'saas' and `id` <> '3'", "type": "query", "params": [], "bindings": ["saas", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.611099, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 79.863, "width_percent": 20.137}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Source": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FSource.php&line=1", "ajax": false, "filename": "Source.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": {"admin.contact.manage-source #HS8FAKJDqrXQDKU3Uvp1": "array:4 [\n  \"data\" => array:5 [\n    \"source\" => App\\Models\\Source {#2053\n      #connection: \"mysql\"\n      #table: \"sources\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 3\n        \"name\" => \"saas\"\n        \"created_at\" => \"2025-07-12 16:15:17\"\n        \"updated_at\" => \"2025-07-12 16:15:17\"\n      ]\n      #original: array:4 [\n        \"id\" => 3\n        \"name\" => \"saas\"\n        \"created_at\" => \"2025-07-12 16:15:17\"\n        \"updated_at\" => \"2025-07-12 16:15:17\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"name\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"showSourceModal\" => false\n    \"confirmingDeletion\" => false\n    \"source_id\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.contact.manage-source\"\n  \"component\" => \"App\\Livewire\\Admin\\Contact\\ManageSource\"\n  \"id\" => \"HS8FAKJDqrXQDKU3Uvp1\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Contact\\ManageSource@save<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FContact%2FManageSource.php&line=56\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FContact%2FManageSource.php&line=56\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Contact/ManageSource.php:56-87</a>", "middleware": "web", "duration": "698ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1769085224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1769085224\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-787579838 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"489 characters\">{&quot;data&quot;:{&quot;source&quot;:[{&quot;name&quot;:&quot;saas&quot;},{&quot;key&quot;:3,&quot;class&quot;:&quot;App\\\\Models\\\\Source&quot;,&quot;s&quot;:&quot;elmdl&quot;}],&quot;showSourceModal&quot;:true,&quot;confirmingDeletion&quot;:false,&quot;source_id&quot;:null,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;HS8FAKJDqrXQDKU3Uvp1&quot;,&quot;name&quot;:&quot;admin.contact.manage-source&quot;,&quot;path&quot;:&quot;admin\\/source&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:{&quot;lw-3315509130-0&quot;:[&quot;div&quot;,&quot;GAoqazw4GV9QZnP64lBo&quot;]},&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;1a027c62f5a0e79d4db112e464c875964f8f778d18ce19826b7b925207c2a1cd&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787579838\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2069841319 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">711</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/admin/source</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZ6MGpPZk10ejkreG5GOUZxR3ZrV3c9PSIsInZhbHVlIjoiYzBuSERHNXRzVktSWGNuT0RuNk40cDhPQVU1RU11MUJRMmhPbHJaNEs1TXhCcUNKM3IxRGpxQjR3SThnQXRBcTFaNDUrUzZyQ2pLbG8xTjZRbm0vTGxxYlozUi9EYUNrVTA5STNiNzNyQWxkZm83bmRNaGpUYk80eVJuNy9WbHoiLCJtYWMiOiIzNDI2NzEyYTRiYWE0M2M5MjVjNDhlYWFkYTc3NDE0ZjhkNjU3ZTIxNmNhYmExOTAzZmYxNjhmNDQ0NzZjNDBlIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlJmNnptNGFIQ3RQOExPeUVjelpVMnc9PSIsInZhbHVlIjoiUDhlSFFSQ0pFYkZnTWVzU0VzWjE3R1Q1dWNETE16TmdwUjdMb0F0bEFxTENra1hNWlRjTWp5SmwvVVE1b1VNQnRlYmZZcFE0Q05pYVoxVVNQMzRvM2pTM1BmUkRJd29UTnN6TjV3aXp6NjNWTWJCSzMzMXMwazc1bXJRTnVGem4iLCJtYWMiOiJjNzg5YTMyNjMzMjRlNTE4YjA4ZjhhZTQ2MGU4ODY2MWUyZjU1Zjg2ZDliMWFhNzZlNmMwZTUzMWIyODdlMTE4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069841319\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1695277427 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695277427\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-802690138 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:43:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802690138\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-839279737 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/admin/source</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839279737\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}