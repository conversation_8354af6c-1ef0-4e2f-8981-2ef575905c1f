<div class="relative" x-init="getObserver()">
     <?php $__env->slot('title', null, []); ?> 
        <?php echo e(t('contact')); ?>

     <?php $__env->endSlot(); ?>

    <div class="flex justify-start mb-3 px-5 lg:px-0 items-center gap-2">
        <!--[if BLOCK]><![endif]--><?php if(checkPermission('contact.create')): ?>
            <?php if (isset($component)) { $__componentOriginal79c47ff43af68680f280e55afc88fe59 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal79c47ff43af68680f280e55afc88fe59 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.primary','data' => ['wire:click' => 'createContact']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button.primary'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'createContact']); ?>
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-m-plus'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 mr-1']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?><?php echo e(t('new_contact_button')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal79c47ff43af68680f280e55afc88fe59)): ?>
<?php $attributes = $__attributesOriginal79c47ff43af68680f280e55afc88fe59; ?>
<?php unset($__attributesOriginal79c47ff43af68680f280e55afc88fe59); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal79c47ff43af68680f280e55afc88fe59)): ?>
<?php $component = $__componentOriginal79c47ff43af68680f280e55afc88fe59; ?>
<?php unset($__componentOriginal79c47ff43af68680f280e55afc88fe59); ?>
<?php endif; ?>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <!--[if BLOCK]><![endif]--><?php if(checkPermission('contact.bulk_import')): ?>
            <?php if (isset($component)) { $__componentOriginal79c47ff43af68680f280e55afc88fe59 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal79c47ff43af68680f280e55afc88fe59 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.primary','data' => ['wire:click' => 'importContact']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button.primary'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'importContact']); ?>
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-m-plus'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-4 h-4 mr-1']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?><?php echo e(t('import_contact')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal79c47ff43af68680f280e55afc88fe59)): ?>
<?php $attributes = $__attributesOriginal79c47ff43af68680f280e55afc88fe59; ?>
<?php unset($__attributesOriginal79c47ff43af68680f280e55afc88fe59); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal79c47ff43af68680f280e55afc88fe59)): ?>
<?php $component = $__componentOriginal79c47ff43af68680f280e55afc88fe59; ?>
<?php unset($__componentOriginal79c47ff43af68680f280e55afc88fe59); ?>
<?php endif; ?>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php if (isset($component)) { $__componentOriginal79c47ff43af68680f280e55afc88fe59 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal79c47ff43af68680f280e55afc88fe59 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.primary','data' => ['wire:click' => 'refreshTable','wire:loading.attr' => 'disabled','class' => 'relative flex items-center justify-center space-x-1 min-w-[120px]']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button.primary'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'refreshTable','wire:loading.attr' => 'disabled','class' => 'relative flex items-center justify-center space-x-1 min-w-[120px]']); ?>
            
            <div wire:loading.remove wire:target="refreshTable" class="flex items-center justify-center">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-arrow-path'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-4 w-4 mr-1']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                <span><?php echo e(t('refresh')); ?></span>
            </div>
            <div wire:loading wire:target="refreshTable">
                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-arrow-path'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-4 w-4 animate-spin']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal79c47ff43af68680f280e55afc88fe59)): ?>
<?php $attributes = $__attributesOriginal79c47ff43af68680f280e55afc88fe59; ?>
<?php unset($__attributesOriginal79c47ff43af68680f280e55afc88fe59); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal79c47ff43af68680f280e55afc88fe59)): ?>
<?php $component = $__componentOriginal79c47ff43af68680f280e55afc88fe59; ?>
<?php unset($__componentOriginal79c47ff43af68680f280e55afc88fe59); ?>
<?php endif; ?>
    </div>

    <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['class' => 'mx-4 lg:mx-0 rounded-lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mx-4 lg:mx-0 rounded-lg']); ?>
         <?php $__env->slot('content', null, []); ?> 
            <div class="lg:mt-0">
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('admin.table.contact-table', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1933250559-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>

    <!-- Delete Confirmation Modal -->
    <?php if (isset($component)) { $__componentOriginal79e52b819ddc9a73b4560c41923d18f7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal79e52b819ddc9a73b4560c41923d18f7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modal.confirm-box','data' => ['maxWidth' => 'lg','id' => 'delete-contact-modal','title' => ''.e(t('delete_contact_title')).'','wire:model.defer' => 'confirmingDeletion','description' => ''.e(t('delete_message')).' ']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modal.confirm-box'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['maxWidth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('lg'),'id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('delete-contact-modal'),'title' => ''.e(t('delete_contact_title')).'','wire:model.defer' => 'confirmingDeletion','description' => ''.e(t('delete_message')).' ']); ?>
        <div
            class="border-neutral-200 border-neutral-500/30 flex justify-end items-center sm:block space-x-3 bg-gray-100 dark:bg-gray-700 ">
            <?php if (isset($component)) { $__componentOriginalae37219fcdee25763f87d04348a96c20 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalae37219fcdee25763f87d04348a96c20 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.cancel-button','data' => ['wire:click' => '$set(\'confirmingDeletion\', false)','class' => '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button.cancel-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => '$set(\'confirmingDeletion\', false)','class' => '']); ?>
                <?php echo e(t('cancel')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalae37219fcdee25763f87d04348a96c20)): ?>
<?php $attributes = $__attributesOriginalae37219fcdee25763f87d04348a96c20; ?>
<?php unset($__attributesOriginalae37219fcdee25763f87d04348a96c20); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalae37219fcdee25763f87d04348a96c20)): ?>
<?php $component = $__componentOriginalae37219fcdee25763f87d04348a96c20; ?>
<?php unset($__componentOriginalae37219fcdee25763f87d04348a96c20); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginal254f851538d10c3f8455184bad85911f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal254f851538d10c3f8455184bad85911f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button.delete-button','data' => ['wire:click' => 'delete','class' => 'mt-3 sm:mt-0']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button.delete-button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:click' => 'delete','class' => 'mt-3 sm:mt-0']); ?>
                <?php echo e(t('delete')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal254f851538d10c3f8455184bad85911f)): ?>
<?php $attributes = $__attributesOriginal254f851538d10c3f8455184bad85911f; ?>
<?php unset($__attributesOriginal254f851538d10c3f8455184bad85911f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal254f851538d10c3f8455184bad85911f)): ?>
<?php $component = $__componentOriginal254f851538d10c3f8455184bad85911f; ?>
<?php unset($__componentOriginal254f851538d10c3f8455184bad85911f); ?>
<?php endif; ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal79e52b819ddc9a73b4560c41923d18f7)): ?>
<?php $attributes = $__attributesOriginal79e52b819ddc9a73b4560c41923d18f7; ?>
<?php unset($__attributesOriginal79e52b819ddc9a73b4560c41923d18f7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal79e52b819ddc9a73b4560c41923d18f7)): ?>
<?php $component = $__componentOriginal79e52b819ddc9a73b4560c41923d18f7; ?>
<?php unset($__componentOriginal79e52b819ddc9a73b4560c41923d18f7); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal8050579086355a6ef9a782e9f44d533f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8050579086355a6ef9a782e9f44d533f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.modal.custom-modal','data' => ['id' => 'view-contact-modal','maxWidth' => '5xl','wire:model.defer' => 'viewContactModal']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('modal.custom-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('view-contact-modal'),'maxWidth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('5xl'),'wire:model.defer' => 'viewContactModal']); ?>
        <div class="px-6 py-4 border-b border-neutral-200 dark:border-neutral-500/30 flex justify-between">
            <h1 class="text-xl font-medium text-slate-800 dark:text-slate-300">
                <?php echo e($contact ? "#{$contact->id} - {$contact->firstname} {$contact->lastname}" : t('contact_details')); ?>

            </h1>
            <button class="text-gray-500 hover:text-gray-700 text-2xl dark:hover:text-gray-300"
                wire:click="$set('viewContactModal', false)">
                &times;
            </button>
        </div>

        <!-- Tabs -->
        <div x-data="{ activeTab: 'profile' }">
            <div
                class="bg-gray-100 border-b border-neutral-200 dark:bg-gray-800 dark:border-neutral-500/30 gap-2 grid  grid-cols-3 mt-5 mx-5 px-2 py-1.5 rounded-md">

                <!-- Profile Tab -->
                <button class="px-4 py-2 text-sm font-medium rounded-md flex items-center justify-center space-x-2"
                    :class="activeTab === 'profile'
                        ?
                        'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400' :
                        'text-gray-600 dark:text-gray-300 hover:text-indigo-500 dark:hover:text-indigo-400'"
                    x-on:click="activeTab = 'profile'">
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-user'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'hidden md:inline w-6 h-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                    <span> <?php echo e(t('profile')); ?> </span>
                </button>

                <!-- Other Information Tab -->
                <button class="px-4 py-2 text-sm font-medium rounded-md flex items-center justify-center space-x-2"
                    :class="activeTab === 'other'
                        ?
                        'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400' :
                        'text-gray-600 dark:text-gray-300 hover:text-indigo-500 dark:hover:text-indigo-400'"
                    x-on:click="activeTab = 'other'">
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-information-circle'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'hidden md:inline w-6 h-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                    <span> <?php echo e(t('other_information')); ?> </span>
                </button>

                <!-- Notes Tab -->
                <button class="px-4 py-2 text-sm font-medium rounded-md flex items-center justify-center space-x-2"
                    :class="activeTab === 'notes'
                        ?
                        'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400' :
                        'text-gray-600 dark:text-gray-300 hover:text-indigo-500 dark:hover:text-indigo-400'"
                    x-on:click="activeTab = 'notes'">
                    <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-document-text'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'hidden md:inline w-6 h-6']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                    <span> <?php echo e(t('notes_title')); ?> </span>
                </button>
            </div>

            <div class="p-4">
                <div x-show="activeTab === 'profile'">
                    <div class="grid grid-cols-2 gap-x-8 gap-y-4 p-4 rounded-lg break-words">
                        <div class="space-y-4">
                            <!-- Name -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"><?php echo e(t('name')); ?></span>
                                <p class="text-sm text-slate-700 dark:text-slate-300 tesxt-wrap">
                                    <?php echo e($contact ? "{$contact->firstname} {$contact->lastname}" : '-'); ?>

                                </p>
                            </div>

                            <!-- Status -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('status')); ?>

                                </span>
                                <div>
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                                        style="background-color: <?php echo e($contact->status->color ?? '#ccc'); ?>20; color: <?php echo e($contact->status->color ?? '#333'); ?>;">
                                        <?php echo e($contact->status->name ?? '-'); ?>

                                    </span>
                                </div>
                            </div>

                            <!-- Source -->
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('source')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e($contact->source->name ?? '-'); ?></p>
                            </div>

                            <!-- Assigned -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('assigned')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e($contact && $contact->user ? "{$contact->user->firstname} {$contact->user->lastname}" : '-'); ?>

                                </p>
                            </div>

                            <!-- Company -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('company')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(isset($contact) && $contact->company ? $contact->company : '-'); ?>

                                </p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <!-- Type -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('type')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(ucfirst($contact->type ?? '-')); ?></p>
                            </div>

                            <!-- Email -->
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('email')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300 ">
                                    <?php echo e(isset($contact) && $contact->email ? $contact->email : '-'); ?></p>
                            </div>

                            <!-- Phone -->
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400"><?php echo e(t('phone')); ?></span>
                                <p>
                                    <a href='tel:<?php echo e($contact->phone ?? '-'); ?>' class="text-blue-600 text-sm">
                                        <?php echo e($contact->phone ?? '-'); ?>

                                    </a>
                                </p>
                            </div>

                            <!-- Website -->
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('website')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(isset($contact) && $contact->website ? $contact->website : '-'); ?></p>

                            </div>

                            <!-- Default Language -->
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400">
                                    <?php echo e(t('default_language')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e($contact->default_language ?? '-'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div x-show="activeTab === 'other'">
                    <div class="grid grid-cols-2 gap-x-8 gap-y-4 p-4 rounded-lg">
                        <div class="space-y-4">
                            <!-- City -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('city')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(isset($contact) && $contact->city ? $contact->city : '-'); ?>

                                </p>
                            </div>

                            <!-- State -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('state')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(isset($contact) && $contact->state ? $contact->state : '-'); ?>

                                </p>
                            </div>

                            <!-- Country -->
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('country')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(isset($contact) && $contact->country_name ? $contact->country_name : '-'); ?>

                                </p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <!-- Zip Code -->
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('zip_code')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(isset($contact) && $contact->zip ? $contact->zip : '-'); ?>

                                </p>
                            </div>
                            <div>
                                <span class=" text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('description')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300">
                                    <?php echo e(isset($contact) && $contact->description ? $contact->description : '-'); ?>

                                </p>
                            </div>

                            <!-- Address -->
                            <div>
                                <span class="text-sm text-slate-400 dark:text-slate-400"> <?php echo e(t('address')); ?>

                                </span>
                                <p class="text-sm text-slate-700 dark:text-slate-300 ">
                                    <?php echo e(isset($contact) && $contact->address ? $contact->address : '-'); ?>

                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div x-show="activeTab === 'notes'">
                    <div class="col-span-1">
                        <div>
                            <div
                                class="mt-4 relative px-4 h-80 overflow-y-auto scrollbar-thin scrollbar-track-gray-200 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800">
                                <ol class="relative border-s border-gray-300 dark:border-gray-700">
                                    <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $note): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <li class="mb-6 ms-4 relative">
                                            <div
                                                class="absolute w-2 h-2 bg-indigo-600 dark:bg-indigo-400 rounded-full -left-5 top-4">
                                            </div>

                                            <div
                                                class="flex-1 p-2 border-b border-gray-300 dark:border-gray-600 text-sm space-y-1">

                                                <span class="text-xs text-gray-500 dark:text-gray-400 block relative"
                                                    data-tippy-content="<?php echo e(format_date_time($note['created_at'])); ?>"
                                                    style="cursor: pointer; display: inline-block; text-decoration: underline dotted;">
                                                    <?php echo e(\Carbon\Carbon::parse($note['created_at'])->diffForHumans(['options' => \Carbon\Carbon::JUST_NOW])); ?>

                                                </span>
                                                <div class="flex justify-between items-start flex-nowrap">
                                                    <span class="text-gray-800 dark:text-gray-200 flex-1">
                                                        <?php echo e($note['notes_description']); ?>

                                                    </span>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <p class="text-gray-500 dark:text-gray-400 text-center">
                                            <?php echo e(t('no_notes_available')); ?> </p>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8050579086355a6ef9a782e9f44d533f)): ?>
<?php $attributes = $__attributesOriginal8050579086355a6ef9a782e9f44d533f; ?>
<?php unset($__attributesOriginal8050579086355a6ef9a782e9f44d533f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8050579086355a6ef9a782e9f44d533f)): ?>
<?php $component = $__componentOriginal8050579086355a6ef9a782e9f44d533f; ?>
<?php unset($__componentOriginal8050579086355a6ef9a782e9f44d533f); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\laragon\www\whats\resources\views/livewire/admin/contact/contact-list.blade.php ENDPATH**/ ?>