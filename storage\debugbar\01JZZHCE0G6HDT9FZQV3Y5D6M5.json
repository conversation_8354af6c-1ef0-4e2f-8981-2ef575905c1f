{"__meta": {"id": "01JZZHCE0G6HDT9FZQV3Y5D6M5", "datetime": "2025-07-12 04:42:15", "utime": **********.697589, "method": "GET", "uri": "/admin/roles/role/1", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331334.617522, "end": **********.697599, "duration": 1.0800769329071045, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1752331334.617522, "relative_start": 0, "end": **********.24322, "relative_end": **********.24322, "duration": 0.****************, "duration_str": "626ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.24323, "relative_start": 0.****************, "end": **********.697601, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "454ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.257871, "relative_start": 0.****************, "end": **********.260476, "relative_end": **********.260476, "duration": 0.0026051998138427734, "duration_str": "2.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.miscellaneous.role-creator", "start": **********.390572, "relative_start": 0.**************, "end": **********.390572, "relative_end": **********.390572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.396555, "relative_start": 0.****************, "end": **********.396555, "relative_end": **********.396555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.398531, "relative_start": 0.****************, "end": **********.398531, "relative_end": **********.398531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.39945, "relative_start": 0.7819280624389648, "end": **********.39945, "relative_end": **********.39945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6d2b0abe301d90865d3a9363cc05318", "start": **********.402698, "relative_start": 0.7851760387420654, "end": **********.402698, "relative_end": **********.402698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97dd5d9098ff5fff74d84335c08804fe", "start": **********.406587, "relative_start": 0.789064884185791, "end": **********.406587, "relative_end": **********.406587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.413652, "relative_start": 0.7961299419403076, "end": **********.413652, "relative_end": **********.413652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::91d90daf605ea08e8ad2731eba7b330d", "start": **********.415113, "relative_start": 0.797590970993042, "end": **********.415113, "relative_end": **********.415113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.table-base", "start": **********.438996, "relative_start": 0.8214740753173828, "end": **********.438996, "relative_end": **********.438996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header", "start": **********.440304, "relative_start": 0.822782039642334, "end": **********.440304, "relative_end": **********.440304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "start": **********.441119, "relative_start": 0.8235969543457031, "end": **********.441119, "relative_end": **********.441119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "start": **********.442284, "relative_start": 0.8247621059417725, "end": **********.442284, "relative_end": **********.442284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.loading", "start": **********.443182, "relative_start": 0.825659990310669, "end": **********.443182, "relative_end": **********.443182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.loading", "start": **********.444104, "relative_start": 0.8265819549560547, "end": **********.444104, "relative_end": **********.444104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.search", "start": **********.444808, "relative_start": 0.8272860050201416, "end": **********.444808, "relative_end": **********.444808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.search", "start": **********.445384, "relative_start": 0.827862024307251, "end": **********.445384, "relative_end": **********.445384, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "start": **********.446906, "relative_start": 0.8293840885162354, "end": **********.446906, "relative_end": **********.446906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "start": **********.447999, "relative_start": 0.8304769992828369, "end": **********.447999, "relative_end": **********.447999, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table", "start": **********.448664, "relative_start": 0.8311419486999512, "end": **********.448664, "relative_end": **********.448664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.449499, "relative_start": 0.8319768905639648, "end": **********.449499, "relative_end": **********.449499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.45022, "relative_start": 0.8326981067657471, "end": **********.45022, "relative_end": **********.45022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.451928, "relative_start": 0.8344058990478516, "end": **********.451928, "relative_end": **********.451928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.452405, "relative_start": 0.8348829746246338, "end": **********.452405, "relative_end": **********.452405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.452794, "relative_start": 0.8352720737457275, "end": **********.452794, "relative_end": **********.452794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.th-empty", "start": **********.453804, "relative_start": 0.8362820148468018, "end": **********.453804, "relative_end": **********.453804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.no-data-label", "start": **********.45524, "relative_start": 0.8377180099487305, "end": **********.45524, "relative_end": **********.45524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table-base", "start": **********.456013, "relative_start": 0.8384909629821777, "end": **********.456013, "relative_end": **********.456013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.footer", "start": **********.457633, "relative_start": 0.8401110172271729, "end": **********.457633, "relative_end": **********.457633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.down", "start": **********.459068, "relative_start": 0.8415460586547852, "end": **********.459068, "relative_end": **********.459068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.pagination", "start": **********.459952, "relative_start": 0.8424301147460938, "end": **********.459952, "relative_end": **********.459952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.466898, "relative_start": 0.8493759632110596, "end": **********.466898, "relative_end": **********.466898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.468503, "relative_start": 0.8509809970855713, "end": **********.468503, "relative_end": **********.468503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.469396, "relative_start": 0.8518741130828857, "end": **********.469396, "relative_end": **********.469396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.loading-button", "start": **********.471007, "relative_start": 0.853485107421875, "end": **********.471007, "relative_end": **********.471007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83f96136a4af4da91c50b6760ff72460", "start": **********.474632, "relative_start": 0.8571100234985352, "end": **********.474632, "relative_end": **********.474632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.4843, "relative_start": 0.8667778968811035, "end": **********.4843, "relative_end": **********.4843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.484761, "relative_start": 0.8672389984130859, "end": **********.484761, "relative_end": **********.484761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.backend.sidebar-navigation", "start": **********.513122, "relative_start": 0.8956000804901123, "end": **********.513122, "relative_end": **********.513122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::886d727e9dc72997f32a0f2f234fc84f", "start": **********.517635, "relative_start": 0.9001131057739258, "end": **********.517635, "relative_end": **********.517635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6b1898751c066ebcc10934b78f87cbf0", "start": **********.518603, "relative_start": 0.9010810852050781, "end": **********.518603, "relative_end": **********.518603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8438fc7bf4d7ce759e8eba19af63a3f1", "start": **********.520552, "relative_start": 0.9030299186706543, "end": **********.520552, "relative_end": **********.520552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56fe4b6f285fe1ec6232822f4d144f28", "start": **********.524663, "relative_start": 0.9071409702301025, "end": **********.524663, "relative_end": **********.524663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6c9caa75cd6f207df991e15d7cbf1c8", "start": **********.527408, "relative_start": 0.9098858833312988, "end": **********.527408, "relative_end": **********.527408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9cfed0aaa378ec61417e808f5b171d30", "start": **********.528986, "relative_start": 0.9114639759063721, "end": **********.528986, "relative_end": **********.528986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5096c547e1e4799b251226d536a755f", "start": **********.530419, "relative_start": 0.9128971099853516, "end": **********.530419, "relative_end": **********.530419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bbeeb09c8e60a84bbc3758cdf5155cc7", "start": **********.531994, "relative_start": 0.9144721031188965, "end": **********.531994, "relative_end": **********.531994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::782be997dc25a78b850884434c00f240", "start": **********.533522, "relative_start": 0.9159998893737793, "end": **********.533522, "relative_end": **********.533522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::871b569fe4b8bc1f493e29254eddc76e", "start": **********.534979, "relative_start": 0.917457103729248, "end": **********.534979, "relative_end": **********.534979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d9dc4902cc1ebcdd71cccc9ce512531a", "start": **********.536444, "relative_start": 0.918921947479248, "end": **********.536444, "relative_end": **********.536444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3c343ff09c2ca79e16f50878c798a182", "start": **********.543555, "relative_start": 0.9260330200195312, "end": **********.543555, "relative_end": **********.543555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::136011ccc3a87208e1917491717f9a4f", "start": **********.546201, "relative_start": 0.9286789894104004, "end": **********.546201, "relative_end": **********.546201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b37254cf00729b74237ee910ffb6596f", "start": **********.548126, "relative_start": 0.9306039810180664, "end": **********.548126, "relative_end": **********.548126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::93ef6be6fe1eb178e1feb898975a91ee", "start": **********.550057, "relative_start": 0.93253493309021, "end": **********.550057, "relative_end": **********.550057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fbf47fa86220ae7145c8d582e8c0eed4", "start": **********.55211, "relative_start": 0.9345879554748535, "end": **********.55211, "relative_end": **********.55211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bb3f94f23803809e73a8da21ef4efa8e", "start": **********.554064, "relative_start": 0.9365420341491699, "end": **********.554064, "relative_end": **********.554064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83003a3f2a3ed01b01e553b0ac0ad83c", "start": **********.558178, "relative_start": 0.9406559467315674, "end": **********.558178, "relative_end": **********.558178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6de57ecc36cdded0fb3b0d8da86d69ff", "start": **********.561126, "relative_start": 0.9436039924621582, "end": **********.561126, "relative_end": **********.561126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::220c546cb91e6a4599242f7bf0cef465", "start": **********.563527, "relative_start": 0.94600510597229, "end": **********.563527, "relative_end": **********.563527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f852be124bb2e2d20528b78218622ca5", "start": **********.565721, "relative_start": 0.9481990337371826, "end": **********.565721, "relative_end": **********.565721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7b66fb07888ac783788e2b26bcd125a8", "start": **********.567223, "relative_start": 0.9497010707855225, "end": **********.567223, "relative_end": **********.567223, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "start": **********.568765, "relative_start": 0.9512429237365723, "end": **********.568765, "relative_end": **********.568765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::886d727e9dc72997f32a0f2f234fc84f", "start": **********.570161, "relative_start": 0.952639102935791, "end": **********.570161, "relative_end": **********.570161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c949e4db934c39e884a3b8df4f399cf6", "start": **********.572118, "relative_start": 0.9545960426330566, "end": **********.572118, "relative_end": **********.572118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5bdb4266551740440506aff4bf77ee0d", "start": **********.575394, "relative_start": 0.9578719139099121, "end": **********.575394, "relative_end": **********.575394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5f16700bfd815b296f7f512eeaea5673", "start": **********.577822, "relative_start": 0.9602999687194824, "end": **********.577822, "relative_end": **********.577822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6c9caa75cd6f207df991e15d7cbf1c8", "start": **********.578878, "relative_start": 0.9613559246063232, "end": **********.578878, "relative_end": **********.578878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86e645ab8f001358369d5b4e60f4bf59", "start": **********.580239, "relative_start": 0.9627170562744141, "end": **********.580239, "relative_end": **********.580239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2aa3fa40c579d34617cbba7fa1f682ca", "start": **********.581711, "relative_start": 0.9641890525817871, "end": **********.581711, "relative_end": **********.581711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bbeeb09c8e60a84bbc3758cdf5155cc7", "start": **********.582725, "relative_start": 0.965203046798706, "end": **********.582725, "relative_end": **********.582725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::782be997dc25a78b850884434c00f240", "start": **********.583725, "relative_start": 0.966202974319458, "end": **********.583725, "relative_end": **********.583725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::871b569fe4b8bc1f493e29254eddc76e", "start": **********.584633, "relative_start": 0.9671111106872559, "end": **********.584633, "relative_end": **********.584633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d9dc4902cc1ebcdd71cccc9ce512531a", "start": **********.58548, "relative_start": 0.9679579734802246, "end": **********.58548, "relative_end": **********.58548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b5ca655d4335b87ed0d138a5d744f3d6", "start": **********.591736, "relative_start": 0.9742140769958496, "end": **********.591736, "relative_end": **********.591736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::136011ccc3a87208e1917491717f9a4f", "start": **********.594503, "relative_start": 0.9769809246063232, "end": **********.594503, "relative_end": **********.594503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7891d25efdd26b215062e0b170dbcab6", "start": **********.596447, "relative_start": 0.9789249897003174, "end": **********.596447, "relative_end": **********.596447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e1275e529df599477ba24a2029d9c4c", "start": **********.598281, "relative_start": 0.9807589054107666, "end": **********.598281, "relative_end": **********.598281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f754263ea240437bcce4902de59bc4b9", "start": **********.600101, "relative_start": 0.9825789928436279, "end": **********.600101, "relative_end": **********.600101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b1589f415f31dd93e57e18d0643b6093", "start": **********.601486, "relative_start": 0.9839639663696289, "end": **********.601486, "relative_end": **********.601486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::99c7a16739e2f632d30b83abd5fdcad9", "start": **********.603146, "relative_start": 0.9856240749359131, "end": **********.603146, "relative_end": **********.603146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::adfc97029070652f0e172ecbe3494c8b", "start": **********.606624, "relative_start": 0.9891018867492676, "end": **********.606624, "relative_end": **********.606624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8fdc46d28d72c53c63d9392f76f64223", "start": **********.610165, "relative_start": 0.9926431179046631, "end": **********.610165, "relative_end": **********.610165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c05a33e9b57f6d56c3e42853eed0304b", "start": **********.612354, "relative_start": 0.9948320388793945, "end": **********.612354, "relative_end": **********.612354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff2a50cf02e428f29948df9e0b706bc2", "start": **********.613873, "relative_start": 0.9963510036468506, "end": **********.613873, "relative_end": **********.613873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "start": **********.614784, "relative_start": 0.9972620010375977, "end": **********.614784, "relative_end": **********.614784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.backend.header-navigation", "start": **********.620785, "relative_start": 1.003262996673584, "end": **********.620785, "relative_end": **********.620785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ac8aa99487bb4cf2a4bcd65a29a3f04d", "start": **********.626236, "relative_start": 1.008713960647583, "end": **********.626236, "relative_end": **********.626236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::df7c6ab5de2b42f6cee2f41a9504d04c", "start": **********.6276, "relative_start": 1.010077953338623, "end": **********.6276, "relative_end": **********.6276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bbf9e785d4d46e59cc980886ec5758d7", "start": **********.628703, "relative_start": 1.011181116104126, "end": **********.628703, "relative_end": **********.628703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::914f269289845944af4ea7064df47b48", "start": **********.630429, "relative_start": 1.0129070281982422, "end": **********.630429, "relative_end": **********.630429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.631258, "relative_start": 1.0137360095977783, "end": **********.631258, "relative_end": **********.631258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::73f1fde11c4734cced4d836d3a092bec", "start": **********.632481, "relative_start": 1.0149590969085693, "end": **********.632481, "relative_end": **********.632481, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary-round", "start": **********.632826, "relative_start": 1.0153040885925293, "end": **********.632826, "relative_end": **********.632826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::18061f0a7de6cc38934967fcf0ab7119", "start": **********.634256, "relative_start": 1.0167338848114014, "end": **********.634256, "relative_end": **********.634256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::826d9ae0f124683ce39e8e5582533db2", "start": **********.635715, "relative_start": 1.018193006515503, "end": **********.635715, "relative_end": **********.635715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f43fb37d760babea44d38d2243b9db02", "start": **********.637274, "relative_start": 1.019752025604248, "end": **********.637274, "relative_end": **********.637274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6b40256b79ce902897968ff2c396eeb2", "start": **********.6412, "relative_start": 1.0236780643463135, "end": **********.6412, "relative_end": **********.6412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7dbf7b1d342ba2c17a858fb8b23129fb", "start": **********.644088, "relative_start": 1.0265660285949707, "end": **********.644088, "relative_end": **********.644088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8d22fb70c9977fca6e7c3a462d78936e", "start": **********.645843, "relative_start": 1.0283210277557373, "end": **********.645843, "relative_end": **********.645843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b45136778d9e1f97d0856c0b8bfe0ab6", "start": **********.650683, "relative_start": 1.033160924911499, "end": **********.650683, "relative_end": **********.650683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6c9caa75cd6f207df991e15d7cbf1c8", "start": **********.652062, "relative_start": 1.0345399379730225, "end": **********.652062, "relative_end": **********.652062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.653015, "relative_start": 1.0354928970336914, "end": **********.653015, "relative_end": **********.653015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.language-switcher", "start": **********.656063, "relative_start": 1.0385410785675049, "end": **********.656063, "relative_end": **********.656063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7d5927e3602df13b6cb94272364e60cd", "start": **********.658163, "relative_start": 1.0406410694122314, "end": **********.658163, "relative_end": **********.658163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary-round", "start": **********.659106, "relative_start": 1.0415840148925781, "end": **********.659106, "relative_end": **********.659106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.662, "relative_start": 1.044477939605713, "end": **********.662, "relative_end": **********.662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::626e3186dfddcc134144cee5faad3af0", "start": **********.664169, "relative_start": 1.046647071838379, "end": **********.664169, "relative_end": **********.664169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bf02a25af80fbc3a9220a1cbfec1ed84", "start": **********.665176, "relative_start": 1.0476539134979248, "end": **********.665176, "relative_end": **********.665176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4a422c51ce49d9d4b3edb4019ae706ba", "start": **********.666047, "relative_start": 1.048525094985962, "end": **********.666047, "relative_end": **********.666047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd9adf6608e0d55e77c26a59fa85b0a2", "start": **********.667142, "relative_start": 1.0496199131011963, "end": **********.667142, "relative_end": **********.667142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.668264, "relative_start": 1.0507419109344482, "end": **********.668264, "relative_end": **********.668264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c73f13ef25c37df445e040c2e298817c", "start": **********.669284, "relative_start": 1.0517621040344238, "end": **********.669284, "relative_end": **********.669284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.670138, "relative_start": 1.0526158809661865, "end": **********.670138, "relative_end": **********.670138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::66b7debda96562d7d5ec9a50db5fb253", "start": **********.672295, "relative_start": 1.0547730922698975, "end": **********.672295, "relative_end": **********.672295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.674616, "relative_start": 1.0570940971374512, "end": **********.674616, "relative_end": **********.674616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.674922, "relative_start": 1.0573999881744385, "end": **********.674922, "relative_end": **********.674922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.677805, "relative_start": 1.0602829456329346, "end": **********.677805, "relative_end": **********.677805, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.67868, "relative_start": 1.0611579418182373, "end": **********.67868, "relative_end": **********.67868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.678976, "relative_start": 1.0614540576934814, "end": **********.678976, "relative_end": **********.678976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.notification", "start": **********.681307, "relative_start": 1.0637850761413574, "end": **********.681307, "relative_end": **********.681307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e619de47f59c986e9f566638044a3ab7", "start": **********.683097, "relative_start": 1.0655748844146729, "end": **********.683097, "relative_end": **********.683097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f2b343610bcc0778e0f00f4bfe27f188", "start": **********.684115, "relative_start": 1.0665929317474365, "end": **********.684115, "relative_end": **********.684115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d5cde93dab074a387e37ef65b78febc", "start": **********.685086, "relative_start": 1.0675640106201172, "end": **********.685086, "relative_end": **********.685086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::10cb7a8570e3e511d44febea50de8e08", "start": **********.686045, "relative_start": 1.0685229301452637, "end": **********.686045, "relative_end": **********.686045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46553ac5b5a848738f4879c0717608ca", "start": **********.689211, "relative_start": 1.0716888904571533, "end": **********.689211, "relative_end": **********.689211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.695387, "relative_start": 1.0778648853302002, "end": **********.695557, "relative_end": **********.695557, "duration": 0.00017023086547851562, "duration_str": "170μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 40638160, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 124, "nb_templates": 124, "templates": [{"name": "1x livewire.admin.miscellaneous.role-creator", "param_count": null, "params": [], "start": **********.390489, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/miscellaneous/role-creator.blade.phplivewire.admin.miscellaneous.role-creator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fmiscellaneous%2Frole-creator.blade.php&line=1", "ajax": false, "filename": "role-creator.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.miscellaneous.role-creator"}, {"name": "1x components.label", "param_count": null, "params": [], "start": **********.396517, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.label"}, {"name": "1x components.input", "param_count": null, "params": [], "start": **********.398495, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.input"}, {"name": "1x components.input-error", "param_count": null, "params": [], "start": **********.399414, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.input-error"}, {"name": "1x __components::c6d2b0abe301d90865d3a9363cc05318", "param_count": null, "params": [], "start": **********.402662, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c6d2b0abe301d90865d3a9363cc05318.blade.php__components::c6d2b0abe301d90865d3a9363cc05318", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc6d2b0abe301d90865d3a9363cc05318.blade.php&line=1", "ajax": false, "filename": "c6d2b0abe301d90865d3a9363cc05318.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c6d2b0abe301d90865d3a9363cc05318"}, {"name": "1x __components::97dd5d9098ff5fff74d84335c08804fe", "param_count": null, "params": [], "start": **********.4065, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/97dd5d9098ff5fff74d84335c08804fe.blade.php__components::97dd5d9098ff5fff74d84335c08804fe", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F97dd5d9098ff5fff74d84335c08804fe.blade.php&line=1", "ajax": false, "filename": "97dd5d9098ff5fff74d84335c08804fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::97dd5d9098ff5fff74d84335c08804fe"}, {"name": "2x components.card", "param_count": null, "params": [], "start": **********.413611, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.card"}, {"name": "1x __components::91d90daf605ea08e8ad2731eba7b330d", "param_count": null, "params": [], "start": **********.415077, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/91d90daf605ea08e8ad2731eba7b330d.blade.php__components::91d90daf605ea08e8ad2731eba7b330d", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F91d90daf605ea08e8ad2731eba7b330d.blade.php&line=1", "ajax": false, "filename": "91d90daf605ea08e8ad2731eba7b330d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::91d90daf605ea08e8ad2731eba7b330d"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.table-base", "param_count": null, "params": [], "start": **********.438925, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/table-base.blade.phplivewire-powergrid::components.frameworks.tailwind.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header", "param_count": null, "params": [], "start": **********.440266, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header.blade.phplivewire-powergrid::components.frameworks.tailwind.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "param_count": null, "params": [], "start": **********.441085, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/toggle-columns.blade.phplivewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Ftoggle-columns.blade.php&line=1", "ajax": false, "filename": "toggle-columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.toggle-columns"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "param_count": null, "params": [], "start": **********.4422, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsoft-deletes.blade.php&line=1", "ajax": false, "filename": "soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.soft-deletes"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.loading", "param_count": null, "params": [], "start": **********.443145, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/loading.blade.phplivewire-powergrid::components.frameworks.tailwind.header.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.loading"}, {"name": "1x livewire-powergrid::components.icons.loading", "param_count": null, "params": [], "start": **********.444067, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/loading.blade.phplivewire-powergrid::components.icons.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.loading"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.search", "param_count": null, "params": [], "start": **********.444772, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/search.blade.phplivewire-powergrid::components.frameworks.tailwind.header.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.search"}, {"name": "1x livewire-powergrid::components.icons.search", "param_count": null, "params": [], "start": **********.44535, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/search.blade.phplivewire-powergrid::components.icons.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.search"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "param_count": null, "params": [], "start": **********.446871, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/enabled-filters.blade.phplivewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fenabled-filters.blade.php&line=1", "ajax": false, "filename": "enabled-filters.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.enabled-filters"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "param_count": null, "params": [], "start": **********.447887, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/message-soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fmessage-soft-deletes.blade.php&line=1", "ajax": false, "filename": "message-soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes"}, {"name": "1x livewire-powergrid::components.table", "param_count": null, "params": [], "start": **********.448631, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table.blade.phplivewire-powergrid::components.table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table"}, {"name": "2x livewire-powergrid::components.table.tr", "param_count": null, "params": [], "start": **********.449465, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/tr.blade.phplivewire-powergrid::components.table.tr", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Ftr.blade.php&line=1", "ajax": false, "filename": "tr.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.table.tr"}, {"name": "1x livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.450186, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.cols"}, {"name": "1x __components::6da562a044c365ee08fb55b6325b90a1", "param_count": null, "params": [], "start": **********.451897, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6da562a044c365ee08fb55b6325b90a1.blade.php__components::6da562a044c365ee08fb55b6325b90a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6da562a044c365ee08fb55b6325b90a1.blade.php&line=1", "ajax": false, "filename": "6da562a044c365ee08fb55b6325b90a1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6da562a044c365ee08fb55b6325b90a1"}, {"name": "1x livewire-powergrid::components.icons.chevron-up-down", "param_count": null, "params": [], "start": **********.45237, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-up-down.blade.phplivewire-powergrid::components.icons.chevron-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-up-down.blade.php&line=1", "ajax": false, "filename": "chevron-up-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.chevron-up-down"}, {"name": "1x livewire-powergrid::components.table.th-empty", "param_count": null, "params": [], "start": **********.453706, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/th-empty.blade.phplivewire-powergrid::components.table.th-empty", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fth-empty.blade.php&line=1", "ajax": false, "filename": "th-empty.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table.th-empty"}, {"name": "1x livewire-powergrid::components.table.no-data-label", "param_count": null, "params": [], "start": **********.455179, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/no-data-label.blade.phplivewire-powergrid::components.table.no-data-label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fno-data-label.blade.php&line=1", "ajax": false, "filename": "no-data-label.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table.no-data-label"}, {"name": "1x livewire-powergrid::components.table-base", "param_count": null, "params": [], "start": **********.455969, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table-base.blade.phplivewire-powergrid::components.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.footer", "param_count": null, "params": [], "start": **********.457591, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/footer.blade.phplivewire-powergrid::components.frameworks.tailwind.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.footer"}, {"name": "1x livewire-powergrid::components.icons.down", "param_count": null, "params": [], "start": **********.458991, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/down.blade.phplivewire-powergrid::components.icons.down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fdown.blade.php&line=1", "ajax": false, "filename": "down.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.down"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.pagination", "param_count": null, "params": [], "start": **********.459914, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/pagination.blade.phplivewire-powergrid::components.frameworks.tailwind.pagination", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.pagination"}, {"name": "1x components.button.secondary", "param_count": null, "params": [], "start": **********.468468, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/secondary.blade.phpcomponents.button.secondary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fsecondary.blade.php&line=1", "ajax": false, "filename": "secondary.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.button.secondary"}, {"name": "1x components.button", "param_count": null, "params": [], "start": **********.469364, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.button"}, {"name": "1x components.button.loading-button", "param_count": null, "params": [], "start": **********.470944, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/loading-button.blade.phpcomponents.button.loading-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Floading-button.blade.php&line=1", "ajax": false, "filename": "loading-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.button.loading-button"}, {"name": "1x __components::83f96136a4af4da91c50b6760ff72460", "param_count": null, "params": [], "start": **********.474591, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83f96136a4af4da91c50b6760ff72460.blade.php__components::83f96136a4af4da91c50b6760ff72460", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83f96136a4af4da91c50b6760ff72460.blade.php&line=1", "ajax": false, "filename": "83f96136a4af4da91c50b6760ff72460.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::83f96136a4af4da91c50b6760ff72460"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.484264, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": **********.484728, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x livewire.backend.sidebar-navigation", "param_count": null, "params": [], "start": **********.513083, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/backend/sidebar-navigation.blade.phplivewire.backend.sidebar-navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fbackend%2Fsidebar-navigation.blade.php&line=1", "ajax": false, "filename": "sidebar-navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.backend.sidebar-navigation"}, {"name": "2x __components::886d727e9dc72997f32a0f2f234fc84f", "param_count": null, "params": [], "start": **********.5176, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/886d727e9dc72997f32a0f2f234fc84f.blade.php__components::886d727e9dc72997f32a0f2f234fc84f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F886d727e9dc72997f32a0f2f234fc84f.blade.php&line=1", "ajax": false, "filename": "886d727e9dc72997f32a0f2f234fc84f.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::886d727e9dc72997f32a0f2f234fc84f"}, {"name": "1x __components::6b1898751c066ebcc10934b78f87cbf0", "param_count": null, "params": [], "start": **********.51857, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6b1898751c066ebcc10934b78f87cbf0.blade.php__components::6b1898751c066ebcc10934b78f87cbf0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6b1898751c066ebcc10934b78f87cbf0.blade.php&line=1", "ajax": false, "filename": "6b1898751c066ebcc10934b78f87cbf0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6b1898751c066ebcc10934b78f87cbf0"}, {"name": "1x __components::8438fc7bf4d7ce759e8eba19af63a3f1", "param_count": null, "params": [], "start": **********.520481, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/8438fc7bf4d7ce759e8eba19af63a3f1.blade.php__components::8438fc7bf4d7ce759e8eba19af63a3f1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F8438fc7bf4d7ce759e8eba19af63a3f1.blade.php&line=1", "ajax": false, "filename": "8438fc7bf4d7ce759e8eba19af63a3f1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8438fc7bf4d7ce759e8eba19af63a3f1"}, {"name": "1x __components::56fe4b6f285fe1ec6232822f4d144f28", "param_count": null, "params": [], "start": **********.524626, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/56fe4b6f285fe1ec6232822f4d144f28.blade.php__components::56fe4b6f285fe1ec6232822f4d144f28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F56fe4b6f285fe1ec6232822f4d144f28.blade.php&line=1", "ajax": false, "filename": "56fe4b6f285fe1ec6232822f4d144f28.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::56fe4b6f285fe1ec6232822f4d144f28"}, {"name": "3x __components::c6c9caa75cd6f207df991e15d7cbf1c8", "param_count": null, "params": [], "start": **********.527374, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c6c9caa75cd6f207df991e15d7cbf1c8.blade.php__components::c6c9caa75cd6f207df991e15d7cbf1c8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc6c9caa75cd6f207df991e15d7cbf1c8.blade.php&line=1", "ajax": false, "filename": "c6c9caa75cd6f207df991e15d7cbf1c8.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::c6c9caa75cd6f207df991e15d7cbf1c8"}, {"name": "1x __components::9cfed0aaa378ec61417e808f5b171d30", "param_count": null, "params": [], "start": **********.528954, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/9cfed0aaa378ec61417e808f5b171d30.blade.php__components::9cfed0aaa378ec61417e808f5b171d30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F9cfed0aaa378ec61417e808f5b171d30.blade.php&line=1", "ajax": false, "filename": "9cfed0aaa378ec61417e808f5b171d30.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9cfed0aaa378ec61417e808f5b171d30"}, {"name": "1x __components::c5096c547e1e4799b251226d536a755f", "param_count": null, "params": [], "start": **********.530386, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c5096c547e1e4799b251226d536a755f.blade.php__components::c5096c547e1e4799b251226d536a755f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc5096c547e1e4799b251226d536a755f.blade.php&line=1", "ajax": false, "filename": "c5096c547e1e4799b251226d536a755f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c5096c547e1e4799b251226d536a755f"}, {"name": "2x __components::bbeeb09c8e60a84bbc3758cdf5155cc7", "param_count": null, "params": [], "start": **********.531961, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bbeeb09c8e60a84bbc3758cdf5155cc7.blade.php__components::bbeeb09c8e60a84bbc3758cdf5155cc7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbbeeb09c8e60a84bbc3758cdf5155cc7.blade.php&line=1", "ajax": false, "filename": "bbeeb09c8e60a84bbc3758cdf5155cc7.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::bbeeb09c8e60a84bbc3758cdf5155cc7"}, {"name": "2x __components::782be997dc25a78b850884434c00f240", "param_count": null, "params": [], "start": **********.53349, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/782be997dc25a78b850884434c00f240.blade.php__components::782be997dc25a78b850884434c00f240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F782be997dc25a78b850884434c00f240.blade.php&line=1", "ajax": false, "filename": "782be997dc25a78b850884434c00f240.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::782be997dc25a78b850884434c00f240"}, {"name": "2x __components::871b569fe4b8bc1f493e29254eddc76e", "param_count": null, "params": [], "start": **********.534946, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/871b569fe4b8bc1f493e29254eddc76e.blade.php__components::871b569fe4b8bc1f493e29254eddc76e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F871b569fe4b8bc1f493e29254eddc76e.blade.php&line=1", "ajax": false, "filename": "871b569fe4b8bc1f493e29254eddc76e.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::871b569fe4b8bc1f493e29254eddc76e"}, {"name": "2x __components::d9dc4902cc1ebcdd71cccc9ce512531a", "param_count": null, "params": [], "start": **********.536412, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/d9dc4902cc1ebcdd71cccc9ce512531a.blade.php__components::d9dc4902cc1ebcdd71cccc9ce512531a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fd9dc4902cc1ebcdd71cccc9ce512531a.blade.php&line=1", "ajax": false, "filename": "d9dc4902cc1ebcdd71cccc9ce512531a.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d9dc4902cc1ebcdd71cccc9ce512531a"}, {"name": "1x __components::3c343ff09c2ca79e16f50878c798a182", "param_count": null, "params": [], "start": **********.543519, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/3c343ff09c2ca79e16f50878c798a182.blade.php__components::3c343ff09c2ca79e16f50878c798a182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F3c343ff09c2ca79e16f50878c798a182.blade.php&line=1", "ajax": false, "filename": "3c343ff09c2ca79e16f50878c798a182.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3c343ff09c2ca79e16f50878c798a182"}, {"name": "2x __components::136011ccc3a87208e1917491717f9a4f", "param_count": null, "params": [], "start": **********.546168, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/136011ccc3a87208e1917491717f9a4f.blade.php__components::136011ccc3a87208e1917491717f9a4f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F136011ccc3a87208e1917491717f9a4f.blade.php&line=1", "ajax": false, "filename": "136011ccc3a87208e1917491717f9a4f.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::136011ccc3a87208e1917491717f9a4f"}, {"name": "1x __components::b37254cf00729b74237ee910ffb6596f", "param_count": null, "params": [], "start": **********.548093, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b37254cf00729b74237ee910ffb6596f.blade.php__components::b37254cf00729b74237ee910ffb6596f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb37254cf00729b74237ee910ffb6596f.blade.php&line=1", "ajax": false, "filename": "b37254cf00729b74237ee910ffb6596f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b37254cf00729b74237ee910ffb6596f"}, {"name": "1x __components::93ef6be6fe1eb178e1feb898975a91ee", "param_count": null, "params": [], "start": **********.550024, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/93ef6be6fe1eb178e1feb898975a91ee.blade.php__components::93ef6be6fe1eb178e1feb898975a91ee", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F93ef6be6fe1eb178e1feb898975a91ee.blade.php&line=1", "ajax": false, "filename": "93ef6be6fe1eb178e1feb898975a91ee.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::93ef6be6fe1eb178e1feb898975a91ee"}, {"name": "1x __components::fbf47fa86220ae7145c8d582e8c0eed4", "param_count": null, "params": [], "start": **********.552077, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/fbf47fa86220ae7145c8d582e8c0eed4.blade.php__components::fbf47fa86220ae7145c8d582e8c0eed4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ffbf47fa86220ae7145c8d582e8c0eed4.blade.php&line=1", "ajax": false, "filename": "fbf47fa86220ae7145c8d582e8c0eed4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fbf47fa86220ae7145c8d582e8c0eed4"}, {"name": "1x __components::bb3f94f23803809e73a8da21ef4efa8e", "param_count": null, "params": [], "start": **********.553957, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bb3f94f23803809e73a8da21ef4efa8e.blade.php__components::bb3f94f23803809e73a8da21ef4efa8e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbb3f94f23803809e73a8da21ef4efa8e.blade.php&line=1", "ajax": false, "filename": "bb3f94f23803809e73a8da21ef4efa8e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bb3f94f23803809e73a8da21ef4efa8e"}, {"name": "1x __components::83003a3f2a3ed01b01e553b0ac0ad83c", "param_count": null, "params": [], "start": **********.558142, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83003a3f2a3ed01b01e553b0ac0ad83c.blade.php__components::83003a3f2a3ed01b01e553b0ac0ad83c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83003a3f2a3ed01b01e553b0ac0ad83c.blade.php&line=1", "ajax": false, "filename": "83003a3f2a3ed01b01e553b0ac0ad83c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::83003a3f2a3ed01b01e553b0ac0ad83c"}, {"name": "1x __components::6de57ecc36cdded0fb3b0d8da86d69ff", "param_count": null, "params": [], "start": **********.561075, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6de57ecc36cdded0fb3b0d8da86d69ff.blade.php__components::6de57ecc36cdded0fb3b0d8da86d69ff", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6de57ecc36cdded0fb3b0d8da86d69ff.blade.php&line=1", "ajax": false, "filename": "6de57ecc36cdded0fb3b0d8da86d69ff.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6de57ecc36cdded0fb3b0d8da86d69ff"}, {"name": "1x __components::220c546cb91e6a4599242f7bf0cef465", "param_count": null, "params": [], "start": **********.563494, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/220c546cb91e6a4599242f7bf0cef465.blade.php__components::220c546cb91e6a4599242f7bf0cef465", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F220c546cb91e6a4599242f7bf0cef465.blade.php&line=1", "ajax": false, "filename": "220c546cb91e6a4599242f7bf0cef465.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::220c546cb91e6a4599242f7bf0cef465"}, {"name": "1x __components::f852be124bb2e2d20528b78218622ca5", "param_count": null, "params": [], "start": **********.565685, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f852be124bb2e2d20528b78218622ca5.blade.php__components::f852be124bb2e2d20528b78218622ca5", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff852be124bb2e2d20528b78218622ca5.blade.php&line=1", "ajax": false, "filename": "f852be124bb2e2d20528b78218622ca5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f852be124bb2e2d20528b78218622ca5"}, {"name": "1x __components::7b66fb07888ac783788e2b26bcd125a8", "param_count": null, "params": [], "start": **********.567189, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7b66fb07888ac783788e2b26bcd125a8.blade.php__components::7b66fb07888ac783788e2b26bcd125a8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7b66fb07888ac783788e2b26bcd125a8.blade.php&line=1", "ajax": false, "filename": "7b66fb07888ac783788e2b26bcd125a8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7b66fb07888ac783788e2b26bcd125a8"}, {"name": "2x __components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "param_count": null, "params": [], "start": **********.568732, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4bd12cd2b54372ae8dc9e9d0ea19ceca.blade.php__components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4bd12cd2b54372ae8dc9e9d0ea19ceca.blade.php&line=1", "ajax": false, "filename": "4bd12cd2b54372ae8dc9e9d0ea19ceca.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::4bd12cd2b54372ae8dc9e9d0ea19ceca"}, {"name": "1x __components::c949e4db934c39e884a3b8df4f399cf6", "param_count": null, "params": [], "start": **********.572051, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c949e4db934c39e884a3b8df4f399cf6.blade.php__components::c949e4db934c39e884a3b8df4f399cf6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc949e4db934c39e884a3b8df4f399cf6.blade.php&line=1", "ajax": false, "filename": "c949e4db934c39e884a3b8df4f399cf6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c949e4db934c39e884a3b8df4f399cf6"}, {"name": "1x __components::5bdb4266551740440506aff4bf77ee0d", "param_count": null, "params": [], "start": **********.575309, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/5bdb4266551740440506aff4bf77ee0d.blade.php__components::5bdb4266551740440506aff4bf77ee0d", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F5bdb4266551740440506aff4bf77ee0d.blade.php&line=1", "ajax": false, "filename": "5bdb4266551740440506aff4bf77ee0d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5bdb4266551740440506aff4bf77ee0d"}, {"name": "1x __components::5f16700bfd815b296f7f512eeaea5673", "param_count": null, "params": [], "start": **********.577787, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/5f16700bfd815b296f7f512eeaea5673.blade.php__components::5f16700bfd815b296f7f512eeaea5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F5f16700bfd815b296f7f512eeaea5673.blade.php&line=1", "ajax": false, "filename": "5f16700bfd815b296f7f512eeaea5673.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5f16700bfd815b296f7f512eeaea5673"}, {"name": "1x __components::86e645ab8f001358369d5b4e60f4bf59", "param_count": null, "params": [], "start": **********.580206, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/86e645ab8f001358369d5b4e60f4bf59.blade.php__components::86e645ab8f001358369d5b4e60f4bf59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F86e645ab8f001358369d5b4e60f4bf59.blade.php&line=1", "ajax": false, "filename": "86e645ab8f001358369d5b4e60f4bf59.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::86e645ab8f001358369d5b4e60f4bf59"}, {"name": "1x __components::2aa3fa40c579d34617cbba7fa1f682ca", "param_count": null, "params": [], "start": **********.581678, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/2aa3fa40c579d34617cbba7fa1f682ca.blade.php__components::2aa3fa40c579d34617cbba7fa1f682ca", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F2aa3fa40c579d34617cbba7fa1f682ca.blade.php&line=1", "ajax": false, "filename": "2aa3fa40c579d34617cbba7fa1f682ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2aa3fa40c579d34617cbba7fa1f682ca"}, {"name": "1x __components::b5ca655d4335b87ed0d138a5d744f3d6", "param_count": null, "params": [], "start": **********.591698, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b5ca655d4335b87ed0d138a5d744f3d6.blade.php__components::b5ca655d4335b87ed0d138a5d744f3d6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb5ca655d4335b87ed0d138a5d744f3d6.blade.php&line=1", "ajax": false, "filename": "b5ca655d4335b87ed0d138a5d744f3d6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b5ca655d4335b87ed0d138a5d744f3d6"}, {"name": "1x __components::7891d25efdd26b215062e0b170dbcab6", "param_count": null, "params": [], "start": **********.596414, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7891d25efdd26b215062e0b170dbcab6.blade.php__components::7891d25efdd26b215062e0b170dbcab6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7891d25efdd26b215062e0b170dbcab6.blade.php&line=1", "ajax": false, "filename": "7891d25efdd26b215062e0b170dbcab6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7891d25efdd26b215062e0b170dbcab6"}, {"name": "1x __components::7e1275e529df599477ba24a2029d9c4c", "param_count": null, "params": [], "start": **********.598248, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7e1275e529df599477ba24a2029d9c4c.blade.php__components::7e1275e529df599477ba24a2029d9c4c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7e1275e529df599477ba24a2029d9c4c.blade.php&line=1", "ajax": false, "filename": "7e1275e529df599477ba24a2029d9c4c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e1275e529df599477ba24a2029d9c4c"}, {"name": "1x __components::f754263ea240437bcce4902de59bc4b9", "param_count": null, "params": [], "start": **********.600068, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f754263ea240437bcce4902de59bc4b9.blade.php__components::f754263ea240437bcce4902de59bc4b9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff754263ea240437bcce4902de59bc4b9.blade.php&line=1", "ajax": false, "filename": "f754263ea240437bcce4902de59bc4b9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f754263ea240437bcce4902de59bc4b9"}, {"name": "1x __components::b1589f415f31dd93e57e18d0643b6093", "param_count": null, "params": [], "start": **********.601453, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b1589f415f31dd93e57e18d0643b6093.blade.php__components::b1589f415f31dd93e57e18d0643b6093", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb1589f415f31dd93e57e18d0643b6093.blade.php&line=1", "ajax": false, "filename": "b1589f415f31dd93e57e18d0643b6093.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b1589f415f31dd93e57e18d0643b6093"}, {"name": "1x __components::99c7a16739e2f632d30b83abd5fdcad9", "param_count": null, "params": [], "start": **********.603111, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/99c7a16739e2f632d30b83abd5fdcad9.blade.php__components::99c7a16739e2f632d30b83abd5fdcad9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F99c7a16739e2f632d30b83abd5fdcad9.blade.php&line=1", "ajax": false, "filename": "99c7a16739e2f632d30b83abd5fdcad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::99c7a16739e2f632d30b83abd5fdcad9"}, {"name": "1x __components::adfc97029070652f0e172ecbe3494c8b", "param_count": null, "params": [], "start": **********.606566, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/adfc97029070652f0e172ecbe3494c8b.blade.php__components::adfc97029070652f0e172ecbe3494c8b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fadfc97029070652f0e172ecbe3494c8b.blade.php&line=1", "ajax": false, "filename": "adfc97029070652f0e172ecbe3494c8b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::adfc97029070652f0e172ecbe3494c8b"}, {"name": "1x __components::8fdc46d28d72c53c63d9392f76f64223", "param_count": null, "params": [], "start": **********.610126, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/8fdc46d28d72c53c63d9392f76f64223.blade.php__components::8fdc46d28d72c53c63d9392f76f64223", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F8fdc46d28d72c53c63d9392f76f64223.blade.php&line=1", "ajax": false, "filename": "8fdc46d28d72c53c63d9392f76f64223.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8fdc46d28d72c53c63d9392f76f64223"}, {"name": "1x __components::c05a33e9b57f6d56c3e42853eed0304b", "param_count": null, "params": [], "start": **********.61232, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c05a33e9b57f6d56c3e42853eed0304b.blade.php__components::c05a33e9b57f6d56c3e42853eed0304b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc05a33e9b57f6d56c3e42853eed0304b.blade.php&line=1", "ajax": false, "filename": "c05a33e9b57f6d56c3e42853eed0304b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c05a33e9b57f6d56c3e42853eed0304b"}, {"name": "1x __components::ff2a50cf02e428f29948df9e0b706bc2", "param_count": null, "params": [], "start": **********.61384, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ff2a50cf02e428f29948df9e0b706bc2.blade.php__components::ff2a50cf02e428f29948df9e0b706bc2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fff2a50cf02e428f29948df9e0b706bc2.blade.php&line=1", "ajax": false, "filename": "ff2a50cf02e428f29948df9e0b706bc2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff2a50cf02e428f29948df9e0b706bc2"}, {"name": "1x livewire.backend.header-navigation", "param_count": null, "params": [], "start": **********.620668, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/backend/header-navigation.blade.phplivewire.backend.header-navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fbackend%2Fheader-navigation.blade.php&line=1", "ajax": false, "filename": "header-navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.backend.header-navigation"}, {"name": "1x __components::ac8aa99487bb4cf2a4bcd65a29a3f04d", "param_count": null, "params": [], "start": **********.626199, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ac8aa99487bb4cf2a4bcd65a29a3f04d.blade.php__components::ac8aa99487bb4cf2a4bcd65a29a3f04d", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fac8aa99487bb4cf2a4bcd65a29a3f04d.blade.php&line=1", "ajax": false, "filename": "ac8aa99487bb4cf2a4bcd65a29a3f04d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ac8aa99487bb4cf2a4bcd65a29a3f04d"}, {"name": "1x __components::df7c6ab5de2b42f6cee2f41a9504d04c", "param_count": null, "params": [], "start": **********.627566, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/df7c6ab5de2b42f6cee2f41a9504d04c.blade.php__components::df7c6ab5de2b42f6cee2f41a9504d04c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fdf7c6ab5de2b42f6cee2f41a9504d04c.blade.php&line=1", "ajax": false, "filename": "df7c6ab5de2b42f6cee2f41a9504d04c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::df7c6ab5de2b42f6cee2f41a9504d04c"}, {"name": "1x __components::bbf9e785d4d46e59cc980886ec5758d7", "param_count": null, "params": [], "start": **********.62867, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bbf9e785d4d46e59cc980886ec5758d7.blade.php__components::bbf9e785d4d46e59cc980886ec5758d7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbbf9e785d4d46e59cc980886ec5758d7.blade.php&line=1", "ajax": false, "filename": "bbf9e785d4d46e59cc980886ec5758d7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bbf9e785d4d46e59cc980886ec5758d7"}, {"name": "1x __components::914f269289845944af4ea7064df47b48", "param_count": null, "params": [], "start": **********.630396, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/914f269289845944af4ea7064df47b48.blade.php__components::914f269289845944af4ea7064df47b48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F914f269289845944af4ea7064df47b48.blade.php&line=1", "ajax": false, "filename": "914f269289845944af4ea7064df47b48.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::914f269289845944af4ea7064df47b48"}, {"name": "5x components.dropdown", "param_count": null, "params": [], "start": **********.631224, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown.blade.phpcomponents.dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 5, "name_original": "components.dropdown"}, {"name": "1x __components::73f1fde11c4734cced4d836d3a092bec", "param_count": null, "params": [], "start": **********.632448, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/73f1fde11c4734cced4d836d3a092bec.blade.php__components::73f1fde11c4734cced4d836d3a092bec", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F73f1fde11c4734cced4d836d3a092bec.blade.php&line=1", "ajax": false, "filename": "73f1fde11c4734cced4d836d3a092bec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::73f1fde11c4734cced4d836d3a092bec"}, {"name": "2x components.button.primary-round", "param_count": null, "params": [], "start": **********.632793, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary-round.blade.phpcomponents.button.primary-round", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary-round.blade.php&line=1", "ajax": false, "filename": "primary-round.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.button.primary-round"}, {"name": "1x __components::18061f0a7de6cc38934967fcf0ab7119", "param_count": null, "params": [], "start": **********.634223, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/18061f0a7de6cc38934967fcf0ab7119.blade.php__components::18061f0a7de6cc38934967fcf0ab7119", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F18061f0a7de6cc38934967fcf0ab7119.blade.php&line=1", "ajax": false, "filename": "18061f0a7de6cc38934967fcf0ab7119.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::18061f0a7de6cc38934967fcf0ab7119"}, {"name": "1x __components::826d9ae0f124683ce39e8e5582533db2", "param_count": null, "params": [], "start": **********.635682, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/826d9ae0f124683ce39e8e5582533db2.blade.php__components::826d9ae0f124683ce39e8e5582533db2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F826d9ae0f124683ce39e8e5582533db2.blade.php&line=1", "ajax": false, "filename": "826d9ae0f124683ce39e8e5582533db2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::826d9ae0f124683ce39e8e5582533db2"}, {"name": "1x __components::f43fb37d760babea44d38d2243b9db02", "param_count": null, "params": [], "start": **********.637214, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f43fb37d760babea44d38d2243b9db02.blade.php__components::f43fb37d760babea44d38d2243b9db02", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff43fb37d760babea44d38d2243b9db02.blade.php&line=1", "ajax": false, "filename": "f43fb37d760babea44d38d2243b9db02.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f43fb37d760babea44d38d2243b9db02"}, {"name": "1x __components::6b40256b79ce902897968ff2c396eeb2", "param_count": null, "params": [], "start": **********.64116, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6b40256b79ce902897968ff2c396eeb2.blade.php__components::6b40256b79ce902897968ff2c396eeb2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6b40256b79ce902897968ff2c396eeb2.blade.php&line=1", "ajax": false, "filename": "6b40256b79ce902897968ff2c396eeb2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6b40256b79ce902897968ff2c396eeb2"}, {"name": "1x __components::7dbf7b1d342ba2c17a858fb8b23129fb", "param_count": null, "params": [], "start": **********.644054, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7dbf7b1d342ba2c17a858fb8b23129fb.blade.php__components::7dbf7b1d342ba2c17a858fb8b23129fb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7dbf7b1d342ba2c17a858fb8b23129fb.blade.php&line=1", "ajax": false, "filename": "7dbf7b1d342ba2c17a858fb8b23129fb.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7dbf7b1d342ba2c17a858fb8b23129fb"}, {"name": "1x __components::8d22fb70c9977fca6e7c3a462d78936e", "param_count": null, "params": [], "start": **********.645807, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/8d22fb70c9977fca6e7c3a462d78936e.blade.php__components::8d22fb70c9977fca6e7c3a462d78936e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F8d22fb70c9977fca6e7c3a462d78936e.blade.php&line=1", "ajax": false, "filename": "8d22fb70c9977fca6e7c3a462d78936e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8d22fb70c9977fca6e7c3a462d78936e"}, {"name": "1x __components::b45136778d9e1f97d0856c0b8bfe0ab6", "param_count": null, "params": [], "start": **********.650568, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b45136778d9e1f97d0856c0b8bfe0ab6.blade.php__components::b45136778d9e1f97d0856c0b8bfe0ab6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb45136778d9e1f97d0856c0b8bfe0ab6.blade.php&line=1", "ajax": false, "filename": "b45136778d9e1f97d0856c0b8bfe0ab6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b45136778d9e1f97d0856c0b8bfe0ab6"}, {"name": "1x livewire.language-switcher", "param_count": null, "params": [], "start": **********.655979, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/language-switcher.blade.phplivewire.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.language-switcher"}, {"name": "1x __components::7d5927e3602df13b6cb94272364e60cd", "param_count": null, "params": [], "start": **********.658121, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7d5927e3602df13b6cb94272364e60cd.blade.php__components::7d5927e3602df13b6cb94272364e60cd", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7d5927e3602df13b6cb94272364e60cd.blade.php&line=1", "ajax": false, "filename": "7d5927e3602df13b6cb94272364e60cd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7d5927e3602df13b6cb94272364e60cd"}, {"name": "1x __components::626e3186dfddcc134144cee5faad3af0", "param_count": null, "params": [], "start": **********.664134, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/626e3186dfddcc134144cee5faad3af0.blade.php__components::626e3186dfddcc134144cee5faad3af0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F626e3186dfddcc134144cee5faad3af0.blade.php&line=1", "ajax": false, "filename": "626e3186dfddcc134144cee5faad3af0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::626e3186dfddcc134144cee5faad3af0"}, {"name": "1x __components::bf02a25af80fbc3a9220a1cbfec1ed84", "param_count": null, "params": [], "start": **********.665142, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bf02a25af80fbc3a9220a1cbfec1ed84.blade.php__components::bf02a25af80fbc3a9220a1cbfec1ed84", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbf02a25af80fbc3a9220a1cbfec1ed84.blade.php&line=1", "ajax": false, "filename": "bf02a25af80fbc3a9220a1cbfec1ed84.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bf02a25af80fbc3a9220a1cbfec1ed84"}, {"name": "1x __components::4a422c51ce49d9d4b3edb4019ae706ba", "param_count": null, "params": [], "start": **********.666014, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4a422c51ce49d9d4b3edb4019ae706ba.blade.php__components::4a422c51ce49d9d4b3edb4019ae706ba", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4a422c51ce49d9d4b3edb4019ae706ba.blade.php&line=1", "ajax": false, "filename": "4a422c51ce49d9d4b3edb4019ae706ba.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4a422c51ce49d9d4b3edb4019ae706ba"}, {"name": "1x __components::bd9adf6608e0d55e77c26a59fa85b0a2", "param_count": null, "params": [], "start": **********.667109, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bd9adf6608e0d55e77c26a59fa85b0a2.blade.php__components::bd9adf6608e0d55e77c26a59fa85b0a2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbd9adf6608e0d55e77c26a59fa85b0a2.blade.php&line=1", "ajax": false, "filename": "bd9adf6608e0d55e77c26a59fa85b0a2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd9adf6608e0d55e77c26a59fa85b0a2"}, {"name": "5x components.dropdown-link", "param_count": null, "params": [], "start": **********.668229, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}, "render_count": 5, "name_original": "components.dropdown-link"}, {"name": "1x __components::c73f13ef25c37df445e040c2e298817c", "param_count": null, "params": [], "start": **********.669251, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c73f13ef25c37df445e040c2e298817c.blade.php__components::c73f13ef25c37df445e040c2e298817c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc73f13ef25c37df445e040c2e298817c.blade.php&line=1", "ajax": false, "filename": "c73f13ef25c37df445e040c2e298817c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c73f13ef25c37df445e040c2e298817c"}, {"name": "1x __components::66b7debda96562d7d5ec9a50db5fb253", "param_count": null, "params": [], "start": **********.672205, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/66b7debda96562d7d5ec9a50db5fb253.blade.php__components::66b7debda96562d7d5ec9a50db5fb253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F66b7debda96562d7d5ec9a50db5fb253.blade.php&line=1", "ajax": false, "filename": "66b7debda96562d7d5ec9a50db5fb253.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::66b7debda96562d7d5ec9a50db5fb253"}, {"name": "1x components.notification", "param_count": null, "params": [], "start": **********.681273, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/notification.blade.phpcomponents.notification", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.notification"}, {"name": "1x __components::e619de47f59c986e9f566638044a3ab7", "param_count": null, "params": [], "start": **********.683063, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/e619de47f59c986e9f566638044a3ab7.blade.php__components::e619de47f59c986e9f566638044a3ab7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fe619de47f59c986e9f566638044a3ab7.blade.php&line=1", "ajax": false, "filename": "e619de47f59c986e9f566638044a3ab7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e619de47f59c986e9f566638044a3ab7"}, {"name": "1x __components::f2b343610bcc0778e0f00f4bfe27f188", "param_count": null, "params": [], "start": **********.684082, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f2b343610bcc0778e0f00f4bfe27f188.blade.php__components::f2b343610bcc0778e0f00f4bfe27f188", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff2b343610bcc0778e0f00f4bfe27f188.blade.php&line=1", "ajax": false, "filename": "f2b343610bcc0778e0f00f4bfe27f188.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f2b343610bcc0778e0f00f4bfe27f188"}, {"name": "1x __components::0d5cde93dab074a387e37ef65b78febc", "param_count": null, "params": [], "start": **********.685053, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/0d5cde93dab074a387e37ef65b78febc.blade.php__components::0d5cde93dab074a387e37ef65b78febc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F0d5cde93dab074a387e37ef65b78febc.blade.php&line=1", "ajax": false, "filename": "0d5cde93dab074a387e37ef65b78febc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d5cde93dab074a387e37ef65b78febc"}, {"name": "1x __components::10cb7a8570e3e511d44febea50de8e08", "param_count": null, "params": [], "start": **********.686012, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/10cb7a8570e3e511d44febea50de8e08.blade.php__components::10cb7a8570e3e511d44febea50de8e08", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F10cb7a8570e3e511d44febea50de8e08.blade.php&line=1", "ajax": false, "filename": "10cb7a8570e3e511d44febea50de8e08.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::10cb7a8570e3e511d44febea50de8e08"}, {"name": "1x __components::46553ac5b5a848738f4879c0717608ca", "param_count": null, "params": [], "start": **********.688777, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/46553ac5b5a848738f4879c0717608ca.blade.php__components::46553ac5b5a848738f4879c0717608ca", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F46553ac5b5a848738f4879c0717608ca.blade.php&line=1", "ajax": false, "filename": "46553ac5b5a848738f4879c0717608ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46553ac5b5a848738f4879c0717608ca"}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00472, "accumulated_duration_str": "4.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.26315, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 10.381}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.275245, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 10.381, "width_percent": 11.229}, {"sql": "select * from `roles` where `roles`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 32}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.285239, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "RoleCreator.php:32", "source": {"index": 21, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=32", "ajax": false, "filename": "RoleCreator.php", "line": "32"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 21.61, "width_percent": 11.653}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.293102, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "RoleCreator.php:34", "source": {"index": 20, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=34", "ajax": false, "filename": "RoleCreator.php", "line": "34"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 33.263, "width_percent": 29.661}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.409208, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "RoleCreator.php:75", "source": {"index": 16, "namespace": null, "name": "app/Livewire/Admin/Miscellaneous/RoleCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\Miscellaneous\\RoleCreator.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=75", "ajax": false, "filename": "RoleCreator.php", "line": "75"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 62.924, "width_percent": 15.042}, {"sql": "select count(*) as aggregate from `users` where `role_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/ModelProcessor.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\ModelProcessor.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/ProcessDataSource.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\ProcessDataSource.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 174}, {"index": 20, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 140}], "start": **********.433189, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DataSourceBase.php:85", "source": {"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FDataSource%2FProcessors%2FDataSourceBase.php&line=85", "ajax": false, "filename": "DataSourceBase.php", "line": "85"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 77.966, "width_percent": 10.805}, {"sql": "select `code`, `name` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.659905, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "GeneralHelper.php:376", "source": {"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FHelpers%2FGeneralHelper.php&line=376", "ajax": false, "filename": "GeneralHelper.php", "line": "376"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 88.771, "width_percent": 11.229}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 117, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 120, "is_counter": true}, "livewire": {"data": {"admin.miscellaneous.role-creator #I1G3IHMpWngb9neO3G8T": "array:4 [\n  \"data\" => array:5 [\n    \"role\" => Spatie\\Permission\\Models\\Role {#1051\n      #connection: \"mysql\"\n      #table: \"roles\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:5 [\n        \"id\" => 1\n        \"name\" => \"Admin\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-07-12 04:42:06\"\n        \"updated_at\" => \"2025-07-12 04:42:06\"\n      ]\n      #original: array:5 [\n        \"id\" => 1\n        \"name\" => \"Admin\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2025-07-12 04:42:06\"\n        \"updated_at\" => \"2025-07-12 04:42:06\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:1 [\n        \"permissions\" => Illuminate\\Database\\Eloquent\\Collection {#1024\n          #items: array:57 [\n            0 => Spatie\\Permission\\Models\\Permission {#1091\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 1\n                \"name\" => \"source.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 1\n                \"name\" => \"source.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 1\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1027 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            1 => Spatie\\Permission\\Models\\Permission {#1120\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 2\n                \"name\" => \"source.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 2\n                \"name\" => \"source.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 2\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1089 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            2 => Spatie\\Permission\\Models\\Permission {#1119\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 3\n                \"name\" => \"source.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 3\n                \"name\" => \"source.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 3\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1088 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            3 => Spatie\\Permission\\Models\\Permission {#1118\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 4\n                \"name\" => \"source.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 4\n                \"name\" => \"source.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 4\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1087 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            4 => Spatie\\Permission\\Models\\Permission {#1117\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 5\n                \"name\" => \"ai_prompt.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 5\n                \"name\" => \"ai_prompt.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 5\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1086 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            5 => Spatie\\Permission\\Models\\Permission {#1116\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 6\n                \"name\" => \"ai_prompt.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 6\n                \"name\" => \"ai_prompt.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 6\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1085 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            6 => Spatie\\Permission\\Models\\Permission {#1115\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 7\n                \"name\" => \"ai_prompt.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 7\n                \"name\" => \"ai_prompt.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 7\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1084 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            7 => Spatie\\Permission\\Models\\Permission {#1114\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 8\n                \"name\" => \"ai_prompt.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 8\n                \"name\" => \"ai_prompt.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 8\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1083 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            8 => Spatie\\Permission\\Models\\Permission {#1113\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 10\n                \"name\" => \"canned_reply.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 10\n                \"name\" => \"canned_reply.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 10\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#999 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            9 => Spatie\\Permission\\Models\\Permission {#1112\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 11\n                \"name\" => \"canned_reply.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 11\n                \"name\" => \"canned_reply.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 11\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#998 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            10 => Spatie\\Permission\\Models\\Permission {#1111\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 12\n                \"name\" => \"canned_reply.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 12\n                \"name\" => \"canned_reply.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 12\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#997 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            11 => Spatie\\Permission\\Models\\Permission {#1110\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 13\n                \"name\" => \"connect_account.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 13\n                \"name\" => \"connect_account.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 13\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#996 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            12 => Spatie\\Permission\\Models\\Permission {#1109\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 14\n                \"name\" => \"connect_account.connect\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 14\n                \"name\" => \"connect_account.connect\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 14\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#995 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            13 => Spatie\\Permission\\Models\\Permission {#1108\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 15\n                \"name\" => \"connect_account.disconnect\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 15\n                \"name\" => \"connect_account.disconnect\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 15\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#994 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            14 => Spatie\\Permission\\Models\\Permission {#1107\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 17\n                \"name\" => \"message_bot.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 17\n                \"name\" => \"message_bot.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 17\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#993 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            15 => Spatie\\Permission\\Models\\Permission {#1106\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 18\n                \"name\" => \"message_bot.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 18\n                \"name\" => \"message_bot.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 18\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#992 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            16 => Spatie\\Permission\\Models\\Permission {#1105\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 19\n                \"name\" => \"message_bot.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 19\n                \"name\" => \"message_bot.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 19\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#989 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            17 => Spatie\\Permission\\Models\\Permission {#1104\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 20\n                \"name\" => \"message_bot.clone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 20\n                \"name\" => \"message_bot.clone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 20\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#990 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            18 => Spatie\\Permission\\Models\\Permission {#1103\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 21\n                \"name\" => \"template_bot.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 21\n                \"name\" => \"template_bot.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 21\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#991 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            19 => Spatie\\Permission\\Models\\Permission {#1102\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 22\n                \"name\" => \"template_bot.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 22\n                \"name\" => \"template_bot.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 22\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#988 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            20 => Spatie\\Permission\\Models\\Permission {#1101\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 23\n                \"name\" => \"template_bot.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 23\n                \"name\" => \"template_bot.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 23\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1002 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            21 => Spatie\\Permission\\Models\\Permission {#1100\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 24\n                \"name\" => \"template_bot.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 24\n                \"name\" => \"template_bot.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 24\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1003 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            22 => Spatie\\Permission\\Models\\Permission {#1099\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 25\n                \"name\" => \"template_bot.clone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 25\n                \"name\" => \"template_bot.clone\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 25\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1004 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            23 => Spatie\\Permission\\Models\\Permission {#1098\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 26\n                \"name\" => \"template.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 26\n                \"name\" => \"template.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 26\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1005 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            24 => Spatie\\Permission\\Models\\Permission {#1097\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 27\n                \"name\" => \"template.load_template\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 27\n                \"name\" => \"template.load_template\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 27\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1006 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            25 => Spatie\\Permission\\Models\\Permission {#1121\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 28\n                \"name\" => \"campaigns.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 28\n                \"name\" => \"campaigns.view\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 28\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1007 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            26 => Spatie\\Permission\\Models\\Permission {#1122\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 29\n                \"name\" => \"campaigns.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 29\n                \"name\" => \"campaigns.create\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 29\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1008 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            27 => Spatie\\Permission\\Models\\Permission {#1123\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 30\n                \"name\" => \"campaigns.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n              ]\n              #original: array:7 [\n                \"id\" => 30\n                \"name\" => \"campaigns.edit\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                \"updated_at\" => null\n                \"pivot_role_id\" => 1\n                \"pivot_permission_id\" => 30\n              ]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [\n                \"pivot\" => Illuminate\\Database\\Eloquent\\Relations\\Pivot {#1009 …34}\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [\n                0 => \"id\"\n              ]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            28 => Spatie\\Permission\\Models\\Permission {#1124\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [\n                \"id\" => 31\n                \"name\" => \"campaigns.delete\"\n                \"guard_name\" => \"web\"\n                \"created_at\" => null\n                 …1\n              ]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            29 => Spatie\\Permission\\Models\\Permission {#1125\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            30 => Spatie\\Permission\\Models\\Permission {#1126\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            31 => Spatie\\Permission\\Models\\Permission {#1127\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            32 => Spatie\\Permission\\Models\\Permission {#1128\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            33 => Spatie\\Permission\\Models\\Permission {#1129\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            34 => Spatie\\Permission\\Models\\Permission {#1130\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            35 => Spatie\\Permission\\Models\\Permission {#1131\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            36 => Spatie\\Permission\\Models\\Permission {#1132\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            37 => Spatie\\Permission\\Models\\Permission {#1133\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            38 => Spatie\\Permission\\Models\\Permission {#1134\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            39 => Spatie\\Permission\\Models\\Permission {#1135\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            40 => Spatie\\Permission\\Models\\Permission {#1136\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            41 => Spatie\\Permission\\Models\\Permission {#1137\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            42 => Spatie\\Permission\\Models\\Permission {#1138\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            43 => Spatie\\Permission\\Models\\Permission {#1139\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            44 => Spatie\\Permission\\Models\\Permission {#1140\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            45 => Spatie\\Permission\\Models\\Permission {#1141\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            46 => Spatie\\Permission\\Models\\Permission {#1142\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            47 => Spatie\\Permission\\Models\\Permission {#1143\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            48 => Spatie\\Permission\\Models\\Permission {#1144\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            49 => Spatie\\Permission\\Models\\Permission {#1145\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            50 => Spatie\\Permission\\Models\\Permission {#1146\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            51 => Spatie\\Permission\\Models\\Permission {#1147\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            52 => Spatie\\Permission\\Models\\Permission {#1148\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            53 => Spatie\\Permission\\Models\\Permission {#1149\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            54 => Spatie\\Permission\\Models\\Permission {#1150\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            55 => Spatie\\Permission\\Models\\Permission {#1151\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n            56 => Spatie\\Permission\\Models\\Permission {#1152\n              #connection: \"mysql\"\n              #table: \"permissions\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:5 [ …5]\n              #original: array:7 [ …7]\n              #changes: []\n              #casts: []\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: []\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:1 [ …1]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: []\n              #visible: []\n              #fillable: []\n              #guarded: array:1 [ …1]\n              -roleClass: null\n              -permissionClass: null\n              -wildcardClass: null\n              -wildcardPermissionsIndex: ? array\n            }\n          ]\n          #escapeWhenCastingToString: false\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"id\"\n      ]\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"selectedPermissions\" => array:57 [\n      0 => \"source.view\"\n      1 => \"source.create\"\n      2 => \"source.edit\"\n      3 => \"source.delete\"\n      4 => \"ai_prompt.view\"\n      5 => \"ai_prompt.create\"\n      6 => \"ai_prompt.edit\"\n      7 => \"ai_prompt.delete\"\n      8 => \"canned_reply.create\"\n      9 => \"canned_reply.edit\"\n      10 => \"canned_reply.delete\"\n      11 => \"connect_account.view\"\n      12 => \"connect_account.connect\"\n      13 => \"connect_account.disconnect\"\n      14 => \"message_bot.create\"\n      15 => \"message_bot.edit\"\n      16 => \"message_bot.delete\"\n      17 => \"message_bot.clone\"\n      18 => \"template_bot.view\"\n      19 => \"template_bot.create\"\n      20 => \"template_bot.edit\"\n      21 => \"template_bot.delete\"\n      22 => \"template_bot.clone\"\n      23 => \"template.view\"\n      24 => \"template.load_template\"\n      25 => \"campaigns.view\"\n      26 => \"campaigns.create\"\n      27 => \"campaigns.edit\"\n      28 => \"campaigns.delete\"\n      29 => \"campaigns.show_campaign\"\n      30 => \"chat.view\"\n      31 => \"chat.read_only\"\n      32 => \"activity_log.view\"\n      33 => \"activity_log.delete\"\n      34 => \"whatsmark_settings.view\"\n      35 => \"whatsmark_settings.edit\"\n      36 => \"bulk_campaigns.send\"\n      37 => \"role.view\"\n      38 => \"role.create\"\n      39 => \"role.edit\"\n      40 => \"role.delete\"\n      41 => \"status.view\"\n      42 => \"status.create\"\n      43 => \"status.edit\"\n      44 => \"status.delete\"\n      45 => \"contact.view\"\n      46 => \"contact.create\"\n      47 => \"contact.edit\"\n      48 => \"contact.delete\"\n      49 => \"contact.bulk_import\"\n      50 => \"system_settings.view\"\n      51 => \"system_settings.edit\"\n      52 => \"user.view\"\n      53 => \"user.create\"\n      54 => \"user.delete\"\n      55 => \"email_template.view\"\n      56 => \"email_template.edit\"\n    ]\n    \"role_id\" => null\n    \"assigne_from_contact\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"admin.miscellaneous.role-creator\"\n  \"component\" => \"App\\Livewire\\Admin\\Miscellaneous\\RoleCreator\"\n  \"id\" => \"I1G3IHMpWngb9neO3G8T\"\n]", "admin.table.role-assignee-table #ei8YTqIAh3jVDAgQBppt": "array:4 [\n  \"data\" => array:45 [\n    \"tableName\" => \"role-assignee-table-310hvo-table\"\n    \"role_id\" => 1\n    \"theme\" => array:17 [\n      \"name\" => \"tailwind\"\n      \"root\" => \"livewire-powergrid::components.frameworks.tailwind\"\n      \"table\" => array:3 [\n        \"layout\" => array:5 [\n          \"base\" => \"p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8\"\n          \"div\" => \"rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n          \"table\" => \"min-w-full dark:!bg-primary-800\"\n          \"container\" => \"-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8\"\n          \"actions\" => \"flex gap-2\"\n        ]\n        \"header\" => array:4 [\n          \"thead\" => \"shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900\"\n          \"tr\" => \"\"\n          \"th\" => \"font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300\"\n          \"thAction\" => \"!font-bold\"\n        ]\n        \"body\" => array:10 [\n          \"tbody\" => \"text-pg-primary-800\"\n          \"tbodyEmpty\" => \"\"\n          \"tr\" => \"border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700\"\n          \"td\" => \"px-3 py-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdEmpty\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdSummarize\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2\"\n          \"trSummarize\" => \"\"\n          \"tdFilters\" => \"\"\n          \"trFilters\" => \"\"\n          \"tdActionsContainer\" => \"flex gap-2\"\n        ]\n      ]\n      \"footer\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n        \"footer\" => \"border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n        \"footer_with_pagination\" => \"md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900\"\n      ]\n      \"cols\" => array:1 [\n        \"div\" => \"select-none flex items-center gap-1\"\n      ]\n      \"editable\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.editable\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"layout\" => array:4 [\n        \"table\" => \"livewire-powergrid::components.frameworks.tailwind.table-base\"\n        \"header\" => \"livewire-powergrid::components.frameworks.tailwind.header\"\n        \"pagination\" => \"livewire-powergrid::components.frameworks.tailwind.pagination\"\n        \"footer\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n      ]\n      \"toggleable\" => array:1 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.toggleable\"\n      ]\n      \"checkbox\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900\"\n      ]\n      \"radio\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-radio rounded-full transition ease-in-out duration-100\"\n      ]\n      \"filterBoolean\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.boolean\"\n        \"base\" => \"min-w-[5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterDatePicker\" => array:3 [\n        \"base\" => \"\"\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.date-picker\"\n        \"input\" => \"flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n      ]\n      \"filterMultiSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.multi-select\"\n        \"base\" => \"inline-block relative w-full\"\n        \"select\" => \"mt-1\"\n      ]\n      \"filterNumber\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.number\"\n        \"input\" => \"w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6\"\n      ]\n      \"filterSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.select\"\n        \"base\" => \"\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterInputText\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.input-text\"\n        \"base\" => \"min-w-[9.5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"searchBox\" => array:3 [\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8\"\n        \"iconClose\" => \"text-pg-primary-400 dark:text-pg-primary-200\"\n        \"iconSearch\" => \"text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200\"\n      ]\n    ]\n    \"primaryKey\" => \"id\"\n    \"primaryKeyAlias\" => null\n    \"ignoreTablePrefix\" => true\n    \"setUp\" => array:2 [\n      \"header\" => PowerComponents\\LivewirePowerGrid\\Components\\SetUp\\Header {#1346\n        +name: \"header\"\n        +searchInput: true\n        +toggleColumns: false\n        +softDeletes: false\n        +showMessageSoftDeletes: false\n        +includeViewOnTop: \"\"\n        +includeViewOnBottom: \"\"\n        +wireLoading: true\n      }\n      \"footer\" => PowerComponents\\LivewirePowerGrid\\Components\\SetUp\\Footer {#1344\n        +name: \"footer\"\n        +perPage: 10\n        +perPageValues: array:5 [\n          0 => 10\n          1 => 25\n          2 => 50\n          3 => 100\n          4 => 0\n        ]\n        +recordCount: \"full\"\n        +pagination: null\n        +includeViewOnTop: \"\"\n        +includeViewOnBottom: \"\"\n        +pageName: \"page\"\n      }\n    ]\n    \"showErrorBag\" => false\n    \"rowIndex\" => true\n    \"deferLoading\" => false\n    \"readyToLoad\" => true\n    \"loadingComponent\" => \"\"\n    \"columns\" => array:1 [\n      0 => PowerComponents\\LivewirePowerGrid\\Column {#1347\n        +title: \"Name\"\n        +field: \"firstname\"\n        +dataField: \"firstname\"\n        +placeholder: \"\"\n        +searchable: true\n        +enableSort: true\n        +hidden: false\n        +forceHidden: false\n        +visibleInExport: null\n        +sortable: true\n        +index: false\n        +properties: []\n        +rawQueries: []\n        +isAction: false\n        +fixedOnResponsive: false\n        +template: false\n        +contentClassField: \"\"\n        +contentClasses: []\n        +headerClass: \"\"\n        +headerStyle: \"\"\n        +bodyClass: \"\"\n        +bodyStyle: \"\"\n        +toggleable: []\n        +editable: []\n        +filters: null\n        +customContent: []\n      }\n    ]\n    \"headers\" => []\n    \"search\" => \"\"\n    \"currentTable\" => \"users\"\n    \"total\" => 0\n    \"totalCurrentPage\" => 0\n    \"supportModel\" => true\n    \"paginateRaw\" => false\n    \"measurePerformance\" => false\n    \"checkbox\" => false\n    \"checkboxAll\" => false\n    \"checkboxValues\" => []\n    \"checkboxAttribute\" => \"id\"\n    \"filters\" => []\n    \"filtered\" => []\n    \"enabledFilters\" => []\n    \"select\" => []\n    \"showFilters\" => false\n    \"withoutResourcesActions\" => false\n    \"additionalCacheKey\" => \"\"\n    \"persist\" => []\n    \"persistPrefix\" => \"\"\n    \"radio\" => false\n    \"radioAttribute\" => \"id\"\n    \"selectedRow\" => \"\"\n    \"softDeletes\" => \"\"\n    \"sortField\" => \"id\"\n    \"sortDirection\" => \"asc\"\n    \"multiSort\" => false\n    \"sortArray\" => []\n    \"headerTotalColumn\" => false\n    \"footerTotalColumn\" => false\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"admin.table.role-assignee-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Table\\RoleAssigneeTable\"\n  \"id\" => \"ei8YTqIAh3jVDAgQBppt\"\n]", "backend.sidebar-navigation #ZKmT1hggVY6hl2ISCDqN": "array:4 [\n  \"data\" => []\n  \"name\" => \"backend.sidebar-navigation\"\n  \"component\" => \"App\\Livewire\\Backend\\SidebarNavigation\"\n  \"id\" => \"ZKmT1hggVY6hl2ISCDqN\"\n]", "backend.header-navigation #DXUrUUP8G0qXFRJE619y": "array:4 [\n  \"data\" => []\n  \"name\" => \"backend.header-navigation\"\n  \"component\" => \"App\\Livewire\\Backend\\HeaderNavigation\"\n  \"id\" => \"DXUrUUP8G0qXFRJE619y\"\n]", "language-switcher #k4cJptmGI3ktTS6MANF6": "array:4 [\n  \"data\" => array:1 [\n    \"currentLocale\" => \"en\"\n  ]\n  \"name\" => \"language-switcher\"\n  \"component\" => \"App\\Livewire\\LanguageSwitcher\"\n  \"id\" => \"k4cJptmGI3ktTS6MANF6\"\n]"}, "count": 5}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/roles/role/1", "action_name": "admin.roles.save", "controller_action": "App\\Livewire\\Admin\\Miscellaneous\\RoleCreator", "uri": "GET admin/roles/role/{roleId?}", "controller": "App\\Livewire\\Admin\\Miscellaneous\\RoleCreator@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=78\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FMiscellaneous%2FRoleCreator.php&line=78\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Miscellaneous/RoleCreator.php:78-81</a>", "middleware": "web, validate, auth, App\\Http\\Middleware\\AdminMiddleware, App\\Http\\Middleware\\SanitizeInputs, Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified", "duration": "1.08s", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1378528244 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1378528244\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-463961602 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-463961602\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlBrelo1dzRVWGM2YUFudlFDbFJkWlE9PSIsInZhbHVlIjoiaHl4Rm1qL0FKNVZIS1oxcnhrSnltWVhhWTgrT2dIeUFNRDc1SlNSWkJpR1A2bUZ6VmxZaHA4dU01TndZTTNFMjNIOGdqaXdWOEVlNFMrY2w1a3REZ0xrckZHa0YwVTh1cXdHcU8wYldEK1gzbnJ3TTk5OE9IZFFOc0J4ckgxVGsiLCJtYWMiOiJkY2JhYjhmZTBlZDJhMDcwZTQ5MmVlNGE0NzBiNjU1NzUxZTgyNWE1ZmY3NTI3MTMyNzZmN2ZmYmQwZjljZDU1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZVdDF4V0JOdnZKVU9UY2dSdll5RGc9PSIsInZhbHVlIjoiMlozd0xoS1JBaXZCQStXWnRnNUhvS1FWcWhEZmZ3QWJmYzE5U3BXM3czaVp5bXNBSURmK2hkU1FRYzQvR2VPd2NOV3VKZjlsV3pOUDRuSUZVOUFyWWg1VFkwTlpVWS84VnpXMzVnc3JPRGt1RXFUeE1RbXRwM01xMW04WFRYL2kiLCJtYWMiOiI3YTVlZjc1YWZiNTlkOGFhYzUyNjEwZmJlNzdiOWRkOTAxYTE0MDU2ZjNmYmNmZmU5NDM0YTc1M2FkY2M1Mjk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1352108702 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1352108702\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1477888946 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:42:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477888946\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-342502685 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-342502685\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/roles/role/1", "action_name": "admin.roles.save", "controller_action": "App\\Livewire\\Admin\\Miscellaneous\\RoleCreator"}, "badge": null}}