<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('campaign_id');
            $table->unsignedBigInteger('rel_id')->nullable();
            $table->string('rel_type');
            $table->text('header_message')->nullable();
            $table->text('body_message')->nullable();
            $table->text('footer_message')->nullable();
            $table->integer('status')->nullable();
            $table->text('response_message')->nullable();
            $table->string('whatsapp_id')->nullable();
            $table->string('message_status')->nullable();
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('campaign_id')->references('id')->on('campaigns')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_details');
    }
};
