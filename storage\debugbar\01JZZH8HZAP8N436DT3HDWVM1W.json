{"__meta": {"id": "01JZZH8HZAP8N436DT3HDWVM1W", "datetime": "2025-07-12 04:40:08", "utime": **********.683009, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331207.908865, "end": **********.683021, "duration": 0.7741560935974121, "duration_str": "774ms", "measures": [{"label": "Booting", "start": 1752331207.908865, "relative_start": 0, "end": **********.492574, "relative_end": **********.492574, "duration": 0.****************, "duration_str": "584ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.492582, "relative_start": 0.****************, "end": **********.683023, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.507395, "relative_start": 0.****************, "end": **********.509525, "relative_end": **********.509525, "duration": 0.0021300315856933594, "duration_str": "2.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.user.user-creator", "start": **********.603218, "relative_start": 0.****************, "end": **********.603218, "relative_end": **********.603218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c3e78b549e314c45350a1f2014a728f1", "start": **********.608975, "relative_start": 0.****************, "end": **********.608975, "relative_end": **********.608975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.611962, "relative_start": 0.************2451, "end": **********.611962, "relative_end": **********.611962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7b30154958fd08f5dfb04c6347a46789", "start": **********.613749, "relative_start": 0.7048840522766113, "end": **********.613749, "relative_end": **********.613749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.614903, "relative_start": 0.7060379981994629, "end": **********.614903, "relative_end": **********.614903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.615666, "relative_start": 0.7068009376525879, "end": **********.615666, "relative_end": **********.615666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.616742, "relative_start": 0.7078769207000732, "end": **********.616742, "relative_end": **********.616742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.61804, "relative_start": 0.7091751098632812, "end": **********.61804, "relative_end": **********.61804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.618533, "relative_start": 0.7096679210662842, "end": **********.618533, "relative_end": **********.618533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.619421, "relative_start": 0.7105560302734375, "end": **********.619421, "relative_end": **********.619421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.620623, "relative_start": 0.7117581367492676, "end": **********.620623, "relative_end": **********.620623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.62094, "relative_start": 0.7120749950408936, "end": **********.62094, "relative_end": **********.62094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.621704, "relative_start": 0.7128391265869141, "end": **********.621704, "relative_end": **********.621704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ee090b29be96ce0a49e5dbcd11e0496c", "start": **********.622797, "relative_start": 0.7139320373535156, "end": **********.622797, "relative_end": **********.622797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.624102, "relative_start": 0.7152371406555176, "end": **********.624102, "relative_end": **********.624102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.624434, "relative_start": 0.715569019317627, "end": **********.624434, "relative_end": **********.624434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.625196, "relative_start": 0.7163310050964355, "end": **********.625196, "relative_end": **********.625196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.625508, "relative_start": 0.7166430950164795, "end": **********.625508, "relative_end": **********.625508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.625826, "relative_start": 0.7169609069824219, "end": **********.625826, "relative_end": **********.625826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.62677, "relative_start": 0.7179050445556641, "end": **********.62677, "relative_end": **********.62677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.631148, "relative_start": 0.722283124923706, "end": **********.631148, "relative_end": **********.631148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.631606, "relative_start": 0.7227411270141602, "end": **********.631606, "relative_end": **********.631606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.632675, "relative_start": 0.7238099575042725, "end": **********.632675, "relative_end": **********.632675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e6fff3659b0ed5c1aa616103d2d8c906", "start": **********.633712, "relative_start": 0.7248470783233643, "end": **********.633712, "relative_end": **********.633712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.634982, "relative_start": 0.7261171340942383, "end": **********.634982, "relative_end": **********.634982, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.635297, "relative_start": 0.7264320850372314, "end": **********.635297, "relative_end": **********.635297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.63605, "relative_start": 0.7271850109100342, "end": **********.63605, "relative_end": **********.63605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e6fff3659b0ed5c1aa616103d2d8c906", "start": **********.6364, "relative_start": 0.7275350093841553, "end": **********.6364, "relative_end": **********.6364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.637485, "relative_start": 0.7286200523376465, "end": **********.637485, "relative_end": **********.637485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.637792, "relative_start": 0.7289271354675293, "end": **********.637792, "relative_end": **********.637792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.638564, "relative_start": 0.7296991348266602, "end": **********.638564, "relative_end": **********.638564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::39f3dc700baf0170584b79c8d561970f", "start": **********.640387, "relative_start": 0.7315220832824707, "end": **********.640387, "relative_end": **********.640387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53ebc1ebb35fa01a8cd72e001f23ef14", "start": **********.641432, "relative_start": 0.7325670719146729, "end": **********.641432, "relative_end": **********.641432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.642894, "relative_start": 0.7340290546417236, "end": **********.642894, "relative_end": **********.642894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::39f3dc700baf0170584b79c8d561970f", "start": **********.644039, "relative_start": 0.7351739406585693, "end": **********.644039, "relative_end": **********.644039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53ebc1ebb35fa01a8cd72e001f23ef14", "start": **********.644459, "relative_start": 0.7355940341949463, "end": **********.644459, "relative_end": **********.644459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.645833, "relative_start": 0.7369680404663086, "end": **********.645833, "relative_end": **********.645833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cd7e41c6e607d6a89d50d4ad2a98abd8", "start": **********.647251, "relative_start": 0.7383859157562256, "end": **********.647251, "relative_end": **********.647251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.648884, "relative_start": 0.7400190830230713, "end": **********.648884, "relative_end": **********.648884, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::39f3dc700baf0170584b79c8d561970f", "start": **********.64981, "relative_start": 0.7409451007843018, "end": **********.64981, "relative_end": **********.64981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53ebc1ebb35fa01a8cd72e001f23ef14", "start": **********.650108, "relative_start": 0.7412431240081787, "end": **********.650108, "relative_end": **********.650108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.651335, "relative_start": 0.7424700260162354, "end": **********.651335, "relative_end": **********.651335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.652104, "relative_start": 0.7432389259338379, "end": **********.652104, "relative_end": **********.652104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.652414, "relative_start": 0.743549108505249, "end": **********.652414, "relative_end": **********.652414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6d2b0abe301d90865d3a9363cc05318", "start": **********.653601, "relative_start": 0.7447359561920166, "end": **********.653601, "relative_end": **********.653601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97dd5d9098ff5fff74d84335c08804fe", "start": **********.654979, "relative_start": 0.7461140155792236, "end": **********.654979, "relative_end": **********.654979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.664458, "relative_start": 0.7555930614471436, "end": **********.664458, "relative_end": **********.664458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.664846, "relative_start": 0.7559809684753418, "end": **********.664846, "relative_end": **********.664846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.665128, "relative_start": 0.756263017654419, "end": **********.665128, "relative_end": **********.665128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.665401, "relative_start": 0.7565360069274902, "end": **********.665401, "relative_end": **********.665401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.665666, "relative_start": 0.7568011283874512, "end": **********.665666, "relative_end": **********.665666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.665949, "relative_start": 0.7570841312408447, "end": **********.665949, "relative_end": **********.665949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.666246, "relative_start": 0.7573809623718262, "end": **********.666246, "relative_end": **********.666246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.666499, "relative_start": 0.7576339244842529, "end": **********.666499, "relative_end": **********.666499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.666773, "relative_start": 0.7579081058502197, "end": **********.666773, "relative_end": **********.666773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.667029, "relative_start": 0.7581639289855957, "end": **********.667029, "relative_end": **********.667029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.667282, "relative_start": 0.7584171295166016, "end": **********.667282, "relative_end": **********.667282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.667546, "relative_start": 0.758681058883667, "end": **********.667546, "relative_end": **********.667546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.66779, "relative_start": 0.7589249610900879, "end": **********.66779, "relative_end": **********.66779, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.668058, "relative_start": 0.759192943572998, "end": **********.668058, "relative_end": **********.668058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.668321, "relative_start": 0.7594559192657471, "end": **********.668321, "relative_end": **********.668321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.668591, "relative_start": 0.7597260475158691, "end": **********.668591, "relative_end": **********.668591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.668875, "relative_start": 0.7600100040435791, "end": **********.668875, "relative_end": **********.668875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.669137, "relative_start": 0.7602720260620117, "end": **********.669137, "relative_end": **********.669137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef0b18846b6665ccb08f54bad8c6955b", "start": **********.670045, "relative_start": 0.7611799240112305, "end": **********.670045, "relative_end": **********.670045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.67184, "relative_start": 0.7629749774932861, "end": **********.67184, "relative_end": **********.67184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.672799, "relative_start": 0.7639341354370117, "end": **********.672799, "relative_end": **********.672799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.673198, "relative_start": 0.7643330097198486, "end": **********.673198, "relative_end": **********.673198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.loading-button", "start": **********.674033, "relative_start": 0.7651679515838623, "end": **********.674033, "relative_end": **********.674033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83f96136a4af4da91c50b6760ff72460", "start": **********.675177, "relative_start": 0.7663121223449707, "end": **********.675177, "relative_end": **********.675177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.677608, "relative_start": 0.7687430381774902, "end": **********.68023, "relative_end": **********.68023, "duration": 0.002621889114379883, "duration_str": "2.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 40034736, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 70, "nb_templates": 70, "templates": [{"name": "1x livewire.admin.user.user-creator", "param_count": null, "params": [], "start": **********.603174, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/user/user-creator.blade.phplivewire.admin.user.user-creator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fuser%2Fuser-creator.blade.php&line=1", "ajax": false, "filename": "user-creator.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.user.user-creator"}, {"name": "1x __components::c3e78b549e314c45350a1f2014a728f1", "param_count": null, "params": [], "start": **********.608939, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c3e78b549e314c45350a1f2014a728f1.blade.php__components::c3e78b549e314c45350a1f2014a728f1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc3e78b549e314c45350a1f2014a728f1.blade.php&line=1", "ajax": false, "filename": "c3e78b549e314c45350a1f2014a728f1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c3e78b549e314c45350a1f2014a728f1"}, {"name": "12x components.label", "param_count": null, "params": [], "start": **********.611898, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 12, "name_original": "components.label"}, {"name": "1x __components::7b30154958fd08f5dfb04c6347a46789", "param_count": null, "params": [], "start": **********.613714, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7b30154958fd08f5dfb04c6347a46789.blade.php__components::7b30154958fd08f5dfb04c6347a46789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7b30154958fd08f5dfb04c6347a46789.blade.php&line=1", "ajax": false, "filename": "7b30154958fd08f5dfb04c6347a46789.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7b30154958fd08f5dfb04c6347a46789"}, {"name": "2x components.button.secondary", "param_count": null, "params": [], "start": **********.614855, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/secondary.blade.phpcomponents.button.secondary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fsecondary.blade.php&line=1", "ajax": false, "filename": "secondary.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.button.secondary"}, {"name": "2x components.button", "param_count": null, "params": [], "start": **********.61563, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.button"}, {"name": "6x components.input", "param_count": null, "params": [], "start": **********.618005, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.input"}, {"name": "8x components.input-error", "param_count": null, "params": [], "start": **********.618499, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}, "render_count": 8, "name_original": "components.input-error"}, {"name": "1x __components::ee090b29be96ce0a49e5dbcd11e0496c", "param_count": null, "params": [], "start": **********.622762, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ee090b29be96ce0a49e5dbcd11e0496c.blade.php__components::ee090b29be96ce0a49e5dbcd11e0496c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fee090b29be96ce0a49e5dbcd11e0496c.blade.php&line=1", "ajax": false, "filename": "ee090b29be96ce0a49e5dbcd11e0496c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ee090b29be96ce0a49e5dbcd11e0496c"}, {"name": "2x components.select", "param_count": null, "params": [], "start": **********.631111, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.select"}, {"name": "2x __components::e6fff3659b0ed5c1aa616103d2d8c906", "param_count": null, "params": [], "start": **********.633679, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/e6fff3659b0ed5c1aa616103d2d8c906.blade.php__components::e6fff3659b0ed5c1aa616103d2d8c906", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fe6fff3659b0ed5c1aa616103d2d8c906.blade.php&line=1", "ajax": false, "filename": "e6fff3659b0ed5c1aa616103d2d8c906.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::e6fff3659b0ed5c1aa616103d2d8c906"}, {"name": "3x __components::39f3dc700baf0170584b79c8d561970f", "param_count": null, "params": [], "start": **********.640348, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/39f3dc700baf0170584b79c8d561970f.blade.php__components::39f3dc700baf0170584b79c8d561970f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F39f3dc700baf0170584b79c8d561970f.blade.php&line=1", "ajax": false, "filename": "39f3dc700baf0170584b79c8d561970f.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::39f3dc700baf0170584b79c8d561970f"}, {"name": "3x __components::53ebc1ebb35fa01a8cd72e001f23ef14", "param_count": null, "params": [], "start": **********.641396, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/53ebc1ebb35fa01a8cd72e001f23ef14.blade.php__components::53ebc1ebb35fa01a8cd72e001f23ef14", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F53ebc1ebb35fa01a8cd72e001f23ef14.blade.php&line=1", "ajax": false, "filename": "53ebc1ebb35fa01a8cd72e001f23ef14.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::53ebc1ebb35fa01a8cd72e001f23ef14"}, {"name": "2x components.card", "param_count": null, "params": [], "start": **********.645785, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.card"}, {"name": "1x __components::cd7e41c6e607d6a89d50d4ad2a98abd8", "param_count": null, "params": [], "start": **********.647208, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/cd7e41c6e607d6a89d50d4ad2a98abd8.blade.php__components::cd7e41c6e607d6a89d50d4ad2a98abd8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fcd7e41c6e607d6a89d50d4ad2a98abd8.blade.php&line=1", "ajax": false, "filename": "cd7e41c6e607d6a89d50d4ad2a98abd8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cd7e41c6e607d6a89d50d4ad2a98abd8"}, {"name": "1x __components::c6d2b0abe301d90865d3a9363cc05318", "param_count": null, "params": [], "start": **********.653566, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c6d2b0abe301d90865d3a9363cc05318.blade.php__components::c6d2b0abe301d90865d3a9363cc05318", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc6d2b0abe301d90865d3a9363cc05318.blade.php&line=1", "ajax": false, "filename": "c6d2b0abe301d90865d3a9363cc05318.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c6d2b0abe301d90865d3a9363cc05318"}, {"name": "1x __components::97dd5d9098ff5fff74d84335c08804fe", "param_count": null, "params": [], "start": **********.654943, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/97dd5d9098ff5fff74d84335c08804fe.blade.php__components::97dd5d9098ff5fff74d84335c08804fe", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F97dd5d9098ff5fff74d84335c08804fe.blade.php&line=1", "ajax": false, "filename": "97dd5d9098ff5fff74d84335c08804fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::97dd5d9098ff5fff74d84335c08804fe"}, {"name": "18x __components::97693b359951e7d64261ebfaee711ce1", "param_count": null, "params": [], "start": **********.664421, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/97693b359951e7d64261ebfaee711ce1.blade.php__components::97693b359951e7d64261ebfaee711ce1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F97693b359951e7d64261ebfaee711ce1.blade.php&line=1", "ajax": false, "filename": "97693b359951e7d64261ebfaee711ce1.blade.php", "line": "?"}, "render_count": 18, "name_original": "__components::97693b359951e7d64261ebfaee711ce1"}, {"name": "1x __components::ef0b18846b6665ccb08f54bad8c6955b", "param_count": null, "params": [], "start": **********.670009, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ef0b18846b6665ccb08f54bad8c6955b.blade.php__components::ef0b18846b6665ccb08f54bad8c6955b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fef0b18846b6665ccb08f54bad8c6955b.blade.php&line=1", "ajax": false, "filename": "ef0b18846b6665ccb08f54bad8c6955b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ef0b18846b6665ccb08f54bad8c6955b"}, {"name": "1x components.button.loading-button", "param_count": null, "params": [], "start": **********.673993, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/loading-button.blade.phpcomponents.button.loading-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Floading-button.blade.php&line=1", "ajax": false, "filename": "loading-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.button.loading-button"}, {"name": "1x __components::83f96136a4af4da91c50b6760ff72460", "param_count": null, "params": [], "start": **********.675143, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83f96136a4af4da91c50b6760ff72460.blade.php__components::83f96136a4af4da91c50b6760ff72460", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83f96136a4af4da91c50b6760ff72460.blade.php&line=1", "ajax": false, "filename": "83f96136a4af4da91c50b6760ff72460.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::83f96136a4af4da91c50b6760ff72460"}]}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009600000000000001, "accumulated_duration_str": "9.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.512064, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 6.042}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.526717, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 6.042, "width_percent": 9.688}, {"sql": "select count(*) as aggregate from `users` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.586228, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 15.729, "width_percent": 5.833}, {"sql": "select count(*) as aggregate from `users` where `phone` = '+9647504712750' and `id` <> ''", "type": "query", "params": [], "bindings": ["+9647504712750", ""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.589509, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 21.563, "width_percent": 56.354}, {"sql": "select `code`, `name` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.6286168, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "GeneralHelper.php:376", "source": {"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FHelpers%2FGeneralHelper.php&line=376", "ajax": false, "filename": "GeneralHelper.php", "line": "376"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 77.917, "width_percent": 6.146}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Admin/User/UserCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\User\\UserCreator.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.656789, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "UserCreator.php:236", "source": {"index": 16, "namespace": null, "name": "app/Livewire/Admin/User/UserCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\User\\UserCreator.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FUser%2FUserCreator.php&line=236", "ajax": false, "filename": "UserCreator.php", "line": "236"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 84.063, "width_percent": 15.938}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 60, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 62, "is_counter": true}, "livewire": {"data": {"admin.user.user-creator #49KmJxfQssfA4Nh9To4P": "array:4 [\n  \"data\" => array:14 [\n    \"id\" => \"49KmJxfQssfA4Nh9To4P\"\n    \"user\" => App\\Models\\User {#2023\n      #connection: null\n      #table: null\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: false\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:6 [\n        \"firstname\" => \"sd\"\n        \"lastname\" => \"dk\"\n        \"email\" => \"<EMAIL>\"\n        \"phone\" => \"+9647504712750\"\n        \"default_language\" => \"en\"\n        \"profile_image_url\" => null\n      ]\n      #original: []\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"password\" => \"hashed\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:10 [\n        0 => \"firstname\"\n        1 => \"lastname\"\n        2 => \"phone\"\n        3 => \"default_language\"\n        4 => \"profile_image_url\"\n        5 => \"email\"\n        6 => \"password\"\n        7 => \"role_id\"\n        8 => \"is_admin\"\n        9 => \"email_verified_at\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #authPasswordName: \"password\"\n      #rememberTokenName: \"remember_token\"\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"profile_image_url\" => null\n    \"is_admin\" => true\n    \"role_id\" => null\n    \"password\" => \"sd123\"\n    \"password_confirmation\" => \"sd123\"\n    \"roles\" => Illuminate\\Support\\Collection {#2027\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"sendWelcomeMail\" => false\n    \"isVerified\" => false\n    \"selectedPermissions\" => []\n    \"rolePermissions\" => []\n    \"userAdditionalPermissions\" => []\n    \"roleAdditionalPermissions\" => []\n  ]\n  \"name\" => \"admin.user.user-creator\"\n  \"component\" => \"App\\Livewire\\Admin\\User\\UserCreator\"\n  \"id\" => \"49KmJxfQssfA4Nh9To4P\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\User\\UserCreator@save<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FUser%2FUserCreator.php&line=161\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FUser%2FUserCreator.php&line=161\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/User/UserCreator.php:161-226</a>", "middleware": "web", "duration": "775ms", "peak_memory": "46MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2026280473 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2026280473\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2078125477 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"850 characters\">{&quot;data&quot;:{&quot;id&quot;:&quot;49KmJxfQssfA4Nh9To4P&quot;,&quot;user&quot;:[{&quot;firstname&quot;:null,&quot;lastname&quot;:null,&quot;email&quot;:null,&quot;phone&quot;:null,&quot;default_language&quot;:null,&quot;profile_image_url&quot;:null},{&quot;class&quot;:&quot;App\\\\Models\\\\User&quot;,&quot;connection&quot;:null,&quot;s&quot;:&quot;elmdl&quot;}],&quot;profile_image_url&quot;:null,&quot;is_admin&quot;:null,&quot;role_id&quot;:null,&quot;password&quot;:null,&quot;password_confirmation&quot;:null,&quot;roles&quot;:[[],{&quot;class&quot;:&quot;Illuminate\\\\Support\\\\Collection&quot;,&quot;s&quot;:&quot;clctn&quot;}],&quot;sendWelcomeMail&quot;:false,&quot;isVerified&quot;:false,&quot;selectedPermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;rolePermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;userAdditionalPermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;roleAdditionalPermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;49KmJxfQssfA4Nh9To4P&quot;,&quot;name&quot;:&quot;admin.user.user-creator&quot;,&quot;path&quot;:&quot;admin\\/users\\/user&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;0728092b95a81e401e40216b919f41a521e4f2083714dce44eb07594094d2506&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>user.firstname</span>\" => \"<span class=sf-dump-str title=\"2 characters\">sd</span>\"\n        \"<span class=sf-dump-key>user.lastname</span>\" => \"<span class=sf-dump-str title=\"2 characters\">dk</span>\"\n        \"<span class=sf-dump-key>user.email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>user.phone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">+9647504712750</span>\"\n        \"<span class=sf-dump-key>user.default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        \"<span class=sf-dump-key>is_admin</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sd123</span>\"\n        \"<span class=sf-dump-key>password_confirmation</span>\" => \"<span class=sf-dump-str title=\"5 characters\">sd123</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">save</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2078125477\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-494470695 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1325</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/users/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFlcDVNNW00T3VPekRPb0RYZWU0SVE9PSIsInZhbHVlIjoiVmFXR3pUMGlDOFRJcWRGU2FlTmtaeUNlbG9QMXVvZ2JBcnNKcWl4M3prOG10UzJJdzl2Qm9XZ09DOXVmeVJ4NnBSeUl6MWJYRnRxVUhsbVRMdm5jZ3ZaMENJNjEyTjI0clZNaWN4UXM2eDN1UmZIWjR4WmlNL2RyanlocXNvZGMiLCJtYWMiOiIyN2Q0NDM3MWIwNmRkNTBhZWFmY2FmNWU3N2NiMWFhNTljNGIyZTViYTJiYWZlNzg5MTE3MDg0MjRkZWRmMmE1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVPSHM2OUhVaEdiRVNWSnVoc1NBOFE9PSIsInZhbHVlIjoiVGhKS09lMWJuSWpTUHJabHpHZXJYQTgzM0dsM3h2N2NObU1rU21ZUFA3aGtrYUkxcVNmbjhvUjJFN20zWFhGQ2hmbWw5bDFuTnBQRFhsOUNqKy80WENoWGlVMWV1aEZqeVVZaVRHU09RZEc2MEx2QmszR0JMcHNFSysybzFRcWoiLCJtYWMiOiIwNzQwNmRhYTA5MjczM2M0ZmFkZmJlZjAwMDFlYjJhNTFmZDcxZjQ4OWVmZjA5ODE2ZGI2MDdiNjE5MTQ2ZjNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494470695\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-494862692 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494862692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-670855249 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:40:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-670855249\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1161398727 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/users/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161398727\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}