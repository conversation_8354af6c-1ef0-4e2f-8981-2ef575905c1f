1752337436a:1046:{s:15:"welcome_message";s:27:"Welcome to our applications";s:5:"login";s:5:"Login";s:8:"register";s:8:"Register";s:13:"email_address";s:13:"Email Address";s:8:"password";s:8:"Password";s:11:"remember_me";s:11:"Remember me";s:9:"dashboard";s:9:"Dashboard";s:16:"profile_settings";s:16:"Profile Settings";s:6:"logout";s:6:"Logout";s:8:"features";s:8:"Features";s:12:"capabilities";s:12:"Capabilities";s:9:"role_name";s:9:"Role Name";s:8:"add_role";s:8:"Add Role";s:11:"create_role";s:11:"Create Role";s:23:"role_permission_warning";s:99:"Changing role permissions won't affect current staff members' permissions that are using this role.";s:24:"update_staff_permissions";s:62:"Update all staff members' permissions that are using this role";s:6:"status";s:6:"Status";s:10:"new_status";s:10:"New Status";s:4:"name";s:4:"Name";s:12:"status_color";s:5:"Color";s:24:"status_color_placeholder";s:13:"#Select color";s:19:"delete_status_title";s:13:"Delete Status";s:6:"source";s:6:"Source";s:10:"new_source";s:10:"New Source";s:19:"delete_source_title";s:13:"Delete Source";s:20:"source_in_use_notify";s:35:"The Source ID is already being used";s:7:"contact";s:7:"Contact";s:18:"new_contact_button";s:11:"New Contact";s:15:"contact_details";s:15:"Contact Details";s:13:"other_details";s:13:"Other Details";s:9:"firstname";s:10:"First Name";s:8:"lastname";s:9:"Last Name";s:7:"company";s:7:"Company";s:4:"type";s:4:"Type";s:9:"type_lead";s:4:"Lead";s:13:"type_customer";s:8:"Customer";s:5:"email";s:5:"Email";s:7:"website";s:7:"Website";s:16:"default_language";s:16:"Default Language";s:7:"english";s:7:"English";s:8:"assigned";s:8:"Assigned";s:15:"assigned_select";s:15:"Select Assigned";s:4:"city";s:4:"City";s:5:"state";s:5:"State";s:7:"country";s:7:"Country";s:14:"country_select";s:14:"Select Country";s:8:"zip_code";s:8:"Zip Code";s:7:"address";s:7:"Address";s:11:"description";s:11:"Description";s:20:"delete_contact_title";s:14:"Delete Contact";s:10:"ai_prompts";s:10:"AI Prompts";s:6:"action";s:6:"Action";s:23:"delete_ai_prompts_title";s:16:"Delete AI Prompt";s:12:"canned_reply";s:12:"Canned Reply";s:5:"title";s:5:"Title";s:19:"delete_canned_title";s:19:"Delete Canned Reply";s:17:"delete_chat_title";s:12:"Delete Chat?";s:13:"update_button";s:6:"Update";s:10:"add_button";s:3:"Add";s:17:"add_contact_title";s:15:"Add New Contact";s:18:"edit_contact_title";s:12:"Edit Contact";s:15:"add_notes_title";s:9:"Add Notes";s:11:"notes_title";s:5:"Notes";s:7:"confirm";s:7:"Confirm";s:18:"forgot_password_fp";s:15:"Forgot password";s:20:"auth.forgot_password";s:114:"Forgot your password? No problem. Just let us know your email address and we will email you a password reset link.";s:17:"email_password_fp";s:25:"Email Password Reset Link";s:8:"login_lb";s:6:"Log in";s:16:"confirm_password";s:16:"Confirm password";s:23:"auth.already_registered";s:19:"Already registered?";s:17:"reset_password_rp";s:14:"Reset Password";s:12:"varify_email";s:25:"Resend Verification Email";s:9:"logout_ve";s:7:"Log Out";s:25:"delete_activity_log_title";s:19:"Delete Activity Log";s:12:"connect_waba";s:12:"Connect WABA";s:25:"whatsapp_business_account";s:25:"WhatsApp Business Account";s:27:"connect_with_facebook_step1";s:52:"Step - 1 : Facebook Developer Account & Facebook App";s:9:"fb_app_id";s:16:"Facebook App ID ";s:4:"help";s:4:"help";s:13:"fb_app_secret";s:19:"Facebook App Secret";s:7:"webhook";s:15:"Connect Webhook";s:20:"wp_integration_step2";s:37:"Step - 2 : WhatsApp Integration Setup";s:14:"wp_business_id";s:40:"Your WhatsApp Business Account (WABA) ID";s:22:"user_access_token_info";s:87:"Your User Access Token after signing up at for an account at Facebook Developers Portal";s:15:"wp_access_token";s:21:"Whatsapp Access Token";s:11:"debug_token";s:11:"Debug Token";s:6:"config";s:9:"Configure";s:4:"waba";s:4:"WABA";s:7:"qr_code";s:20:"Click to get QR Code";s:18:"disconnect_account";s:18:"Disconnect Account";s:17:"access_token_info";s:24:"Access Token Information";s:12:"access_token";s:12:"Access token";s:17:"permission_scopes";s:17:"Permission scopes";s:6:"issued";s:9:"Issued at";s:6:"expiry";s:9:"Expiry at";s:11:"webhook_url";s:11:"Webhook URL";s:12:"test_message";s:17:"Send Test Message";s:10:"wp_message";s:97:"Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.";s:9:"wp_number";s:15:"WhatsApp number";s:12:"send_message";s:12:"Send Message";s:14:"verify_webhook";s:14:"Verify Webhook";s:20:"display_phone_number";s:20:"Display Phone Number";s:13:"verified_name";s:13:"Verified Name";s:9:"number_id";s:9:"Number ID";s:7:"quality";s:7:"Quality";s:20:"manage_phone_numbers";s:20:"Manage Phone Numbers";s:15:"mark_as_default";s:15:"Mark as Default";s:14:"overall_health";s:14:"Overall Health";s:20:"whatsapp_business_id";s:20:"WhatsApp Business ID";s:12:"status_as_at";s:12:"Status as at";s:27:"overall_health_send_message";s:31:"Overall Health of Send Messages";s:16:"can_send_message";s:17:"Can Send Messages";s:21:"refresh_health_status";s:21:"Refresh health status";s:21:"qr_code_to_start_chat";s:26:"Scan QR Code to Start Chat";s:24:"qr_code_to_invite_people";s:69:"You can use the following QR Codes to invite people on this platform.";s:16:"url_for_qr_image";s:16:"URL for QR Image";s:12:"whatsapp_url";s:12:"WhatsApp URL";s:12:"whatsapp_now";s:12:"WhatsApp Now";s:5:"close";s:5:"Close";s:14:"delete_message";s:46:"Are you sure you want to perform this action ?";s:12:"open_sidebar";s:12:"Open sidebar";s:8:"settings";s:8:"Settings";s:6:"system";s:6:"System";s:9:"whatsmark";s:9:"WhatsMark";s:5:"light";s:5:"Light";s:4:"dark";s:4:"Dark";s:14:"open_user_menu";s:14:"Open user menu";s:6:"avatar";s:6:"Avatar";s:15:"account_profile";s:15:"Account Profile";s:7:"profile";s:7:"Profile";s:15:"add_message_bot";s:18:"Create Message bot";s:8:"edit_bot";s:16:"Edit Message bot";s:8:"bot_name";s:8:"Bot Name";s:13:"relation_type";s:13:"Relation Type";s:18:"data_tippy_content";s:190:"Text that will be sent to the lead or contact. You can also use {companyname}, {crm_url} or any other custom merge fields of lead or contact , or use the  sign to find available merge fields";s:10:"reply_text";s:54:"Reply text (Maximum allowed characters should be 1024)";s:10:"reply_type";s:10:"Reply Type";s:15:"trigger_keyword";s:15:"Trigger Keyword";s:24:"max_allowed_character_60";s:39:"Maximum allowed characters should be 60";s:6:"header";s:6:"Header";s:6:"footer";s:6:"Footer";s:20:"reply_button_option1";s:32:"Option 1: Bot with reply Buttons";s:19:"max_allowed_char_20";s:39:"Maximum allowed characters should be 20";s:7:"button1";s:7:"Button1";s:18:"max_allow_char_256";s:40:"Maximum allowed characters should be 256";s:10:"button1_id";s:10:"Button1 ID";s:7:"button2";s:7:"Button2";s:10:"button2_id";s:10:"Button2 ID";s:7:"button3";s:7:"Button3";s:10:"button3_id";s:10:"Button3 ID";s:19:"option2_button_name";s:57:"Option 2: Bot with button link - Call to Action (CTA) URL";s:11:"button_name";s:11:"Button Name";s:11:"button_link";s:11:"Button Link";s:16:"bot_file_option3";s:23:"Option 3: Bot with file";s:16:"choose_file_type";s:16:"Choose File Type";s:5:"image";s:5:"Image";s:8:"document";s:8:"Document";s:5:"video";s:5:"Video";s:7:"preview";s:7:"Preview";s:6:"submit";s:6:"Submit";s:12:"message_bots";s:12:"Message Bots";s:18:"delete_message_bot";s:18:"Delete Message Bot";s:6:"cancel";s:6:"Cancel";s:6:"delete";s:6:"Delete";s:20:"set_background_color";s:20:"Set background color";s:19:"set_link_text_color";s:19:"Set link text color";s:22:"set_link_message_color";s:17:"Set message color";s:19:"message_bot_options";s:7:"Options";s:25:"email_template_list_title";s:15:"Email Templates";s:20:"email_template_title";s:14:"Email Template";s:19:"email_template_name";s:13:"Template Name";s:22:"email_template_subject";s:7:"Subject";s:19:"save_changes_button";s:12:"Save Changes";s:22:"merge_field_name_title";s:10:"Field Name";s:15:"merge_tag_title";s:9:"Merge Tag";s:19:"merge_not_available";s:26:"No merge fields available.";s:13:"bulk_campaign";s:13:"Bulk Campaign";s:21:"campaign_for_csv_file";s:23:"Campaigns from CSV File";s:8:"campaign";s:8:"Campaign";s:13:"campaign_name";s:13:"Campaign Name";s:15:"choose_csv_file";s:15:"Choose CSV File";s:24:"csv_sample_file_download";s:33:"Download Sample File & Read Rules";s:15:"download_sample";s:15:"Download Sample";s:24:"phone_requirement_column";s:35:"1. Phone Number Column Requirement:";s:21:"phone_req_description";s:183:"Your CSV file must include a column named Phoneno. Each record in this column should contain a valid contact number, correctly formatted with the country code, including the '+' sign.";s:19:"csv_encoding_format";s:28:"2. CSV Format and Encoding: ";s:24:"csv_encoding_description";s:218:"Your CSV data should follow the specified format. The first row of your CSV file must contain the column headers, as shown in the example table. Ensure that your file is encoded in UTF-8 to prevent any encoding issues.";s:13:"<EMAIL>";s:13:"<EMAIL>";s:6:"upload";s:6:"Upload";s:14:"system_setting";s:15:"System Settings";s:18:"email_notification";s:24:"Notification Preferences";s:30:"email_notification_description";s:119:"Configure automated email notifications for various system events, ensuring users stay informed about critical updates.";s:14:"msg_for_admins";s:63:"When new lead is created, send email notification to all admins";s:17:"msg_for_assignees";s:66:"When new lead is created, send email notification to all assignees";s:16:"general_settings";s:16:"General Settings";s:20:"system_core_settings";s:20:"System Core Settings";s:32:"system_core_settings_description";s:132:"Configure fundamental system-wide parameters and default behaviors that impact overall application performance and user experience. ";s:9:"site_name";s:9:"Site Name";s:16:"site_description";s:16:"Site Description";s:12:"localization";s:12:"Localization";s:8:"timezone";s:8:"Timezone";s:11:"date_format";s:11:"Date Format";s:11:"time_format";s:11:"Time Format";s:12:"logo_favicon";s:14:"Logo & Favicon";s:9:"site_logo";s:9:"Site Logo";s:11:"recommended";s:11:"Recommended";s:11:"img_preview";s:13:"Image Preview";s:19:"select_or_browse_to";s:20:"Select or browse to ";s:17:"from_your_gallery";s:18:"from your gallery.";s:10:"remove_img";s:12:"Remove Image";s:9:"dark_logo";s:9:"Dark Logo";s:7:"favicon";s:7:"Favicon";s:12:"favicon_icon";s:12:"Favicon Icon";s:8:"24_hours";s:8:"24 Hours";s:8:"12_hours";s:8:"12 Hours";s:14:"email_settings";s:19:"Email Configuration";s:26:"email_settings_description";s:135:"Manage SMTP settings, email templates, and outgoing communication preferences to ensure reliable and professional email communications.";s:21:"important_information";s:21:"Important Information";s:20:"imp_info_description";s:440:" Please configure your mail server settings accurately. This application will rely on your specified mail server to handle email delivery. Errors encountered during email operations are typically due to incorrect server settings. Ensure all credentials, such as the port, encryption method, and SMTP details, are correct. Use the Send Test Email button to validate your configuration. If an error occurs, review your settings and try again.";s:20:"select_smtp_protocol";s:20:"Select SMTP Protocol";s:13:"smtp_protocol";s:13:"SMTP Protocol";s:4:"smtp";s:4:"SMTP";s:9:"smtp_host";s:9:"SMTP Host";s:9:"smtp_port";s:9:"SMTP Port";s:13:"smtp_username";s:13:"SMTP Username";s:13:"smtp_password";s:13:"SMTP Password";s:15:"smtp_encryption";s:15:"SMTP Encryption";s:3:"ssl";s:3:"SSL";s:3:"tls";s:3:"TLS";s:11:"sender_name";s:11:"Sender Name";s:17:"your_company_name";s:17:"Your Company Name";s:12:"sender_email";s:12:"Sender Email";s:4:"test";s:4:"Test";s:6:"saving";s:9:"Saving...";s:7:"sending";s:10:"Sending...";s:10:"re_captcha";s:10:"Re-Captcha";s:26:"bot_protection_description";s:127:"Implement advanced bot prevention and user verification mechanisms to protect your application from automated attacks and spam.";s:13:"toggle_switch";s:13:"Toggle Switch";s:16:"enable_recaptcha";s:18:"Enable Re-Captcha?";s:18:"recaptcha_site_key";s:19:"Re-Captcha site key";s:21:"recaptcha_site_secret";s:22:"Re-Captcha site secret";s:17:"obtain_credential";s:41:"Obtain your Google Re-Captcha credentials";s:4:"here";s:4:"here";s:20:"v3_setup_description";s:116:"Must use Re-Captcha v3 in credentials setup. Be careful, the wrong settings will make the login system interruption.";s:20:"announcement_setting";s:23:"Site-Wide Announcements";s:12:"announcement";s:12:"Announcement";s:33:"announcement_settings_description";s:127:"Create and manage global messages that can be displayed across the application to communicate important updates or information.";s:9:"is_Enable";s:9:"Is Enable";s:4:"link";s:4:"Link";s:9:"link_text";s:9:"Link Text";s:7:"message";s:7:"Message";s:7:"cronjob";s:7:"Cronjob";s:19:"cronjob_description";s:119:"Schedule and manage automated background tasks, ensuring timely execution of system maintenance and periodic processes.";s:15:"cronjob_running";s:41:"Congratulations! Your cronjob is running.";s:15:"last_checked_at";s:15:"Last checked at";s:22:"setting_up_the_cronjob";s:22:"Setting up the Cronjob";s:9:"cronjob_1";s:55:"Connect to your server via SSH or any preferred method.";s:9:"cronjob_2";s:63:"Open the crontab file using a text editor (e.g., `crontab -e`).";s:9:"cronjob_3";s:54:"Add the above command to the crontab file and save it.";s:9:"cronjob_4";s:75:"The cronjob will now run at every minute and execute the specified command.";s:16:"link_description";s:49:"You can learn more about cronjob from the Laravel";s:3:"seo";s:3:"SEO";s:15:"seo_description";s:127:"Configure meta tags, sitemap settings, and other SEO-related parameters to improve your application's search engine visibility.";s:10:"meta_title";s:10:"Meta Title";s:16:"meta_description";s:16:"Meta Description";s:6:"pusher";s:6:"Pusher";s:6:"app_id";s:6:"App ID";s:7:"app_key";s:7:"App Key";s:10:"app_secret";s:10:"App Secret";s:7:"cluster";s:7:"Cluster";s:31:"leave_blank_for_default_cluster";s:46:"Leave blank to use the default pusher cluster.";s:30:"enable_real_time_notifications";s:30:"Enable Real Time Notifications";s:28:"enable_desktop_notifications";s:28:"Enable Desktop Notifications";s:38:"ssl_required_for_desktop_notifications";s:42:"SSL is required for desktop notifications.";s:11:"test_pusher";s:11:"Test Pusher";s:15:"test_connection";s:15:"Test Connection";s:16:"dest_notify_desc";s:136:"Starting from September 2024-25 the application must run on SSL in order desktop notifications to work properly (browsers requires SSL).";s:13:"google_chrome";s:14:"Google Chrome.";s:20:"auto_dismiss_desktop";s:65:"Auto Dismiss Desktop Notifications After X Seconds (0 to disable)";s:12:"api_settings";s:12:"API Settings";s:9:"api_token";s:9:"API Token";s:21:"api_token_description";s:40:"Configure your API token settings below.";s:17:"enable_api_access";s:17:"Enable API Access";s:18:"generate_new_token";s:18:"Generate New Token";s:4:"copy";s:4:"Copy";s:6:"copied";s:6:"Copied";s:34:"please_copy_your_new_api_token_now";s:35:"Please copy your new API token now.";s:48:"these_are_the_default_permissions_for_api_access";s:48:"These are the default permissions for API access";s:7:"success";s:7:"Success";s:14:"api_management";s:14:"API Management";s:24:"performance_optimization";s:24:"Performance Optimization";s:15:"token_abilities";s:15:"Token Abilities";s:17:"cache_description";s:114:"Configure caching strategies to improve application speed and reduce server load through intelligent data storage.";s:20:"clear_framework_text";s:59:"Clear framework cache, bootstrap cache and temporary files.";s:9:"view_text";s:46:"Clear compiled views to make views up to date.";s:12:"clear_config";s:112:"Clear config - You might need to refresh the config caching when you change something on production environment.";s:19:"clear_cache_routing";s:20:"Clear cache routing.";s:21:"clear_system_log_file";s:23:"Clear system log files.";s:4:"size";s:5:"Size:";s:8:"run_tool";s:8:"Run Tool";s:10:"processing";s:14:" Processing...";s:25:"processing_cache_clearing";s:28:"Processing cache clearing...";s:13:"system_update";s:13:"System Update";s:26:"software_update_management";s:26:"Software Update Management";s:38:"software_update_management_description";s:102:"Manage application version updates, check for new releases, and control the update deployment process.";s:18:"system_information";s:18:"System Information";s:23:"please_select_an_option";s:23:"Please select an option";s:18:"packages_installed";s:18:"Packages Installed";s:18:"system_environment";s:18:"System Environment";s:12:"core_version";s:13:"Core Version:";s:17:"framework_version";s:18:"Framework Version:";s:10:"debug_mode";s:11:"Debug Mode:";s:12:"what_is_this";s:13:"What is this?";s:20:"storage_dir_writable";s:21:"Storage Dir Writable:";s:18:"cache_dir_writable";s:19:"Cache Dir Writable:";s:8:"app_size";s:9:"App Size:";s:21:"free_total_disk_space";s:22:"Free/Total Disk Space:";s:22:"php_max_execution_time";s:26:"PHP Max Execution Time(s):";s:15:"server_software";s:16:"Server Software:";s:9:"server_os";s:10:"Server OS:";s:8:"database";s:9:"Database:";s:12:"cache_driver";s:13:"Cache Driver:";s:14:"session_driver";s:15:"Session Driver:";s:16:"queue_connection";s:17:"Queue Connection:";s:9:"extension";s:10:"Extension:";s:18:"whatsapp_auto_lead";s:18:"Whatsapp Auto Lead";s:8:"stop_bot";s:8:"Stop Bot";s:9:"web_hooks";s:16:"Whatsapp Webhook";s:13:"support_agent";s:13:"Support Agent";s:18:"notification_sound";s:18:"Notification Sound";s:14:"ai_integration";s:14:"AI Integration";s:23:"auto_clear_chat_history";s:23:"Auto Clear Chat History";s:18:"whatsmark_settings";s:18:"Whatsmark Settings";s:24:"automate_lead_generation";s:69:"Automate lead generation and management through WhatsApp integration.";s:30:"acquire_new_lead_automatically";s:70:"Acquire New Lead Automatically (convert new WhatsApp messages to lead)";s:11:"lead_status";s:11:"Lead Status";s:11:"lead_source";s:11:"Lead Source";s:13:"lead_assigned";s:13:"Lead Assigned";s:18:"configure_stop_bot";s:68:"Configure settings to prevent unwanted bot activities in the system.";s:17:"stop_bots_keyword";s:17:"Stop Bots Keyword";s:18:"restart_bots_after";s:18:"Restart Bots After";s:5:"hours";s:5:"Hours";s:16:"manage_web_hooks";s:70:"Manage webhooks to enable seamless integration with external services.";s:22:"enable_webhooks_resend";s:23:"Enable WebHooks Re-send";s:21:"webhook_resend_method";s:21:"Webhook Resend Method";s:3:"get";s:3:"GET";s:4:"post";s:4:"POST";s:32:"whatsapp_received_data_resend_to";s:40:"WhatsApp received data will be resent to";s:23:"configure_support_agent";s:66:"Configure support agent settings for streamlined customer service.";s:20:"restrict_chat_access";s:53:"Restrict chat access to assigned support agents only.";s:4:"note";s:5:"Note:";s:26:"support_agent_feature_info";s:151:"When you enable the support agent feature, the staff will automatically be assigned to the chat. Admins can also assign a new agent from the chat page.";s:28:"customize_notification_sound";s:57:"Customize notification sounds for better user experience.";s:39:"enable_whatsapp_chat_notification_sound";s:39:"Enable WhatsApp chat notification sound";s:18:"integrate_ai_tools";s:69:"Integrate AI-powered tools to enhance automation and decision-making.";s:23:"activate_openai_in_chat";s:28:"Activate OpenAI in the chat.";s:10:"chat_model";s:10:"Chat Model";s:17:"openai_secret_key";s:17:"OpenAI Secret Key";s:24:"where_to_find_secret_key";s:30:"Where you can find secret key?";s:18:"clear_chat_history";s:18:"Clear Chat History";s:21:"setup_auto_clear_chat";s:87:"Set up automated clearing of chat histories to maintain system performance and privacy.";s:24:"activate_auto_clear_chat";s:32:"Activate Auto Clear Chat History";s:23:"auto_clear_history_time";s:23:"Auto Clear History Time";s:4:"days";s:4:"Days";s:8:"enabling";s:8:"Enabling";s:15:"auto_clear_note";s:80:" will automatically delete old chats after the specified number of days when the";s:8:"cron_job";s:8:"cron job";s:4:"runs";s:5:"runs.";s:17:"cron_job_required";s:43:"This feature requires a properly configured";s:19:"cron_job_setup_info";s:49:"Before activating, ensure it is set up as per the";s:13:"documentation";s:14:"documentation.";s:8:"template";s:8:"Template";s:9:"variables";s:9:"Variables";s:8:"variable";s:8:"Variable";s:20:"variable_description";s:59:"Currently, the variable is not available for this template.";s:15:"select_document";s:15:"Select Document";s:12:"select_image";s:12:"Select Image";s:12:"select_video";s:12:"Select Video";s:4:"body";s:4:"Body";s:13:"send_campaign";s:13:"Send Campaign";s:18:"create_message_bot";s:18:"Create Message Bot";s:11:"message_bot";s:11:"Message Bot";s:16:"nothing_selected";s:16:"Nothing Selected";s:12:"reply_button";s:13:"Reply Buttons";s:7:"cta_url";s:7:"CTA URL";s:11:"file_upload";s:5:"Files";s:12:"welcome_back";s:13:"Welcome Back,";s:24:"whatsapp_business_update";s:58:"Here's what's happening with your WhatsApp business today.";s:12:"new_campaign";s:12:"New Campaign";s:16:"view_all_reports";s:16:"View All Reports";s:17:"recent_activities";s:17:"Recent Activities";s:8:"view_all";s:8:"View All";s:15:"bot_performance";s:15:"Bot Performance";s:14:"configure_bots";s:14:"Configure Bots";s:12:"interactions";s:12:"interactions";s:18:"success_percentage";s:9:"% Success";s:17:"whatsapp_template";s:17:"Whatsapp Template";s:13:"load_template";s:14:"Load Templates";s:19:"template_management";s:19:"Template Management";s:24:"announcement_toggle_note";s:66:"(Enable this option to display an announcement on the login page.)";s:8:"new_user";s:8:"New User";s:11:"delete_user";s:11:"Delete User";s:5:"users";s:5:"Users";s:4:"user";s:4:"User";s:8:"add_user";s:8:"Add User";s:4:"role";s:4:"Role";s:13:"profile_image";s:13:"Profile Image";s:6:"remove";s:6:"Remove";s:34:"profile_image_removed_successfully";s:35:"Profile image removed successfully.";s:6:"change";s:6:"Change";s:8:"new_role";s:8:"New Role";s:11:"delete_role";s:11:"Delete Role";s:10:"permission";s:10:"Permission";s:5:"phone";s:5:"Phone";s:23:"select_default_language";s:23:"Select Default Language";s:11:"select_role";s:11:"Select Role";s:4:"save";s:4:"Save";s:17:"activity_log_list";s:17:"Activity Log List";s:9:"clear_log";s:9:"Clear Log";s:20:"activity_log_details";s:20:"Activity Log Details";s:4:"date";s:4:"DATE";s:15:"total_parameter";s:16:"Total Parameters";s:25:"number_id_of_the_whatsapp";s:42:"Number Id of the WhatsApp Registered Phone";s:19:"business_account_id";s:28:"WhatsApp Business Account ID";s:21:"whatsapp_access_token";s:21:"WhatsApp Access Token";s:11:"raw_content";s:11:"Raw Content";s:11:"format_type";s:17:"Format Type: JSON";s:8:"response";s:8:"Response";s:20:"something_went_wrong";s:31:"Something went wrong! Try again";s:20:"account_disconnected";s:20:"Account disconnected";s:21:"health_status_updated";s:21:"Health status updated";s:22:"default_number_updated";s:22:"Default number updated";s:9:"languages";s:9:"Languages";s:13:"language_name";s:13:"Language Name";s:13:"language_code";s:24:"Language Code (ISO Code)";s:15:"delete_language";s:15:"Delete Language";s:22:"translation_management";s:22:"Translation Management";s:18:"translate_language";s:18:"Translate Language";s:12:"activity_log";s:12:"Activity Log";s:9:"templates";s:9:"Templates";s:9:"marketing";s:9:"Marketing";s:12:"template_bot";s:12:"Template Bot";s:7:"support";s:7:"Support";s:4:"chat";s:4:"Chat";s:5:"setup";s:5:"Setup";s:18:"email_verification";s:143:"Thanks for signing up! Please verify your email by clicking the link we sent you. If you didn't receive the email, we will gladly send another.";s:12:"verify_email";s:12:"Verify Email";s:10:"email_veri";s:18:"Email Verification";s:12:"phone_sample";s:15:"+1 ************";s:11:"sample_data";s:11:"Sample Data";s:14:"import_contact";s:15:"Import Contacts";s:20:"import_contact_camel";s:14:"Import Contact";s:28:"import_contact_from_csv_file";s:29:"Import contacts from CSV file";s:25:"drag_and_drop_description";s:42:"Drag your file here or click in this area.";s:11:"pusher_link";s:32:"https://pusher.com/docs/clusters";s:15:"send_test_email";s:15:"Send Test Email";s:27:"send_test_email_description";s:70:"Send test email to make sure that your SMTP settings is set correctly.";s:31:"template_bot_saved_successfully";s:31:"Template bot saved successfully";s:19:"create_template_bot";s:19:"Create Template Bot";s:17:"edit_template_bot";s:17:"Edit Template Bot";s:15:"create_campaign";s:15:"Create Campaign";s:24:"select_all_relation_type";s:24:"Select All Relation type";s:16:"select_all_leads";s:10:"Select all";s:2:"or";s:2:"OR";s:18:"schedule_send_time";s:19:"Scheduled Send Time";s:34:"ignore_scheduled_time_and_send_now";s:34:"Ignore scheduled time and send now";s:40:"variable_not_available_for_this_template";s:59:"Currently, the variable is not available for this template.";s:17:"document_uploaded";s:18:"Document uploaded:";s:21:"this_campaign_send_to";s:23:"This campaign sent to :";s:12:"notification";s:12:"Notification";s:9:"clear_all";s:9:"Clear All";s:25:"development_warning_title";s:49:"Application is running in development/debug mode!";s:27:"development_warning_content";s:89:"To optimize performance and security, change the settings in your `.env` file as follows:";s:7:"app_env";s:8:"APP_ENV:";s:9:"app_debug";s:10:"APP_DEBUG:";s:10:"production";s:10:"production";s:11:"debug_false";s:5:"false";s:27:"development_warning_details";s:89:"In development or debug mode, you may encounter detailed errors and deprecation warnings.";s:24:"performance_security_tip";s:80:"Always use production mode unless actively working on new features or debugging.";s:15:"change_language";s:15:"Change Language";s:4:"edit";s:4:"Edit";s:3:"ids";s:2:"ID";s:5:"clone";s:5:"Clone";s:20:"type_and_press_enter";s:22:"Type and press Enter..";s:21:"delete_campaign_title";s:15:"Delete Campaign";s:13:"edit_campaign";s:13:"Edit Campaign";s:29:"campaign_created_successfully";s:27:"Campaign saved successfully";s:28:"campaign_update_successfully";s:29:"Campaign updated successfully";s:17:"run_cron_manually";s:17:"Run Cron Manually";s:7:"running";s:7:"Running";s:16:"cron_not_running";s:20:"Cron Job Not Running";s:13:"cron_last_run";s:8:"Last Run";s:23:"please_check_cron_setup";s:70:"Please check your cron job setup. It has not run in the last 48 hours.";s:14:"delete_account";s:14:"Delete Account";s:22:"delete_account_message";s:183:"Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain.";s:27:"account_delete_confirmation";s:45:"Are you sure you want to delete your account?";s:23:"account_delete_password";s:177:"Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account.";s:15:"update_password";s:15:"Update Password";s:23:"update_password_message";s:68:"Ensure your account is using a long, random password to stay secure.";s:16:"current_password";s:16:"Current Password";s:12:"new_password";s:12:"New Password";s:5:"saved";s:5:"Saved";s:19:"profile_information";s:19:"Profile Information";s:26:"update_profile_information";s:60:"Update your account's profile information and email address.";s:16:"email_unverified";s:33:"Your email address is unverified.";s:25:"resend_email_verification";s:45:"Click here to re-send the verification email.";s:31:"verification_link_sent_to_email";s:60:"A new verification link has been sent to your email address.";s:13:"csv_file_only";s:13:"CSV file only";s:25:"csv_uploaded_successfully";s:25:"CSV uploaded successfully";s:28:"please_upload_valid_csv_file";s:28:"Please upload valid CSV File";s:13:"import_failed";s:13:"Import failed";s:22:"please_select_csv_file";s:22:"Please select CSV File";s:13:"file_selected";s:13:"File selected";s:16:"import_completed";s:30:"Contacts imported successfully";s:5:"leads";s:5:"leads";s:16:"campaign_details";s:16:"Campaign Details";s:17:"back_to_campaigns";s:17:"Back to Campaigns";s:19:"create_new_campaign";s:19:"Create New Campaign";s:21:"campaign_name_capital";s:13:"CAMPAIGN NAME";s:14:"status_capital";s:6:"STATUS";s:8:"executed";s:8:"EXECUTED";s:16:"template_capital";s:8:"TEMPLATE";s:20:"scheduled_at_capital";s:12:"SCHEDULED AT";s:14:"total_contacts";s:14:"TOTAL CONTACTS";s:23:"all_contacts_from_group";s:23:"All contacts from group";s:5:"total";s:6:"Total ";s:15:"total_delivered";s:15:"TOTAL DELIVERED";s:8:"messages";s:8:"Messages";s:10:"total_read";s:10:"TOTAL READ";s:19:"total_read_messages";s:13:"Messages Read";s:12:"total_failed";s:12:"TOTAL FAILED";s:4:"rate";s:4:"Rate";s:10:"out_of_the";s:10:"Out of the";s:24:"records_in_your_csv_file";s:25:"records in your CSV file,";s:17:"records_are_valid";s:18:"records are valid.";s:35:"campaign_successfully_sent_to_these";s:47:" The campaign can be successfully sent to these";s:4:"from";s:5:"From:";s:9:"all_chats";s:9:"All Chats";s:9:"searching";s:9:"Searching";s:7:"filters";s:7:"Filters";s:11:"select_type";s:11:"Select Type";s:6:"agents";s:6:"Agents";s:6:"groups";s:6:"groups";s:12:"select_group";s:12:"Select Group";s:13:"select_source";s:13:"Select Source";s:13:"select_status";s:13:"Select Status";s:13:"select_assign";s:20:"Select Assigned Name";s:18:"click_user_to_chat";s:18:"Click user to chat";s:14:"24_hours_limit";s:14:"24 hours limit";s:37:"whatsapp_block_message_24_hours_after";s:39:"WhatsApp blocks messages 24 hours after";s:39:"the_last_template_message_still_be_sent";s:58:"the last contact, but template messages can still be sent.";s:6:"search";s:6:"Search";s:12:"reply_within";s:12:"Reply within";s:9:"hours_and";s:9:"hours and";s:17:"minutes_remaining";s:17:"minutes remaining";s:5:"share";s:5:"Share";s:16:"user_information";s:16:"User Information";s:17:"download_document";s:17:"Download Document";s:18:"delete_notes_title";s:11:"Delete Note";s:5:"reply";s:5:"Reply";s:7:"details";s:7:"Details";s:11:"replying_to";s:13:" Replying to:";s:33:"your_broser_not_support_video_tag";s:45:" Your browser does not support the video tag.";s:9:"user_info";s:9:"User Info";s:15:"select_language";s:15:"Select Language";s:10:"english_to";s:10:"English To";s:6:"active";s:6:"Active";s:27:"support_has_already_expired";s:28:"Support has already expired.";s:13:"renew_support";s:13:"Renew Support";s:26:"do_you_want_custom_service";s:62:"Do you want custom services? Visit here and create your ticket";s:21:"create_support_ticket";s:21:"Create Support Ticket";s:19:"version_information";s:19:"Version Information";s:12:"your_version";s:12:"Your Version";s:14:"latest_version";s:14:"Latest Version";s:12:"purchase_key";s:13:" Purchase Key";s:8:"username";s:8:"Username";s:15:"download_update";s:15:"Download Update";s:25:"before_update_description";s:155:"Before performing an update, it is strongly recommended to create a full backup of your current installation (files and database) and review the changelog.";s:10:"change_log";s:10:"Change Log";s:7:"version";s:7:"Version";s:34:"added_new_feature_and_improvements";s:35:"Added new features and improvements";s:38:"bug_fixes_and_performance_improvements";s:38:"Bug fixes and performance improvements";s:28:"campaign_paused_successfully";s:28:"Campaign paused successfully";s:29:"campaign_resumed_successfully";s:29:"Campaign resumed successfully";s:15:"resume_campaign";s:15:"Resume Campaign";s:14:"pause_campaign";s:14:"Pause Campaign";s:18:"dashboard_overview";s:18:"Dashboard Overview";s:33:"note_will_be_available_in_contact";s:52:"Notes will be available once the contact is created.";s:17:"other_information";s:17:"Other Information";s:18:"no_notes_available";s:19:"No notes available.";s:17:"records_processed";s:17:"records processed";s:28:"record_successfully_inserted";s:31:" Records successfully inserted:";s:18:"records_with_error";s:20:"Records with errors:";s:12:"import_error";s:13:"Import Errors";s:13:"prompt_action";s:13:"Prompt Action";s:11:"sent_status";s:11:"Sent status";s:12:"delivered_to";s:12:"Delivered To";s:8:"ready_by";s:7:"Read By";s:10:"created_at";s:10:"Created At";s:12:"not_assigned";s:12:"Not Assigned";s:6:"public";s:6:"Public";s:21:"canned_reply_activate";s:25:"Canned reply is activated";s:23:"canned_reply_deactivate";s:27:"Canned reply is deactivated";s:19:"no_contact_selected";s:20:"No contacts selected";s:27:"contact_enable_successfully";s:28:"Contact enabled successfully";s:29:"contact_disabled_successfully";s:29:"Contact disabled successfully";s:4:"code";s:4:"Code";s:9:"translate";s:9:"Translate";s:14:"canned_replies";s:14:"Canned Replies";s:13:"creation_time";s:13:"Creation Time";s:13:"last_activity";s:13:"Last Activity";s:32:"translation_updated_successfully";s:32:"Translation updated successfully";s:24:"message_bot_is_activated";s:24:"Message Bot is activated";s:26:"message_bot_is_deactivated";s:26:"Message Bot is deactivated";s:21:"message_bot_not_found";s:21:"Message bot not found";s:19:"invalid_file_format";s:24:"Invalid file name format";s:23:"original_file_not_found";s:23:"Original file not found";s:22:"bot_clone_successfully";s:22:"Bot clone successfully";s:21:"template_bot_activate";s:24:"Template Bot is activate";s:23:"template_bot_deactivate";s:26:"Template Bot is deactivate";s:22:"template_bot_not_found";s:22:"Template bot not found";s:8:"category";s:8:"Category";s:13:"template_type";s:13:"Template Type";s:9:"body_data";s:9:"Body Data";s:18:"delete_templatebot";s:19:"Delete Template Bot";s:13:"response_code";s:13:"Response Code";s:4:"view";s:4:"View";s:11:"bulk_delete";s:11:"Bulk Delete";s:19:"sql_injection_error";s:55:"This field contain invalid characters, script, or HTML.";s:30:"message_bot_saved_successfully";s:30:"Message bot saved successfully";s:31:"delete_message_bot_successfully";s:32:"Message bot deleted successfully";s:32:"template_bot_delete_successfully";s:33:"Template bot deleted successfully";s:28:"campaign_delete_successfully";s:29:"Campaign deleted successfully";s:14:"file_not_found";s:14:"File not found";s:19:"download_successful";s:19:"Download successful";s:28:"ai_prompt_saved_successfully";s:28:"AI Prompt saved successfully";s:30:"ai_prompt_updated_successfully";s:30:"AI Prompt updated successfully";s:29:"ai_prompt_delete_successfully";s:30:"AI Prompt deleted successfully";s:30:"canned_reply_save_successfully";s:31:"Canned Reply saved successfully";s:32:"canned_reply_update_successfully";s:33:"Canned Reply updated successfully";s:32:"canned_reply_delete_successfully";s:33:"Canned Reply deleted successfully";s:23:"note_added_successfully";s:23:"Note added successfully";s:24:"note_delete_successfully";s:25:"Note deleted successfully";s:28:"contact_created_successfully";s:28:"Contact created successfully";s:27:"contact_update_successfully";s:28:"Contact updated successfully";s:28:"contacts_delete_successfully";s:29:"Contacts deleted successfully";s:22:"contact_delete_success";s:28:"Contact deleted successfully";s:33:"an_error_occured_deleting_contact";s:45:"An error occurred while deleting the contacts";s:16:"phone_validation";s:70:"The phone number must be in international format (e.g., +12345678901).";s:16:"invalid_csv_file";s:16:"Invalid CSV file";s:24:"missing_required_columns";s:23:"Missing required column";s:21:"sample_file_not_found";s:21:"Sample file not found";s:25:"source_saved_successfully";s:25:"Source saved successfully";s:26:"source_update_successfully";s:27:"Source updated successfully";s:26:"source_delete_successfully";s:27:"Source deleted successfully";s:24:"status_save_successfully";s:25:"Status saved successfully";s:26:"status_update_successfully";s:27:"Status updated successfully";s:26:"status_delete_successfully";s:27:"Status deleted successfully";s:27:"status_delete_in_use_notify";s:35:"The Status ID is already being used";s:21:"no_activity_log_found";s:32:"No activity logs found to delete";s:21:"activity_logs_deleted";s:22:" Activity logs deleted";s:14:"system_default";s:14:"System Default";s:11:"log_deleted";s:11:"Log deleted";s:13:"log_not_found";s:13:"Log not found";s:14:"clear_all_logs";s:14:"Clear all logs";s:30:"template_activate_successfully";s:31:"Template activated successfully";s:32:"template_deactivate_successfully";s:33:"Template deactivated successfully";s:34:"email_template_update_successfully";s:36:"Email-Template updated successfully!";s:22:"role_save_successfully";s:23:"Role saved successfully";s:24:"role_delete_successfully";s:25:"Role deleted successfully";s:27:"profile_update_successfully";s:28:"Profile updated successfully";s:33:"edit_english_language_not_allowed";s:43:"Editing the English language is not allowed";s:28:"language_update_successfully";s:29:"Language updated successfully";s:27:"language_added_successfully";s:27:"Language added successfully";s:23:"language_handling_error";s:52:"There was an error while handling the language files";s:12:"deleting_the";s:12:"Deleting the";s:23:"language_is_not_allowed";s:23:"Language is not allowed";s:28:"language_delete_successfully";s:30:" language deleted successfully";s:25:"setting_save_successfully";s:27:"Settings saved successfully";s:26:"cache_cleared_successfully";s:27:" cache cleared successfully";s:16:"cache_management";s:16:"Cache Management";s:5:"never";s:5:"Never";s:26:"failed_to_execute_cron_job";s:27:"Failed to execute cron job:";s:24:"email_config_is_required";s:31:"Email Configuration is required";s:23:"email_sent_successfully";s:23:"Email sent successfully";s:24:"failed_to_send_test_mail";s:26:"Failed to send test email:";s:19:"remove_successfully";s:22:" removed successfully!";s:31:"api_setting_update_successfully";s:33:"API settings updated successfully";s:31:"fill_required_pusher_credential";s:49:"Please fill all required Pusher credentials first";s:25:"pusher_is_not_initialized";s:64:"Pusher service is not initialized, Please check your credentials";s:5:"hello";s:5:"Hello";s:27:"your_real_time_notification";s:48:"your real-time notification setup is good to go!";s:33:"pusher_connection_test_successful";s:34:"Pusher connection test successful!";s:29:"pusher_connection_test_failed";s:58:"Pusher connection test failed, Check logs for more details";s:28:"pusher_test_connection_error";s:29:"Pusher test connection error:";s:29:"pusher_test_connection_failed";s:30:"Pusher test connection failed:";s:22:"user_save_successfully";s:23:"User saved successfully";s:24:"user_update_successfully";s:25:"User updated successfully";s:24:"user_delete_successfully";s:25:"User deleted successfully";s:18:"user_in_use_notify";s:33:"The User ID is already being used";s:28:"webhook_connect_successfully";s:30:"Webhook connected successfully";s:28:"account_connect_successfully";s:30:"Account connected successfully";s:31:"recipient_phone_number_required";s:34:"Recipient phone number is required";s:25:"message_template_required";s:28:"Message template is required";s:27:"invalid_phone_number_format";s:27:"Invalid phone number format";s:17:"language_file_for";s:17:"Language file for";s:9:"not_found";s:9:"Not found";s:26:"failed_to_decode_json_from";s:27:"Failed to decode JSON from ";s:27:"error_loading_language_file";s:28:"Error loading language file:";s:33:"error_fetching_language_key_value";s:35:"Error fetching language key value: ";s:5:"today";s:5:"Today";s:9:"yesterday";s:9:"Yesterday";s:27:"notification_marked_as_read";s:27:"Notification marked as read";s:31:"all_notification_marked_as_read";s:32:"All notifications marked as read";s:32:"notification_delete_successfully";s:33:"Notification deleted successfully";s:24:"all_notification_cleared";s:25:"All notifications cleared";s:8:"just_now";s:8:"just now";s:7:"min_ago";s:7:"min ago";s:9:"hours_ago";s:9:"hours ago";s:8:"days_ago";s:8:"days ago";s:23:"failed_to_fetch_contact";s:24:"Failed to fetch contacts";s:14:"user_not_found";s:14:"User not found";s:24:"thank_you_for_signing_up";s:86:"Thank you for signing up! Please verify your email address by clicking the link below:";s:33:"email_sent_successfull_with_emoji";s:29:"Email sent successfully! 🎉";s:24:"email_not_sent_try_again";s:57:"Oops! Email Not Sent Please try again or contact support.";s:28:"email_service_not_configured";s:75:"Email service is not configured properly. Please contact the administrator.";s:22:"email_recaptcha_failed";s:48:"reCAPTCHA verification failed. Please try again.";s:29:"provided_credential_not_match";s:50:"The provided credentials do not match our records.";s:22:"verification_link_sent";s:22:"verification-link-sent";s:18:"verification_error";s:18:"verification-error";s:16:"password_updated";s:16:"password-updated";s:34:"failed_to_send_password_reset_link";s:70:"Failed to send password-reset email. Please contact the administrator.";s:14:"profile_update";s:15:"profile-updated";s:13:"access_denied";s:13:"Access denied";s:22:"api_access_is_disabled";s:22:"API access is disabled";s:17:"invalid_api_token";s:17:"Invalid API token";s:33:"token_not_have_required_abilities";s:42:"Token does not have the required abilities";s:23:"failed_to_load_template";s:24:"Failed to load templates";s:18:"template_not_found";s:18:"Template not found";s:11:"template_id";s:11:"Template ID";s:37:"no_found_in_whatsapp_business_account";s:38:"not found in WhatsApp Business Account";s:15:"is_not_approved";s:32:"is not approved. Current status:";s:7:"modules";s:7:"Modules";s:32:"prepare_to_send_campaign_message";s:34:"Preparing to send campaign message";s:23:"campaign_message_result";s:23:"Campaign message result";s:23:"campaign_message_failed";s:23:"Campaign message failed";s:34:"whatsapp_message_sent_successfully";s:34:"WhatsApp message sent successfully";s:23:"whatsapp_message_failed";s:23:"WhatsApp message failed";s:31:"failed_to_send_whatsapp_message";s:37:"Failed to send WhatsApp message after";s:8:"attempts";s:8:"attempts";s:35:"whatsapp_message_failed_permanently";s:35:"WhatsApp message failed permanently";s:11:"other_group";s:11:"other-group";s:15:"default_subject";s:15:"Default Subject";s:20:"default_body_content";s:20:"Default Body Content";s:5:"there";s:5:"there";s:31:"verify_email_notification_error";s:31:"VerifyEmail Notification Error:";s:24:"build_mail_message_error";s:26:"Build Mail Message Error: ";s:33:"verification_url_generation_error";s:35:"Verification URL Generation Error: ";s:35:"set_verification_url_callback_error";s:37:"Set Verification URL Callback Error: ";s:23:"set_mail_callback_error";s:25:"Set Mail Callback Error: ";s:27:"lear_created_event_occurred";s:28:"Lead Created Event occurred:";s:27:"whatsapp_message_job_failed";s:27:"WhatsApp message job failed";s:21:"attack_injection_risk";s:70:":attribute contains invalid input (HTML, SQL, or JSON injection risk).";s:23:"potential_sql_injection";s:33:"Potential SQL injection detected.";s:24:"potential_json_injection";s:34:"Potential JSON injection detected.";s:41:"validation_failed_due_to_unexpected_error";s:45:"Validation failed due to an unexpected error.";s:20:"failed_to_send_email";s:22:"Failed to send email: ";s:5:"class";s:5:"Class";s:14:"does_not_exist";s:15:"does not exist.";s:14:"must_implement";s:43:" must implement 'name' and 'build' methods.";s:26:"module_json_file_not_found";s:26:"module.json file not found";s:19:"invalid_json_format";s:34:"Invalid JSON format in module.json";s:22:"invalid_version_format";s:69:"Invalid version format. Must follow semantic versioning (e.g., 1.0.0)";s:33:"module_must_have_service_provider";s:46:"Module must have at least one service provider";s:23:"invalid_provider_format";s:23:"Invalid provider format";s:18:"pusher_inti_failed";s:30:"Pusher initialization failed: ";s:28:"pusher_initialization_failed";s:25:"Pusher is not initialized";s:27:"pusher_batch_trigger_failed";s:27:"Pusher batch trigger failed";s:22:"pusher_not_initialized";s:22:"Pusher not initialized";s:21:"pusher_trigger_failed";s:23:"Pusher trigger failed: ";s:28:"unable_to_read_composer_lock";s:28:"Unable to read composer.lock";s:14:"module_manager";s:14:"Module Manager";s:13:"upload_module";s:13:"Upload Module";s:10:"upload_now";s:10:"Upload Now";s:9:"uploading";s:12:"Uploading...";s:6:"module";s:6:"Module";s:14:"documentations";s:14:" Documentation";s:4:"core";s:4:"Core";s:5:"addon";s:5:"Addon";s:8:"inactive";s:8:"Inactive";s:7:"install";s:7:"Install";s:5:"reset";s:5:"Reset";s:12:"select_model";s:12:"Select Model";s:16:"settings_webhook";s:18:"Webhook Management";s:22:"select_smtp_encryption";s:22:"Select SMTP Encryption";s:14:"bot_protection";s:14:"Bot Protection";s:26:"scheduled_tasks_management";s:26:"Scheduled Tasks Management";s:26:"search_engine_optimization";s:26:"Search Engine Optimization";s:28:"real_time_event_broadcasting";s:28:"Real-Time Event Broadcasting";s:40:"real_time_event_broadcasting_description";s:106:"Set up real-time communication channels for instant updates and live interactions across your application.";s:26:"api_integration_and_access";s:24:"API Integration & Access";s:38:"api_integration_and_access_description";s:182:"Securely extend your application's functionality through our robust API, enabling seamless data exchange, custom integrations, and programmatic access to your system's core resources";s:8:"contacts";s:8:"Contacts";s:15:"contacts_create";s:15:"Contacts Create";s:13:"contacts_read";s:13:"Contacts Read";s:15:"contacts_update";s:15:"Contacts Update";s:15:"contacts_delete";s:15:"Contacts Delete";s:8:"statuses";s:8:"Statuses";s:13:"status_create";s:13:"Status Create";s:11:"status_read";s:11:"Status Read";s:13:"status_update";s:13:"Status Update";s:13:"status_delete";s:13:"Status Delete";s:7:"sources";s:7:"Sources";s:13:"source_create";s:13:"Source Create";s:11:"source_read";s:11:"Source Read";s:13:"source_update";s:13:"Source Update";s:13:"source_delete";s:13:"Source Delete";s:20:"webhook_integrations";s:20:"Webhook Integrations";s:32:"webhook_integrations_description";s:235:"Webhooks allow real-time notifications and seamless data synchronization between your application and external services. Configure endpoints to receive instant updates about contacts, statuses, and other critical events in your system.";s:21:"enable_webhook_access";s:21:"Enable Webhook Access";s:17:"webhook_abilities";s:17:"Webhook Abilities";s:38:"default_permissions_for_webhook_access";s:37:"Default permission for webhook access";s:7:"general";s:7:"General";s:20:"default_phone_number";s:20:"Default Phone Number";s:15:"messaging_limit";s:13:"Message Limit";s:23:"additional_phone_number";s:23:"Additional Phone Number";s:19:"messages_sent_today";s:19:"messages sent today";s:21:"increase_webhook_note";s:79:" Enabling this option will increase your webhook load. For more info visit this";s:24:"fix_spelling_and_grammar";s:22:"Fix Spelling & Grammar";s:17:"simplify_language";s:17:"Simplify Language";s:11:"get_qr_code";s:11:"Get QR Code";s:10:"disconnect";s:10:"Disconnect";s:18:"disconnect_message";s:36:"Are You Sure You Want to Disconnect?";s:3:"add";s:3:"Add";s:4:"lead";s:4:"Lead";s:8:"customer";s:8:"Customer";s:21:"users_using_this_role";s:29:"List of users using this role";s:24:"total_send_campaign_list";s:24:"Total Send Campaign List";s:35:"please_add_valid_number_in_csv_file";s:35:"Please add valid number in csv file";s:22:"chat_cleanup_completed";s:20:"Chat Cleanup Summary";s:14:"found_messages";s:57:"Found :count messages older than the specified timeframe.";s:16:"deleted_messages";s:28:"Deleted :count old messages.";s:21:"deleted_conversations";s:40:"Removed :count empty chat conversations.";s:20:"error_during_cleanup";s:45:"An error occurred during the cleanup process.";s:7:"dismiss";s:7:"Dismiss";s:15:"run_cleanup_now";s:15:"Run Cleanup Now";s:19:"confirm_run_cleanup";s:99:"Are you sure you want to run the cleanup now? This will delete old messages based on your settings.";s:35:"chat_cleanup_completed_successfully";s:36:"Chat cleanup completed successfully.";s:19:"chat_cleanup_failed";s:19:"Chat cleanup failed";s:27:"please_specify_days_to_keep";s:74:"Please specify the number of days to keep messages before running cleanup.";s:9:"edit_role";s:9:"Edit Role";s:17:"send_welcome_mail";s:18:"Send Welcome Email";s:22:"send_verification_mail";s:16:"Is Verified User";s:21:"email_template_editor";s:21:"Email Template Editor";s:13:"template_name";s:13:"Template Name";s:7:"subject";s:7:"Subject";s:11:"user_fields";s:11:"User Fields";s:14:"contact_fields";s:14:"Contact Fields";s:12:"other_fields";s:12:"Other Fields";s:13:"save_template";s:13:"Save Template";s:35:"email_template_updated_successfully";s:35:"Email Template updated successfully";s:22:"available_merge_fields";s:22:"Available Merge Fields";s:17:"user_not_verified";s:57:"Access restricted. Please verify your account to proceed.";s:25:"no_merge_fields_available";s:48:"No available merge fields for this template type";s:14:"of_total_leads";s:19:"of your total leads";s:10:"total_fail";s:15:"Messages Failed";s:18:"messages_delivered";s:18:"Messages Delivered";s:16:"sending_campaign";s:19:"Sending Campaign...";s:27:"this_may_take_a_few_moments";s:27:"This May Take A Few Moments";s:10:"sending_to";s:10:"Sending To";s:27:"this_trigger_already_exists";s:36:"This trigger keyword already exists.";s:16:"cover_page_image";s:16:"Cover Page Image";s:20:"personal_information";s:20:"Personal Information";s:21:"roles_and_permissions";s:19:"Roles & Permissions";s:11:"permissions";s:11:"Permissions";s:20:"administrator_access";s:20:"Administrator Access";s:16:"enter_first_name";s:16:"Enter first name";s:15:"enter_last_name";s:15:"Enter last name";s:19:"enter_email_address";s:19:"Enter email address";s:14:"enter_password";s:14:"Enter password";s:36:"leave_blank_to_keep_current_password";s:36:"Leave blank to keep current password";s:12:"toggle_admin";s:20:"Toggle Administrator";s:2:"on";s:2:"ON";s:3:"off";s:3:"OFF";s:31:"sends_welcome_email_to_new_user";s:63:"Sends a welcome email to the user when their account is created";s:43:"mark_email_as_verified_or_send_verification";s:49:"Mark email as verified or send verification email";s:15:"required_fields";s:15:"Required fields";s:9:"from_role";s:9:"from role";s:18:"administrator_info";s:25:"Administrator Information";s:60:"administrators_have_full_access_to_all_features_and_settings";s:75:"Administrators have full access to all features and settings of the system.";s:42:"admin_user_has_full_access_to_all_features";s:75:"Administrator users have unrestricted access to all features and functions.";s:36:"tap_on_a_feature_to_view_permissions";s:46:"Tap on a feature to view available permissions";s:3:"tip";s:3:"Tip";s:18:"use_browser_search";s:18:"Use browser search";s:28:"to_find_specific_permissions";s:28:"to find specific permissions";s:19:"user_create_success";s:25:"User created successfully";s:19:"user_update_success";s:25:"User updated successfully";s:19:"user_delete_success";s:25:"User deleted successfully";s:19:"user_delete_confirm";s:42:"Are you sure you want to delete this user?";s:17:"password_mismatch";s:22:"Passwords do not match";s:24:"password_changed_success";s:29:"Password changed successfully";s:22:"password_changed_error";s:23:"Error changing password";s:26:"profile_image_upload_error";s:29:"Error uploading profile image";s:28:"profile_image_remove_success";s:34:"Profile image removed successfully";s:19:"validation_required";s:33:"The :attribute field is required.";s:16:"validation_email";s:45:"The :attribute must be a valid email address.";s:17:"validation_unique";s:38:"The :attribute has already been taken.";s:14:"validation_min";s:48:"The :attribute must be at least :min characters.";s:20:"validation_confirmed";s:43:"The :attribute confirmation does not match.";s:19:"not_allowed_to_view";s:20:"Not allowed to view.";s:18:"role_in_use_notify";s:30:"The Role is already being used";s:15:"enter_role_name";s:15:"Enter role name";s:33:"your_campaign_is_already_executed";s:33:"Your Campaign is already executed";s:6:"failed";s:6:"Failed";s:11:"in_progress";s:11:"In Progress";s:11:"remove_chat";s:11:"Remove Chat";s:4:"info";s:11:"Information";s:6:"emojis";s:6:"Emojis";s:18:"attach_img_doc_vid";s:28:"Attach Image Video Documents";s:12:"record_audio";s:12:"Record Audio";s:4:"more";s:4:"More";s:19:"click_to_open_leads";s:19:"Click to open leads";s:22:"this_field_is_required";s:22:"This field is required";s:18:"message_statistics";s:18:"Message statistics";s:13:"system_status";s:13:"System status";s:19:"whatsapp_connection";s:19:"Whatsapp connection";s:10:"api_status";s:10:"Api status";s:10:"queue_size";s:10:"Queue size";s:15:"daily_api_calls";s:15:"Daily api calls";s:9:"this_week";s:9:"This week";s:9:"last_week";s:9:"Last week";s:5:"month";s:5:"Month";s:11:"system_logs";s:11:"System Logs";s:35:"support_agent_assigned_successfully";s:35:"Support agent assigned successfully";s:15:"connect_account";s:15:"Connect Account";s:10:"log_viewer";s:10:"Log Viewer";s:12:"no_log_files";s:12:"No log files";s:7:"refresh";s:7:"Refresh";s:8:"per_page";s:8:"per page";s:9:"emergency";s:9:"EMERGENCY";s:5:"alert";s:5:"ALERT";s:8:"critical";s:8:"CRITICAL";s:5:"error";s:5:"ERROR";s:7:"warning";s:7:"WARNING";s:6:"notice";s:6:"NOTICE";s:8:"info_log";s:4:"INFO";s:5:"debug";s:5:"DEBUG";s:5:"local";s:5:"LOCAL";s:5:"level";s:5:"Level";s:7:"content";s:7:"Content";s:7:"actions";s:7:"Actions";s:20:"no_log_file_selected";s:20:"No log file selected";s:42:"no_log_entries_found_the_file_may_be_empty";s:44:"No log entries found. The file may be empty.";s:14:"no_log_entries";s:38:"No log entries matching your criteria.";s:12:"showing_page";s:12:"Showing page";s:2:"of";s:2:"of";s:4:"last";s:4:"Last";s:4:"next";s:4:"Next";s:8:"previous";s:8:"Previous";s:11:"environment";s:11:"Environment";s:15:"delete_log_file";s:15:"Delete Log File";s:28:"delete_log_file_confirmation";s:76:"Are you sure you want to delete this log file? This action cannot be undone.";s:19:"contact_information";s:19:"Contact Information";s:33:"webhook_discoonected_successfully";s:33:"Webhook disconnected successfully";s:28:"error_processing_ai_response";s:28:"Error processing AI response";s:24:"chat_delete_successfully";s:25:"Chat deleted successfully";s:18:"access_denied_note";s:44:"You're not authorized to access this feature";s:29:"user_deactivated_successfully";s:29:"User deactivated successfully";s:27:"user_activated_successfully";s:27:"User activated successfully";s:33:"user_deactivated_message_in_login";s:58:"Your account has been deactivated. Please contact support.";s:29:"account_cannot_be_deactivated";s:35:"This account cannot be deactivated.";s:5:"admin";s:5:"Admin";s:20:"pusher_account_setup";s:20:"Pusher Account Setup";s:32:"pusher_account_setup_description";s:119:" It seems that your Pusher account is not configured correctly. Please complete the setup to enable real-time features.";s:22:"access_system_settings";s:23:" Access System Settings";s:28:"navigate_to_whatsmark_system";s:49:" Navigate to Whatsmark System Settings → Pusher";s:20:"follow_documentation";s:20:"Follow Documentation";s:32:"read_the_whatsmark_documentation";s:64:"Read the Whatsmark documentation for detailed setup instructions";s:49:"real_time_notification_require_pusher_integration";s:50:"Real-time notifications require Pusher integration";s:11:"change_tone";s:11:"Change Tone";s:16:"no_country_found";s:18:" No country found.";s:13:"custom_prompt";s:13:"Custom Prompt";s:15:"no_result_found";s:17:"No results found.";s:29:"recaptcha_verification_failed";s:29:"Recaptcha verification failed";s:23:"failed_to_fetch_sources";s:23:"Failed to fetch sources";s:24:"failed_to_fetch_statuses";s:24:"Failed to fetch statuses";s:23:"message_bot_save_failed";s:23:"Message bot save failed";s:20:"campaign_save_failed";s:20:"Campaign save failed";s:18:"file_upload_failed";s:18:"File upload failed";s:21:"ai_prompt_save_failed";s:21:"AI prompt save failed";s:21:"ai_prompt_edit_failed";s:21:"AI prompt edit failed";s:23:"ai_prompt_delete_failed";s:23:"AI prompt delete failed";s:24:"canned_reply_save_failed";s:24:"Canned reply save failed";s:24:"canned_reply_edit_failed";s:24:"Canned reply edit failed";s:26:"canned_reply_delete_failed";s:26:"Canned reply delete failed";s:20:"error_deleting_notes";s:20:"Error deleting notes";s:19:"mail_sending_failed";s:19:"Mail sending failed";s:22:"mail_successfully_sent";s:23:"Mail Successfully Sent.";s:21:"invalid_email_address";s:21:"Invalid email address";s:23:"redis_connection_failed";s:24:"Redis connection failed:";s:30:"database_info_retrieval_failed";s:31:"Database info retrieval failed:";s:39:"unable_to_retrieve_database_information";s:39:"Unable to retrieve database information";s:29:"error_executing_shell_command";s:29:"Error executing shell command";s:38:"failed_to_clear_cache_after_env_change";s:39:"Failed to clear cache after env change:";s:38:"environment_file_changed_cache_cleared";s:40:"Environment file changed, cache cleared ";s:23:"failed_to_delete_backup";s:24:"Failed to delete backup ";s:13:"backup_failed";s:14:"Backup failed ";s:30:"failed_to_remove_profile_image";s:31:"Failed to remove profile image.";s:14:"campaign_error";s:14:"Campaign error";s:28:"message_deleted_successfully";s:28:"Message deleted successfully";s:18:"source_save_failed";s:18:"Source save failed";s:20:"source_delete_failed";s:20:"Source delete failed";s:18:"status_save_failed";s:18:"Status save failed";s:20:"status_delete_failed";s:20:"Status delete failed";s:28:"email_template_update_failed";s:28:"Email template update failed";s:16:"role_save_failed";s:16:"Role save failed";s:21:"profile_update_failed";s:21:"Profile update failed";s:22:"language_delete_failed";s:22:"Language delete failed";s:48:"the_translation_cannot_be_a_JSON_object_or_array";s:49:"The translation cannot be a JSON object or array.";s:16:"user_save_failed";s:16:"User save failed";s:22:"account_connect_failed";s:22:"Account connect failed";s:25:"webhook_disconnect_failed";s:25:"Webhook disconnect failed";s:22:"webhook_connect_failed";s:22:"Webhook connect failed";s:19:"dynamic_input_error";s:62:"Some fields contain invalid characters or unsupported content.";s:28:"your_account_is_discconected";s:29:"Your Account Is Disconnected!";s:17:"disconnected_info";s:171:"Your account is no longer connected to our system. This may be due to an expired token, a disconnected webhook, an invalid token, or changes in your Meta account settings.";s:30:"allowed_fromats_jpeg_png_max_5";s:37:"Allowed formats: jpeg, png (Max: 5MB)";s:20:"new_password_changed";s:39:"Password has been changed successfully.";s:23:"change_password_heading";s:20:"Change Your Password";s:31:"no_permission_to_perform_action";s:48:"You don't have permission to perform this action";s:16:"in_this_campaign";s:16:"In This Campaign";s:17:"enable_debug_mode";s:17:"Enable Debug Mode";s:19:"enable_whatsapp_log";s:19:"Enable Whatsapp Log";s:19:"environment_updated";s:20:"Environment updated!";s:20:"whatsapp_log_updated";s:21:"Whatsapp log updated!";s:22:"enable_production_mode";s:22:"Enable production mode";s:35:"enable_production_mode_successfully";s:35:"Enable production mode successfully";s:36:"disable_production_mode_successfully";s:36:"Disable production mode successfully";s:9:"changelog";s:9:"Changelog";s:11:"new_feature";s:11:"New Feature";s:11:"improvement";s:11:"Improvement";s:7:"bug_fix";s:7:"Bug Fix";s:30:"cron_job_executed_successfully";s:30:"Cron Job executed successfully";s:15:"resend_campaign";s:15:"Resend Campaign";s:33:"campaign_resend_process_initiated";s:33:"Campaign resend process initiated";s:29:"you_cant_resend_this_campaign";s:32:"you can not resend this campaign";}