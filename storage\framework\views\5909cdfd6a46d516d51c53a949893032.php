<div class="px-8 md:px-0">
   <?php $__env->slot('title', null, []); ?> 
    <?php echo e(t('email_template_list_title')); ?>

   <?php $__env->endSlot(); ?>

  <div class="mt-4 max-w-2xl">
    <!-- Templates List -->
    <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['class' => 'rounded-lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'rounded-lg']); ?>
       <?php $__env->slot('header', null, []); ?> 
        <?php if (isset($component)) { $__componentOriginal32b3aedb79dcb21d2517daf1cd4b81ff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal32b3aedb79dcb21d2517daf1cd4b81ff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.settings-heading','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('settings-heading'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
          <?php echo e(t('email_template_list_title')); ?>

         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal32b3aedb79dcb21d2517daf1cd4b81ff)): ?>
<?php $attributes = $__attributesOriginal32b3aedb79dcb21d2517daf1cd4b81ff; ?>
<?php unset($__attributesOriginal32b3aedb79dcb21d2517daf1cd4b81ff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal32b3aedb79dcb21d2517daf1cd4b81ff)): ?>
<?php $component = $__componentOriginal32b3aedb79dcb21d2517daf1cd4b81ff; ?>
<?php unset($__componentOriginal32b3aedb79dcb21d2517daf1cd4b81ff); ?>
<?php endif; ?>
       <?php $__env->endSlot(); ?>
       <?php $__env->slot('content', null, []); ?> 
        <div>
          <ul role="list" class="space-y-4">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $templates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <li
                class="flex items-center justify-between bg-white dark:bg-slate-800 px-4 py-6 rounded-lg ring-1 ring-slate-300 sm:rounded-lg dark:bg-transparent dark:ring-slate-600 hover:bg-slate-50 dark:hover:bg-slate-700 transition">
                <!-- Template Info -->
                <div class="flex flex-col sm:flex-row sm:justify-between w-full">
                  <!-- Content Section -->
                  <div class="flex items-start sm:items-center space-x-4 w-full">
                    <div class="flex flex-col sm:flex-row sm:justify-between w-full">
                      <div class="max-w-md text-sm">
                        <h4 class="font-medium text-slate-900 dark:text-slate-200 truncate">
                          <a href="<?php echo e(route('admin.emails.save', $item->id)); ?>"
                            class="hover:text-blue-500 dark:hover:text-blue-400">
                            <?php echo e($item->name); ?>

                          </a>
                        </h4>
                        <div class="mt-1 text-slate-500 dark:text-slate-400">
                          <?php echo $item->subject; ?>

                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- Action Section (Edit Button and Switch) -->
                  <div class="flex gap-3 items-center">
                    <!-- Toggle Button for Active Status -->
                    <button
                      wire:click="toggleActive(<?php echo e($item->id); ?>, <?php echo e($item->is_active ? 'false' : 'true'); ?>)"
                      type="button"
                      class="group relative inline-flex h-5 w-10 flex-shrink-0 cursor-pointer items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800"
                      role="switch" aria-checked="<?php echo e($item->is_active ? 'true' : 'false'); ?>">
                      <span class="sr-only"><?php echo e(t('Enable announcement')); ?></span>
                      <span aria-hidden="true"
                        class="pointer-events-none absolute h-full w-full rounded-md"></span>
                      <span aria-hidden="true" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                          'pointer-events-none absolute mx-auto h-4 w-9 rounded-full transition-colors duration-200 ease-in-out',
                          'bg-blue-600' => $item->is_active,
                          'bg-slate-200' => !$item->is_active,
                      ]); ?>"></span>
                      <span aria-hidden="true" class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                          'pointer-events-none absolute left-0 inline-block h-5 w-5 transform rounded-full border border-slate-200 bg-white shadow ring-0 transition-transform duration-200 ease-in-out',
                          'translate-x-5' => $item->is_active,
                          'translate-x-0' => !$item->is_active,
                      ]); ?>"></span>
                    </button>

                    <!-- Edit Button -->
                    <a href="<?php echo e(route('admin.emails.save', $item->id)); ?>"
                      class="text-slate-500 hover:text-blue-500 dark:text-slate-400 dark:hover:text-blue-400">
                    </a>
                  </div>
                </div>
              </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
          </ul>
        </div>
       <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
  </div>
</div>
<?php /**PATH C:\laragon\www\whats\resources\views/livewire/admin/miscellaneous/email-template-list.blade.php ENDPATH**/ ?>