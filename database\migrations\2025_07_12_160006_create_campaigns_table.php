<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('rel_type');
            $table->string('template_id')->nullable();
            $table->timestamp('scheduled_send_time')->nullable();
            $table->boolean('send_now')->default(false);
            $table->json('header_params')->nullable();
            $table->json('body_params')->nullable();
            $table->json('footer_params')->nullable();
            $table->boolean('pause_campaign')->default(false);
            $table->boolean('select_all')->default(false);
            $table->boolean('is_sent')->default(false);
            $table->integer('sending_count')->nullable();
            $table->string('filename')->nullable();
            $table->json('rel_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaigns');
    }
};
