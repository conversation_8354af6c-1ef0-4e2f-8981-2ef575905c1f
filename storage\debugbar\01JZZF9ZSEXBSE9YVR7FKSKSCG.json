{"__meta": {"id": "01JZZF9ZSEXBSE9YVR7FKSKSCG", "datetime": "2025-07-13 02:05:58", "utime": **********.446932, "method": "POST", "uri": "/install/setup", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752329156.852907, "end": **********.446945, "duration": 1.5940380096435547, "duration_str": "1.59s", "measures": [{"label": "Booting", "start": 1752329156.852907, "relative_start": 0, "end": **********.424151, "relative_end": **********.424151, "duration": 0.****************, "duration_str": "571ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.42416, "relative_start": 0.****************, "end": **********.446947, "relative_end": 2.1457672119140625e-06, "duration": 1.***************, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.438798, "relative_start": 0.****************, "end": **********.44104, "relative_end": **********.44104, "duration": 0.0022420883178710938, "duration_str": "2.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.444379, "relative_start": 1.****************, "end": **********.444811, "relative_end": **********.444811, "duration": 0.00043201446533203125, "duration_str": "432μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Wallis", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 8, "nb_statements": 6, "nb_visible_statements": 8, "nb_excluded_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02937, "accumulated_duration_str": "29.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9gnFUveucibV5icVeYkMzp3ptWmipfWPCsLXoPJF' limit 1", "type": "query", "params": [], "bindings": ["9gnFUveucibV5icVeYkMzp3ptWmipfWPCsLXoPJF"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.444404, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 2.656}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 259}, {"index": 9, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 89}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.881131, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InstallController.php:259", "source": {"index": 8, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=259", "ajax": false, "filename": "InstallController.php", "line": "259"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 2.656, "width_percent": 0}, {"sql": "select * from `test_table`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 105}, {"index": 16, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 104}, {"index": 17, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 100}, {"index": 19, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 202}], "start": **********.131765, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:105", "source": {"index": 13, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=105", "ajax": false, "filename": "DatabaseTest.php", "line": "105"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 2.656, "width_percent": 1.192}, {"sql": "update `test_table` set `test_column` = 'Corbital Updated'", "type": "query", "params": [], "bindings": ["Corbital Updated"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 120}, {"index": 14, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 119}, {"index": 15, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 16, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 115}, {"index": 17, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 203}], "start": **********.1641479, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:120", "source": {"index": 11, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 120}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=120", "ajax": false, "filename": "DatabaseTest.php", "line": "120"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 3.847, "width_percent": 4.937}, {"sql": "delete from `test_table`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 135}, {"index": 14, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 134}, {"index": 15, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 52}, {"index": 16, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 130}, {"index": 17, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 204}], "start": **********.194597, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "DatabaseTest.php:135", "source": {"index": 11, "namespace": null, "name": "platform/packages/corbital/installer/src/Classes/DatabaseTest.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Classes\\DatabaseTest.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FClasses%2FDatabaseTest.php&line=135", "ajax": false, "filename": "DatabaseTest.php", "line": "135"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 8.784, "width_percent": 5.55}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 116}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.366706, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "InstallController.php:116", "source": {"index": 9, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 116}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=116", "ajax": false, "filename": "InstallController.php", "line": "116"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 14.334, "width_percent": 0}, {"sql": "SET FOREIGN_KEY_CHECKS=0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 117}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.3668609, "duration": 0.015359999999999999, "duration_str": "15.36ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:117", "source": {"index": 11, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=117", "ajax": false, "filename": "InstallController.php", "line": "117"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 14.334, "width_percent": 52.298}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'whatsmark_non_saas' and table_name = 'sessions' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.428682, "duration": 0.009800000000000001, "duration_str": "9.8ms", "memory": 0, "memory_str": null, "filename": "InstallController.php:119", "source": {"index": 13, "namespace": null, "name": "platform/packages/corbital/installer/src/Http/Controllers/InstallController.php", "file": "C:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Http\\Controllers\\InstallController.php", "line": 119}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=119", "ajax": false, "filename": "InstallController.php", "line": "119"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 66.633, "width_percent": 33.367}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/install/setup", "action_name": "install.setup.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore", "uri": "POST install/setup", "controller": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=85\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "install", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=85\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/corbital/installer/src/Http/Controllers/InstallController.php:85-160</a>", "middleware": "web, Corbital\\Installer\\Http\\Middleware\\CanInstall", "duration": "1.62s", "peak_memory": "42MB", "response": "Redirect to http://127.0.0.1:8000/install/user", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1050158713 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1050158713\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-344323344 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEnadNAZGonjfMy1yxQ15NHa3pBdJ4Vc7MHJA2CB</span>\"\n  \"<span class=sf-dump-key>app_url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>app_name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Laravel</span>\"\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"3 characters\">105</span>\"\n  \"<span class=sf-dump-key>database_hostname</span>\" => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  \"<span class=sf-dump-key>database_port</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3306</span>\"\n  \"<span class=sf-dump-key>database_name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">whatsmark_non_saas</span>\"\n  \"<span class=sf-dump-key>database_username</span>\" => \"<span class=sf-dump-str title=\"4 characters\">root</span>\"\n  \"<span class=sf-dump-key>database_password</span>\" => \"<span class=sf-dump-str title=\"4 characters\">root</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-344323344\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-268310605 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">240</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://127.0.0.1:8000/install/setup</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFIcFJMZDk4amFWeU9SMDFLSU9Dd3c9PSIsInZhbHVlIjoiR3Q4T3UxSXpFc25jZ2lsbUJycDVzSmpjY1dTd2JCTkJ6Y3A1MHk0OU1JZnlCOVczU2t2V05MZG51TEZIUStGenlNRXNzR21HakhTM0FGNjJNTmVNMldZTkJ4NGp4YWZyUGVyR1gxbHZoSjNoRytiSVlkYmRUOUIwWGV6aVBvUk0iLCJtYWMiOiJhMmVmYjlhMjQzYjgxNGIyZjFjZDNhZGY2Zjg4YzdiZGMzMTk0MzBmNGZiMzE3MjkzNTEwYTBjZjc1ODZmYjkzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6InRtblZ6Zk5oMEg5VUdBRXloUTd1bkE9PSIsInZhbHVlIjoicU1tUFNuc2d6bTQ5MlRIbGZmdlBRZGxta0hDUGk0Zkg5ZU1ZdHJXaU5aRU0waSs3YWorbmxZQTJNYkorWEFHUC9Cd0cvNncrU2NRZEVicUZzNXRzeUhLcHMxSU12NXduTDByWG9uWVpqaHBRcm5xNlRJQ0hXK2lEdXBycE5hSzEiLCJtYWMiOiIyOGE4YjZiODM4ODFlMjM2ZDY2OGU3YmI1Yzc5MjQ1ZWQyYTdmYmU2NzU1YjZjMjQ0MjM0ODg3YWIwM2RjMTVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268310605\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-350633996 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEnadNAZGonjfMy1yxQ15NHa3pBdJ4Vc7MHJA2CB</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9gnFUveucibV5icVeYkMzp3ptWmipfWPCsLXoPJF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-350633996\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-745710598 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:05:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://127.0.0.1:8000/install/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745710598\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1014575285 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEnadNAZGonjfMy1yxQ15NHa3pBdJ4Vc7MHJA2CB</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/install</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"3 characters\">105</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014575285\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/install/setup", "action_name": "install.setup.store", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@setupStore"}, "badge": "302 Found"}}