<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('interaction_id');
            $table->string('sender_id');
            $table->string('url')->nullable();
            $table->text('message');
            $table->string('status')->nullable();
            $table->timestamp('time_sent');
            $table->string('message_id')->nullable();
            $table->string('staff_id')->nullable();
            $table->string('type')->nullable();
            $table->boolean('is_read')->default(false);
            $table->string('ref_message_id')->nullable();
            $table->text('status_message')->nullable();
            $table->timestamps();

            // Add foreign key constraint to chat table
            $table->foreign('interaction_id')->references('id')->on('chat')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_messages');
    }
};
