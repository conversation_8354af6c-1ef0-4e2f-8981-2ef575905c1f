<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('receiver_id');
            $table->text('last_message')->nullable();
            $table->timestamp('last_msg_time')->nullable();
            $table->string('wa_no')->nullable();
            $table->string('wa_no_id')->nullable();
            $table->timestamp('time_sent');
            $table->string('type')->nullable();
            $table->string('type_id')->nullable();
            $table->string('agent')->nullable();
            $table->boolean('is_ai_chat')->default(false);
            $table->text('ai_message_json')->nullable();
            $table->boolean('is_bots_stoped')->default(false);
            $table->timestamp('bot_stoped_time')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat');
    }
};
