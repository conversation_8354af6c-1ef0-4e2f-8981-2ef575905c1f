{"__meta": {"id": "01JZZHA94CBMD2HXGV9KSZK663", "datetime": "2025-07-12 04:41:05", "utime": **********.164649, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331264.386863, "end": **********.164659, "duration": 0.7777960300445557, "duration_str": "778ms", "measures": [{"label": "Booting", "start": 1752331264.386863, "relative_start": 0, "end": **********.048701, "relative_end": **********.048701, "duration": 0.****************, "duration_str": "662ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.048709, "relative_start": 0.****************, "end": **********.16466, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.066501, "relative_start": 0.****************, "end": **********.069143, "relative_end": **********.069143, "duration": 0.0026421546936035156, "duration_str": "2.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.table-base", "start": **********.132749, "relative_start": 0.****************, "end": **********.132749, "relative_end": **********.132749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header", "start": **********.133362, "relative_start": 0.****************, "end": **********.133362, "relative_end": **********.133362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "start": **********.134045, "relative_start": 0.7471818923950195, "end": **********.134045, "relative_end": **********.134045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye-off", "start": **********.135681, "relative_start": 0.7488179206848145, "end": **********.135681, "relative_end": **********.135681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.136398, "relative_start": 0.749535083770752, "end": **********.136398, "relative_end": **********.136398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.136797, "relative_start": 0.7499339580535889, "end": **********.136797, "relative_end": **********.136797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.137088, "relative_start": 0.7502250671386719, "end": **********.137088, "relative_end": **********.137088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "start": **********.137447, "relative_start": 0.7505841255187988, "end": **********.137447, "relative_end": **********.137447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.search", "start": **********.137955, "relative_start": 0.7510919570922852, "end": **********.137955, "relative_end": **********.137955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.search", "start": **********.138509, "relative_start": 0.7516460418701172, "end": **********.138509, "relative_end": **********.138509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "start": **********.139978, "relative_start": 0.7531149387359619, "end": **********.139978, "relative_end": **********.139978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "start": **********.141452, "relative_start": 0.7545890808105469, "end": **********.141452, "relative_end": **********.141452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table", "start": **********.142354, "relative_start": 0.7554910182952881, "end": **********.142354, "relative_end": **********.142354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.143276, "relative_start": 0.7564129829406738, "end": **********.143276, "relative_end": **********.143276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.144028, "relative_start": 0.7571649551391602, "end": **********.144028, "relative_end": **********.144028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.14585, "relative_start": 0.7589869499206543, "end": **********.14585, "relative_end": **********.14585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.146317, "relative_start": 0.7594540119171143, "end": **********.146317, "relative_end": **********.146317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.146698, "relative_start": 0.7598350048065186, "end": **********.146698, "relative_end": **********.146698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.147197, "relative_start": 0.7603340148925781, "end": **********.147197, "relative_end": **********.147197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.147558, "relative_start": 0.7606949806213379, "end": **********.147558, "relative_end": **********.147558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.147835, "relative_start": 0.7609720230102539, "end": **********.147835, "relative_end": **********.147835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f80280cbef3647ab5b62bdedf0ef25a1", "start": **********.148579, "relative_start": 0.7617158889770508, "end": **********.148579, "relative_end": **********.148579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-down", "start": **********.15202, "relative_start": 0.7651569843292236, "end": **********.15202, "relative_end": **********.15202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.152393, "relative_start": 0.7655301094055176, "end": **********.152393, "relative_end": **********.152393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.152951, "relative_start": 0.7660880088806152, "end": **********.152951, "relative_end": **********.152951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.custom-loading", "start": **********.153529, "relative_start": 0.7666659355163574, "end": **********.153529, "relative_end": **********.153529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.th-empty", "start": **********.154162, "relative_start": 0.7672989368438721, "end": **********.154162, "relative_end": **********.154162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.no-data-label", "start": **********.154763, "relative_start": 0.7678999900817871, "end": **********.154763, "relative_end": **********.154763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table-base", "start": **********.155134, "relative_start": 0.7682709693908691, "end": **********.155134, "relative_end": **********.155134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.footer", "start": **********.155841, "relative_start": 0.7689781188964844, "end": **********.155841, "relative_end": **********.155841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.down", "start": **********.156851, "relative_start": 0.7699880599975586, "end": **********.156851, "relative_end": **********.156851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.pagination", "start": **********.158606, "relative_start": 0.7717430591583252, "end": **********.158606, "relative_end": **********.158606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.161925, "relative_start": 0.775062084197998, "end": **********.162828, "relative_end": **********.162828, "duration": 0.0009028911590576172, "duration_str": "903μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 37320824, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 32, "nb_templates": 32, "templates": [{"name": "livewire-powergrid::components.frameworks.tailwind.table-base", "param_count": null, "params": [], "start": **********.132709, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/table-base.blade.phplivewire-powergrid::components.frameworks.tailwind.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.header", "param_count": null, "params": [], "start": **********.133328, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header.blade.phplivewire-powergrid::components.frameworks.tailwind.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "param_count": null, "params": [], "start": **********.134011, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/toggle-columns.blade.phplivewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Ftoggle-columns.blade.php&line=1", "ajax": false, "filename": "toggle-columns.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.eye-off", "param_count": null, "params": [], "start": **********.135626, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye-off.blade.phplivewire-powergrid::components.icons.eye-off", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye-off.blade.php&line=1", "ajax": false, "filename": "eye-off.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.eye", "param_count": null, "params": [], "start": **********.136363, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye.blade.phplivewire-powergrid::components.icons.eye", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye.blade.php&line=1", "ajax": false, "filename": "eye.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.eye", "param_count": null, "params": [], "start": **********.136763, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye.blade.phplivewire-powergrid::components.icons.eye", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye.blade.php&line=1", "ajax": false, "filename": "eye.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.eye", "param_count": null, "params": [], "start": **********.137055, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye.blade.phplivewire-powergrid::components.icons.eye", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye.blade.php&line=1", "ajax": false, "filename": "eye.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "param_count": null, "params": [], "start": **********.137413, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsoft-deletes.blade.php&line=1", "ajax": false, "filename": "soft-deletes.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.header.search", "param_count": null, "params": [], "start": **********.137921, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/search.blade.phplivewire-powergrid::components.frameworks.tailwind.header.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.search", "param_count": null, "params": [], "start": **********.138476, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/search.blade.phplivewire-powergrid::components.icons.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "param_count": null, "params": [], "start": **********.139943, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/enabled-filters.blade.phplivewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fenabled-filters.blade.php&line=1", "ajax": false, "filename": "enabled-filters.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "param_count": null, "params": [], "start": **********.141214, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/message-soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fmessage-soft-deletes.blade.php&line=1", "ajax": false, "filename": "message-soft-deletes.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.table", "param_count": null, "params": [], "start": **********.142311, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table.blade.phplivewire-powergrid::components.table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.table.tr", "param_count": null, "params": [], "start": **********.143235, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/tr.blade.phplivewire-powergrid::components.table.tr", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Ftr.blade.php&line=1", "ajax": false, "filename": "tr.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.143987, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}}, {"name": "__components::6da562a044c365ee08fb55b6325b90a1", "param_count": null, "params": [], "start": **********.145811, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6da562a044c365ee08fb55b6325b90a1.blade.php__components::6da562a044c365ee08fb55b6325b90a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6da562a044c365ee08fb55b6325b90a1.blade.php&line=1", "ajax": false, "filename": "6da562a044c365ee08fb55b6325b90a1.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.chevron-up-down", "param_count": null, "params": [], "start": **********.146275, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-up-down.blade.phplivewire-powergrid::components.icons.chevron-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-up-down.blade.php&line=1", "ajax": false, "filename": "chevron-up-down.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.146658, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}}, {"name": "__components::6da562a044c365ee08fb55b6325b90a1", "param_count": null, "params": [], "start": **********.14716, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6da562a044c365ee08fb55b6325b90a1.blade.php__components::6da562a044c365ee08fb55b6325b90a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6da562a044c365ee08fb55b6325b90a1.blade.php&line=1", "ajax": false, "filename": "6da562a044c365ee08fb55b6325b90a1.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.chevron-up-down", "param_count": null, "params": [], "start": **********.147521, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-up-down.blade.phplivewire-powergrid::components.icons.chevron-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-up-down.blade.php&line=1", "ajax": false, "filename": "chevron-up-down.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.147798, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}}, {"name": "__components::f80280cbef3647ab5b62bdedf0ef25a1", "param_count": null, "params": [], "start": **********.148545, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f80280cbef3647ab5b62bdedf0ef25a1.blade.php__components::f80280cbef3647ab5b62bdedf0ef25a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff80280cbef3647ab5b62bdedf0ef25a1.blade.php&line=1", "ajax": false, "filename": "f80280cbef3647ab5b62bdedf0ef25a1.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.151981, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-down.blade.phplivewire-powergrid::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.152359, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.table.tr", "param_count": null, "params": [], "start": **********.152865, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/tr.blade.phplivewire-powergrid::components.table.tr", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Ftr.blade.php&line=1", "ajax": false, "filename": "tr.blade.php", "line": "?"}}, {"name": "components.custom-loading", "param_count": null, "params": [], "start": **********.153497, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/custom-loading.blade.phpcomponents.custom-loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcustom-loading.blade.php&line=1", "ajax": false, "filename": "custom-loading.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.table.th-empty", "param_count": null, "params": [], "start": **********.154126, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/th-empty.blade.phplivewire-powergrid::components.table.th-empty", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fth-empty.blade.php&line=1", "ajax": false, "filename": "th-empty.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.table.no-data-label", "param_count": null, "params": [], "start": **********.154729, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/no-data-label.blade.phplivewire-powergrid::components.table.no-data-label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fno-data-label.blade.php&line=1", "ajax": false, "filename": "no-data-label.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.table-base", "param_count": null, "params": [], "start": **********.155083, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table-base.blade.phplivewire-powergrid::components.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.footer", "param_count": null, "params": [], "start": **********.155807, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/footer.blade.phplivewire-powergrid::components.frameworks.tailwind.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.icons.down", "param_count": null, "params": [], "start": **********.156763, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/down.blade.phplivewire-powergrid::components.icons.down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fdown.blade.php&line=1", "ajax": false, "filename": "down.blade.php", "line": "?"}}, {"name": "livewire-powergrid::components.frameworks.tailwind.pagination", "param_count": null, "params": [], "start": **********.158496, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/pagination.blade.phplivewire-powergrid::components.frameworks.tailwind.pagination", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00415, "accumulated_duration_str": "4.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.0717528, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 14.699}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.088046, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 14.699, "width_percent": 15.663}, {"sql": "select count(*) as aggregate from `roles`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/ModelProcessor.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\ModelProcessor.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/ProcessDataSource.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\ProcessDataSource.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 174}, {"index": 20, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 140}], "start": **********.122743, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "DataSourceBase.php:85", "source": {"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FDataSource%2FProcessors%2FDataSourceBase.php&line=85", "ajax": false, "filename": "DataSourceBase.php", "line": "85"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 30.361, "width_percent": 69.639}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"admin.table.role-table #h21H2g9LAwQwxs7mI8f2": "array:4 [\n  \"data\" => array:44 [\n    \"sortField\" => \"created_at\"\n    \"sortDirection\" => \"DESC\"\n    \"tableName\" => \"role-table-0ituxt-table\"\n    \"deferLoading\" => true\n    \"loadingComponent\" => \"components.custom-loading\"\n    \"theme\" => array:17 [\n      \"name\" => \"tailwind\"\n      \"root\" => \"livewire-powergrid::components.frameworks.tailwind\"\n      \"table\" => array:3 [\n        \"layout\" => array:5 [\n          \"base\" => \"p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8\"\n          \"div\" => \"rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n          \"table\" => \"min-w-full dark:!bg-primary-800\"\n          \"container\" => \"-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8\"\n          \"actions\" => \"flex gap-2\"\n        ]\n        \"header\" => array:4 [\n          \"thead\" => \"shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900\"\n          \"tr\" => \"\"\n          \"th\" => \"font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300\"\n          \"thAction\" => \"!font-bold\"\n        ]\n        \"body\" => array:10 [\n          \"tbody\" => \"text-pg-primary-800\"\n          \"tbodyEmpty\" => \"\"\n          \"tr\" => \"border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700\"\n          \"td\" => \"px-3 py-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdEmpty\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdSummarize\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2\"\n          \"trSummarize\" => \"\"\n          \"tdFilters\" => \"\"\n          \"trFilters\" => \"\"\n          \"tdActionsContainer\" => \"flex gap-2\"\n        ]\n      ]\n      \"footer\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n        \"footer\" => \"border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n        \"footer_with_pagination\" => \"md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900\"\n      ]\n      \"cols\" => array:1 [\n        \"div\" => \"select-none flex items-center gap-1\"\n      ]\n      \"editable\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.editable\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"layout\" => array:4 [\n        \"table\" => \"livewire-powergrid::components.frameworks.tailwind.table-base\"\n        \"header\" => \"livewire-powergrid::components.frameworks.tailwind.header\"\n        \"pagination\" => \"livewire-powergrid::components.frameworks.tailwind.pagination\"\n        \"footer\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n      ]\n      \"toggleable\" => array:1 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.toggleable\"\n      ]\n      \"checkbox\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900\"\n      ]\n      \"radio\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-radio rounded-full transition ease-in-out duration-100\"\n      ]\n      \"filterBoolean\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.boolean\"\n        \"base\" => \"min-w-[5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterDatePicker\" => array:3 [\n        \"base\" => \"\"\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.date-picker\"\n        \"input\" => \"flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n      ]\n      \"filterMultiSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.multi-select\"\n        \"base\" => \"inline-block relative w-full\"\n        \"select\" => \"mt-1\"\n      ]\n      \"filterNumber\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.number\"\n        \"input\" => \"w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6\"\n      ]\n      \"filterSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.select\"\n        \"base\" => \"\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterInputText\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.input-text\"\n        \"base\" => \"min-w-[9.5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"searchBox\" => array:3 [\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8\"\n        \"iconClose\" => \"text-pg-primary-400 dark:text-pg-primary-200\"\n        \"iconSearch\" => \"text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200\"\n      ]\n    ]\n    \"primaryKey\" => \"id\"\n    \"primaryKeyAlias\" => null\n    \"ignoreTablePrefix\" => true\n    \"setUp\" => array:2 [\n      \"header\" => array:8 [\n        \"name\" => \"header\"\n        \"searchInput\" => true\n        \"toggleColumns\" => true\n        \"softDeletes\" => false\n        \"showMessageSoftDeletes\" => false\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"wireLoading\" => false\n      ]\n      \"footer\" => array:8 [\n        \"name\" => \"footer\"\n        \"perPage\" => 10\n        \"perPageValues\" => array:5 [\n          0 => 10\n          1 => 25\n          2 => 50\n          3 => 100\n          4 => 0\n        ]\n        \"recordCount\" => \"full\"\n        \"pagination\" => null\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"pageName\" => \"page\"\n      ]\n    ]\n    \"showErrorBag\" => false\n    \"rowIndex\" => true\n    \"readyToLoad\" => true\n    \"columns\" => array:4 [\n      0 => array:25 [\n        \"title\" => \"ID\"\n        \"field\" => \"id\"\n        \"dataField\" => \"id\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      1 => array:25 [\n        \"title\" => \"Name\"\n        \"field\" => \"name\"\n        \"dataField\" => \"name\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      2 => array:25 [\n        \"title\" => \"Created At\"\n        \"field\" => \"created_at_formatted\"\n        \"dataField\" => \"created_at\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      3 => array:25 [\n        \"title\" => \"Action\"\n        \"field\" => \"\"\n        \"dataField\" => \"\"\n        \"placeholder\" => \"\"\n        \"searchable\" => false\n        \"enableSort\" => false\n        \"hidden\" => false\n        \"forceHidden\" => true\n        \"visibleInExport\" => false\n        \"sortable\" => false\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => true\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n    ]\n    \"headers\" => []\n    \"search\" => \"\"\n    \"currentTable\" => \"roles\"\n    \"total\" => 0\n    \"totalCurrentPage\" => 0\n    \"supportModel\" => true\n    \"paginateRaw\" => false\n    \"measurePerformance\" => false\n    \"checkbox\" => false\n    \"checkboxAll\" => false\n    \"checkboxValues\" => []\n    \"checkboxAttribute\" => \"id\"\n    \"filters\" => []\n    \"filtered\" => []\n    \"enabledFilters\" => []\n    \"select\" => []\n    \"showFilters\" => false\n    \"withoutResourcesActions\" => false\n    \"additionalCacheKey\" => \"\"\n    \"persist\" => []\n    \"persistPrefix\" => \"\"\n    \"radio\" => false\n    \"radioAttribute\" => \"id\"\n    \"selectedRow\" => \"\"\n    \"softDeletes\" => \"\"\n    \"multiSort\" => false\n    \"sortArray\" => []\n    \"headerTotalColumn\" => false\n    \"footerTotalColumn\" => false\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"admin.table.role-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Table\\RoleTable\"\n  \"id\" => \"h21H2g9LAwQwxs7mI8f2\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Table\\RoleTable@fetchDatasource<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/power-components/livewire-powergrid/src/PowerGridComponent.php:79-82</a>", "middleware": "web", "duration": "778ms", "peak_memory": "42MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-530666537 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-530666537\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1495113951 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"12214 characters\">{&quot;data&quot;:{&quot;sortField&quot;:&quot;created_at&quot;,&quot;sortDirection&quot;:&quot;DESC&quot;,&quot;tableName&quot;:&quot;role-table-0ituxt-table&quot;,&quot;deferLoading&quot;:true,&quot;loadingComponent&quot;:&quot;components.custom-loading&quot;,&quot;theme&quot;:[{&quot;name&quot;:&quot;tailwind&quot;,&quot;root&quot;:&quot;livewire-powergrid::components.frameworks.tailwind&quot;,&quot;table&quot;:[{&quot;layout&quot;:[{&quot;base&quot;:&quot;p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8&quot;,&quot;div&quot;:&quot;rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;table&quot;:&quot;min-w-full dark:!bg-primary-800&quot;,&quot;container&quot;:&quot;-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8&quot;,&quot;actions&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;header&quot;:[{&quot;thead&quot;:&quot;shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900&quot;,&quot;tr&quot;:&quot;&quot;,&quot;th&quot;:&quot;font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300&quot;,&quot;thAction&quot;:&quot;!font-bold&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;body&quot;:[{&quot;tbody&quot;:&quot;text-pg-primary-800&quot;,&quot;tbodyEmpty&quot;:&quot;&quot;,&quot;tr&quot;:&quot;border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700&quot;,&quot;td&quot;:&quot;px-3 py-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdEmpty&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdSummarize&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2&quot;,&quot;trSummarize&quot;:&quot;&quot;,&quot;tdFilters&quot;:&quot;&quot;,&quot;trFilters&quot;:&quot;&quot;,&quot;tdActionsContainer&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;footer&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;,&quot;footer&quot;:&quot;border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;footer_with_pagination&quot;:&quot;md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;cols&quot;:[{&quot;div&quot;:&quot;select-none flex items-center gap-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.editable&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;layout&quot;:[{&quot;table&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.table-base&quot;,&quot;header&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.header&quot;,&quot;pagination&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.pagination&quot;,&quot;footer&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;toggleable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.toggleable&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;checkbox&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;radio&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-radio rounded-full transition ease-in-out duration-100&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterBoolean&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.boolean&quot;,&quot;base&quot;:&quot;min-w-[5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterDatePicker&quot;:[{&quot;base&quot;:&quot;&quot;,&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.date-picker&quot;,&quot;input&quot;:&quot;flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterMultiSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.multi-select&quot;,&quot;base&quot;:&quot;inline-block relative w-full&quot;,&quot;select&quot;:&quot;mt-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterNumber&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.number&quot;,&quot;input&quot;:&quot;w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.select&quot;,&quot;base&quot;:&quot;&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterInputText&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.input-text&quot;,&quot;base&quot;:&quot;min-w-[9.5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;searchBox&quot;:[{&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8&quot;,&quot;iconClose&quot;:&quot;text-pg-primary-400 dark:text-pg-primary-200&quot;,&quot;iconSearch&quot;:&quot;text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;primaryKey&quot;:&quot;id&quot;,&quot;primaryKeyAlias&quot;:null,&quot;ignoreTablePrefix&quot;:true,&quot;setUp&quot;:[{&quot;header&quot;:[{&quot;name&quot;:&quot;header&quot;,&quot;searchInput&quot;:true,&quot;toggleColumns&quot;:true,&quot;softDeletes&quot;:false,&quot;showMessageSoftDeletes&quot;:false,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;wireLoading&quot;:false},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Header&quot;,&quot;s&quot;:&quot;wrbl&quot;}],&quot;footer&quot;:[{&quot;name&quot;:&quot;footer&quot;,&quot;perPage&quot;:10,&quot;perPageValues&quot;:[[10,25,50,100,0],{&quot;s&quot;:&quot;arr&quot;}],&quot;recordCount&quot;:&quot;full&quot;,&quot;pagination&quot;:null,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;pageName&quot;:&quot;page&quot;},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Footer&quot;,&quot;s&quot;:&quot;wrbl&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;showErrorBag&quot;:false,&quot;rowIndex&quot;:true,&quot;readyToLoad&quot;:false,&quot;columns&quot;:[[[{&quot;title&quot;:&quot;ID&quot;,&quot;field&quot;:&quot;id&quot;,&quot;dataField&quot;:&quot;id&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Name&quot;,&quot;field&quot;:&quot;name&quot;,&quot;dataField&quot;:&quot;name&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Created At&quot;,&quot;field&quot;:&quot;created_at_formatted&quot;,&quot;dataField&quot;:&quot;created_at&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Action&quot;,&quot;field&quot;:&quot;&quot;,&quot;dataField&quot;:&quot;&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:false,&quot;enableSort&quot;:false,&quot;hidden&quot;:false,&quot;forceHidden&quot;:true,&quot;visibleInExport&quot;:false,&quot;sortable&quot;:false,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:true,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;headers&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;search&quot;:&quot;&quot;,&quot;currentTable&quot;:&quot;&quot;,&quot;total&quot;:0,&quot;totalCurrentPage&quot;:0,&quot;supportModel&quot;:true,&quot;paginateRaw&quot;:false,&quot;measurePerformance&quot;:false,&quot;checkbox&quot;:false,&quot;checkboxAll&quot;:false,&quot;checkboxValues&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;checkboxAttribute&quot;:&quot;id&quot;,&quot;filters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filtered&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;enabledFilters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;select&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;showFilters&quot;:false,&quot;withoutResourcesActions&quot;:false,&quot;additionalCacheKey&quot;:&quot;&quot;,&quot;persist&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;persistPrefix&quot;:&quot;&quot;,&quot;radio&quot;:false,&quot;radioAttribute&quot;:&quot;id&quot;,&quot;selectedRow&quot;:&quot;&quot;,&quot;softDeletes&quot;:&quot;&quot;,&quot;multiSort&quot;:false,&quot;sortArray&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerTotalColumn&quot;:false,&quot;footerTotalColumn&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;h21H2g9LAwQwxs7mI8f2&quot;,&quot;name&quot;:&quot;admin.table.role-table&quot;,&quot;path&quot;:&quot;admin\\/roles&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;8ff0391968be7107b69736c5d0b42aae843a97fb5113246baf8d4ac542ffc580&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">fetchDatasource</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495113951\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-882058133 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">13415</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InpHWFVYUGhla2dKM3ZLdFJpK2xicnc9PSIsInZhbHVlIjoiQ0h1RVJzNzVNV1ZHUVIwSGZNamdpa1Q0RmoxMDFzNFBhcWRBTEJ0ZEJLYjV5WFVjeW9KRjFwQXgzWm1KRXVPYVAwbUN6U1pIWFpWdThaSnQ4WjlWSHZqcHFnbnAwaTdXekZaZ3BEeFV4K0xEK3FLM3NtMXc2VlBET21pMlJoWlYiLCJtYWMiOiJjZmI5MjFmY2U1MDkwZDY3MWUxNGFjM2Q5MDhlZmNmMmI2YWNkYTZmYjg4Mjg2NmVjNDNmMDIxZTc5NDUyYWUxIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Ilk0ZXltcUVQYzRuNjZvc0FndGt1d3c9PSIsInZhbHVlIjoiZ1JHQWwvYk05SjMraTJrZ1JqVU1SRFM5cHdONS9zMllHVHVsWkF3cnVwT2NObHJ4T1BuUStlTU56RGllZ1FscjVubzRzdkZ1dUxheEZ1S3FmckVIS2VYV1BYZTg0MUJpL2ZvTkQ2TUVROTNLdkRrYlBHZGFscC8xZld2Q1krQnoiLCJtYWMiOiIxYTE0NGUwNjkwNWE0YzQwY2MzOGM3NDg5ZThhYjk1YzgyNWVjZjllMDlkMzcwMzBjZDBiNjk5NTM4ZWRkZDYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-882058133\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1236161552 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236161552\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-520302520 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:41:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-520302520\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1418369891 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/admin/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1418369891\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}