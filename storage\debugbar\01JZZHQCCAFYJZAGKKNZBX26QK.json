{"__meta": {"id": "01JZZHQCCAFYJZAGKKNZBX26QK", "datetime": "2025-07-12 04:48:14", "utime": **********.475133, "method": "GET", "uri": "/admin/system-update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331691.513717, "end": **********.475145, "duration": 2.961428165435791, "duration_str": "2.96s", "measures": [{"label": "Booting", "start": 1752331691.513717, "relative_start": 0, "end": **********.070833, "relative_end": **********.070833, "duration": 0.****************, "duration_str": "557ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.070849, "relative_start": 0.****************, "end": **********.475146, "relative_end": 9.5367431640625e-07, "duration": 2.***************, "duration_str": "2.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.094238, "relative_start": 0.****************, "end": **********.098373, "relative_end": **********.098373, "duration": 0.0041348934173583984, "duration_str": "4.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.settings.system.system-update-settings", "start": **********.202097, "relative_start": 2.***************, "end": **********.202097, "relative_end": **********.202097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.settings-heading", "start": **********.210706, "relative_start": 2.***************, "end": **********.210706, "relative_end": **********.210706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.admin-system-settings-navigation", "start": **********.211984, "relative_start": 2.6982669830322266, "end": **********.211984, "relative_end": **********.211984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3884f14aaba57a4f386448d1a7c68e96", "start": **********.221452, "relative_start": 2.707735061645508, "end": **********.221452, "relative_end": **********.221452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.225798, "relative_start": 2.712080955505371, "end": **********.225798, "relative_end": **********.225798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.228044, "relative_start": 2.714327096939087, "end": **********.228044, "relative_end": **********.228044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.229857, "relative_start": 2.716140031814575, "end": **********.229857, "relative_end": **********.229857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.23166, "relative_start": 2.717942953109741, "end": **********.23166, "relative_end": **********.23166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.2328, "relative_start": 2.719083070755005, "end": **********.2328, "relative_end": **********.2328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.23402, "relative_start": 2.7203030586242676, "end": **********.23402, "relative_end": **********.23402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.235036, "relative_start": 2.7213189601898193, "end": **********.235036, "relative_end": **********.235036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.235965, "relative_start": 2.722248077392578, "end": **********.235965, "relative_end": **********.235965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.236832, "relative_start": 2.7231149673461914, "end": **********.236832, "relative_end": **********.236832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.237797, "relative_start": 2.7240800857543945, "end": **********.237797, "relative_end": **********.237797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.239385, "relative_start": 2.725667953491211, "end": **********.239385, "relative_end": **********.239385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.240458, "relative_start": 2.726741075515747, "end": **********.240458, "relative_end": **********.240458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.240758, "relative_start": 2.727041006088257, "end": **********.240758, "relative_end": **********.240758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::22c5497599a0281ab25c8247b437d302", "start": **********.242168, "relative_start": 2.7284510135650635, "end": **********.242168, "relative_end": **********.242168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a432816b85ee65ed60368f9a3c94be48", "start": **********.244259, "relative_start": 2.7305421829223633, "end": **********.244259, "relative_end": **********.244259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46f5938c81ab183386dac5ba8284fe95", "start": **********.247634, "relative_start": 2.733916997909546, "end": **********.247634, "relative_end": **********.247634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ce224f6c6bbe814e8fd6f78c6703ba06", "start": **********.249511, "relative_start": 2.7357940673828125, "end": **********.249511, "relative_end": **********.249511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1165f1d1cef30b0a01dd0d4b054072c1", "start": **********.251293, "relative_start": 2.7375760078430176, "end": **********.251293, "relative_end": **********.251293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e921a1eb2ea111d1003732b3951f1bd9", "start": **********.253043, "relative_start": 2.739326000213623, "end": **********.253043, "relative_end": **********.253043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b402eeba76379a019857e2a4f3f270ea", "start": **********.255556, "relative_start": 2.7418391704559326, "end": **********.255556, "relative_end": **********.255556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b72a2d28d61b467c04005109a3e72ee0", "start": **********.257993, "relative_start": 2.7442760467529297, "end": **********.257993, "relative_end": **********.257993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::01ada89b1421c14d3d3bfdef1871a2a7", "start": **********.260533, "relative_start": 2.7468161582946777, "end": **********.260533, "relative_end": **********.260533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d6b2f7c1dc9e7fcc55a90d9e555aa73", "start": **********.262837, "relative_start": 2.749119997024536, "end": **********.262837, "relative_end": **********.262837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4439815e9fdb154c1dfeba18fb973a1b", "start": **********.264756, "relative_start": 2.7510390281677246, "end": **********.264756, "relative_end": **********.264756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::78f47a3fc9978bef41f40fdb7eb82ff0", "start": **********.266553, "relative_start": 2.752835988998413, "end": **********.266553, "relative_end": **********.266553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.settings-heading", "start": **********.268041, "relative_start": 2.754323959350586, "end": **********.268041, "relative_end": **********.268041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.settings-description", "start": **********.268879, "relative_start": 2.755162000656128, "end": **********.268879, "relative_end": **********.268879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9e3f7297abf85d8b4fa0b27d9f588b96", "start": **********.271499, "relative_start": 2.757781982421875, "end": **********.271499, "relative_end": **********.271499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d231bde2c50418020678628f9ecb5e5e", "start": **********.276194, "relative_start": 2.762477159500122, "end": **********.276194, "relative_end": **********.276194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6ec1be0947a4b87477d5e2dc1a19cf1b", "start": **********.281531, "relative_start": 2.7678141593933105, "end": **********.281531, "relative_end": **********.281531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.293823, "relative_start": 2.7801060676574707, "end": **********.293823, "relative_end": **********.293823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.303005, "relative_start": 2.78928804397583, "end": **********.303005, "relative_end": **********.303005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.app", "start": **********.303516, "relative_start": 2.7897989749908447, "end": **********.303516, "relative_end": **********.303516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.backend.sidebar-navigation", "start": **********.323284, "relative_start": 2.8095669746398926, "end": **********.323284, "relative_end": **********.323284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::886d727e9dc72997f32a0f2f234fc84f", "start": **********.328036, "relative_start": 2.814319133758545, "end": **********.328036, "relative_end": **********.328036, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6b1898751c066ebcc10934b78f87cbf0", "start": **********.329422, "relative_start": 2.8157050609588623, "end": **********.329422, "relative_end": **********.329422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8438fc7bf4d7ce759e8eba19af63a3f1", "start": **********.331241, "relative_start": 2.817523956298828, "end": **********.331241, "relative_end": **********.331241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::56fe4b6f285fe1ec6232822f4d144f28", "start": **********.332892, "relative_start": 2.8191750049591064, "end": **********.332892, "relative_end": **********.332892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6c9caa75cd6f207df991e15d7cbf1c8", "start": **********.334512, "relative_start": 2.8207950592041016, "end": **********.334512, "relative_end": **********.334512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9cfed0aaa378ec61417e808f5b171d30", "start": **********.336042, "relative_start": 2.8223249912261963, "end": **********.336042, "relative_end": **********.336042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c5096c547e1e4799b251226d536a755f", "start": **********.337638, "relative_start": 2.823920965194702, "end": **********.337638, "relative_end": **********.337638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bbeeb09c8e60a84bbc3758cdf5155cc7", "start": **********.339366, "relative_start": 2.8256490230560303, "end": **********.339366, "relative_end": **********.339366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::782be997dc25a78b850884434c00f240", "start": **********.341106, "relative_start": 2.8273890018463135, "end": **********.341106, "relative_end": **********.341106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::871b569fe4b8bc1f493e29254eddc76e", "start": **********.342617, "relative_start": 2.828900098800659, "end": **********.342617, "relative_end": **********.342617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d9dc4902cc1ebcdd71cccc9ce512531a", "start": **********.344247, "relative_start": 2.8305301666259766, "end": **********.344247, "relative_end": **********.344247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3c343ff09c2ca79e16f50878c798a182", "start": **********.348185, "relative_start": 2.834468126296997, "end": **********.348185, "relative_end": **********.348185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::136011ccc3a87208e1917491717f9a4f", "start": **********.350549, "relative_start": 2.836832046508789, "end": **********.350549, "relative_end": **********.350549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b37254cf00729b74237ee910ffb6596f", "start": **********.353299, "relative_start": 2.8395819664001465, "end": **********.353299, "relative_end": **********.353299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::93ef6be6fe1eb178e1feb898975a91ee", "start": **********.356102, "relative_start": 2.8423850536346436, "end": **********.356102, "relative_end": **********.356102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fbf47fa86220ae7145c8d582e8c0eed4", "start": **********.358391, "relative_start": 2.8446741104125977, "end": **********.358391, "relative_end": **********.358391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bb3f94f23803809e73a8da21ef4efa8e", "start": **********.359971, "relative_start": 2.8462541103363037, "end": **********.359971, "relative_end": **********.359971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83003a3f2a3ed01b01e553b0ac0ad83c", "start": **********.361541, "relative_start": 2.8478240966796875, "end": **********.361541, "relative_end": **********.361541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6de57ecc36cdded0fb3b0d8da86d69ff", "start": **********.363146, "relative_start": 2.849429130554199, "end": **********.363146, "relative_end": **********.363146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::220c546cb91e6a4599242f7bf0cef465", "start": **********.365141, "relative_start": 2.851423978805542, "end": **********.365141, "relative_end": **********.365141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9dfa94569e0724e5dabad0703eee2d6a", "start": **********.367221, "relative_start": 2.853504180908203, "end": **********.367221, "relative_end": **********.367221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7b66fb07888ac783788e2b26bcd125a8", "start": **********.369081, "relative_start": 2.8553640842437744, "end": **********.369081, "relative_end": **********.369081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "start": **********.370672, "relative_start": 2.856955051422119, "end": **********.370672, "relative_end": **********.370672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::886d727e9dc72997f32a0f2f234fc84f", "start": **********.372926, "relative_start": 2.8592090606689453, "end": **********.372926, "relative_end": **********.372926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c949e4db934c39e884a3b8df4f399cf6", "start": **********.373871, "relative_start": 2.860154151916504, "end": **********.373871, "relative_end": **********.373871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5bdb4266551740440506aff4bf77ee0d", "start": **********.375231, "relative_start": 2.861514091491699, "end": **********.375231, "relative_end": **********.375231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5f16700bfd815b296f7f512eeaea5673", "start": **********.376497, "relative_start": 2.8627800941467285, "end": **********.376497, "relative_end": **********.376497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6c9caa75cd6f207df991e15d7cbf1c8", "start": **********.377344, "relative_start": 2.8636269569396973, "end": **********.377344, "relative_end": **********.377344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::86e645ab8f001358369d5b4e60f4bf59", "start": **********.378507, "relative_start": 2.8647899627685547, "end": **********.378507, "relative_end": **********.378507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2aa3fa40c579d34617cbba7fa1f682ca", "start": **********.37977, "relative_start": 2.8660531044006348, "end": **********.37977, "relative_end": **********.37977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bbeeb09c8e60a84bbc3758cdf5155cc7", "start": **********.380645, "relative_start": 2.8669281005859375, "end": **********.380645, "relative_end": **********.380645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::782be997dc25a78b850884434c00f240", "start": **********.381424, "relative_start": 2.8677070140838623, "end": **********.381424, "relative_end": **********.381424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::871b569fe4b8bc1f493e29254eddc76e", "start": **********.382631, "relative_start": 2.8689141273498535, "end": **********.382631, "relative_end": **********.382631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d9dc4902cc1ebcdd71cccc9ce512531a", "start": **********.383576, "relative_start": 2.869858980178833, "end": **********.383576, "relative_end": **********.383576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b5ca655d4335b87ed0d138a5d744f3d6", "start": **********.387563, "relative_start": 2.8738460540771484, "end": **********.387563, "relative_end": **********.387563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::136011ccc3a87208e1917491717f9a4f", "start": **********.389397, "relative_start": 2.8756799697875977, "end": **********.389397, "relative_end": **********.389397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7891d25efdd26b215062e0b170dbcab6", "start": **********.39109, "relative_start": 2.877372980117798, "end": **********.39109, "relative_end": **********.39109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e1275e529df599477ba24a2029d9c4c", "start": **********.39274, "relative_start": 2.8790230751037598, "end": **********.39274, "relative_end": **********.39274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f754263ea240437bcce4902de59bc4b9", "start": **********.394682, "relative_start": 2.880964994430542, "end": **********.394682, "relative_end": **********.394682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b1589f415f31dd93e57e18d0643b6093", "start": **********.396023, "relative_start": 2.8823060989379883, "end": **********.396023, "relative_end": **********.396023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::99c7a16739e2f632d30b83abd5fdcad9", "start": **********.397586, "relative_start": 2.883869171142578, "end": **********.397586, "relative_end": **********.397586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::adfc97029070652f0e172ecbe3494c8b", "start": **********.399401, "relative_start": 2.885684013366699, "end": **********.399401, "relative_end": **********.399401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::8fdc46d28d72c53c63d9392f76f64223", "start": **********.401701, "relative_start": 2.887984037399292, "end": **********.401701, "relative_end": **********.401701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5409a33a8ed4a8289e392fc684d7eb1a", "start": **********.404061, "relative_start": 2.8903441429138184, "end": **********.404061, "relative_end": **********.404061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff2a50cf02e428f29948df9e0b706bc2", "start": **********.406134, "relative_start": 2.8924169540405273, "end": **********.406134, "relative_end": **********.406134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "start": **********.407118, "relative_start": 2.8934011459350586, "end": **********.407118, "relative_end": **********.407118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.backend.header-navigation", "start": **********.412852, "relative_start": 2.899135112762451, "end": **********.412852, "relative_end": **********.412852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ac8aa99487bb4cf2a4bcd65a29a3f04d", "start": **********.415806, "relative_start": 2.9020891189575195, "end": **********.415806, "relative_end": **********.415806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::df7c6ab5de2b42f6cee2f41a9504d04c", "start": **********.416952, "relative_start": 2.9032349586486816, "end": **********.416952, "relative_end": **********.416952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::368721f198a3f75f1fe85ac4948f0e84", "start": **********.417995, "relative_start": 2.904278039932251, "end": **********.417995, "relative_end": **********.417995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::914f269289845944af4ea7064df47b48", "start": **********.420961, "relative_start": 2.9072439670562744, "end": **********.420961, "relative_end": **********.420961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.422358, "relative_start": 2.9086410999298096, "end": **********.422358, "relative_end": **********.422358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::73f1fde11c4734cced4d836d3a092bec", "start": **********.42371, "relative_start": 2.9099931716918945, "end": **********.42371, "relative_end": **********.42371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary-round", "start": **********.424037, "relative_start": 2.9103200435638428, "end": **********.424037, "relative_end": **********.424037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::18061f0a7de6cc38934967fcf0ab7119", "start": **********.42501, "relative_start": 2.9112930297851562, "end": **********.42501, "relative_end": **********.42501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::826d9ae0f124683ce39e8e5582533db2", "start": **********.426504, "relative_start": 2.9127869606018066, "end": **********.426504, "relative_end": **********.426504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f43fb37d760babea44d38d2243b9db02", "start": **********.427798, "relative_start": 2.91408109664917, "end": **********.427798, "relative_end": **********.427798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6b40256b79ce902897968ff2c396eeb2", "start": **********.429098, "relative_start": 2.9153809547424316, "end": **********.429098, "relative_end": **********.429098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7dbf7b1d342ba2c17a858fb8b23129fb", "start": **********.43035, "relative_start": 2.916633129119873, "end": **********.43035, "relative_end": **********.43035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4a886341e8b9106f3f63a321813c268e", "start": **********.431605, "relative_start": 2.9178881645202637, "end": **********.431605, "relative_end": **********.431605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b45136778d9e1f97d0856c0b8bfe0ab6", "start": **********.432848, "relative_start": 2.91913104057312, "end": **********.432848, "relative_end": **********.432848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6c9caa75cd6f207df991e15d7cbf1c8", "start": **********.433669, "relative_start": 2.919952154159546, "end": **********.433669, "relative_end": **********.433669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.434286, "relative_start": 2.9205691814422607, "end": **********.434286, "relative_end": **********.434286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.language-switcher", "start": **********.43575, "relative_start": 2.9220330715179443, "end": **********.43575, "relative_end": **********.43575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7d5927e3602df13b6cb94272364e60cd", "start": **********.436788, "relative_start": 2.9230711460113525, "end": **********.436788, "relative_end": **********.436788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.primary-round", "start": **********.437092, "relative_start": 2.923375129699707, "end": **********.437092, "relative_end": **********.437092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.441332, "relative_start": 2.927615165710449, "end": **********.441332, "relative_end": **********.441332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::626e3186dfddcc134144cee5faad3af0", "start": **********.443371, "relative_start": 2.929654121398926, "end": **********.443371, "relative_end": **********.443371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bf02a25af80fbc3a9220a1cbfec1ed84", "start": **********.444453, "relative_start": 2.9307360649108887, "end": **********.444453, "relative_end": **********.444453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4a422c51ce49d9d4b3edb4019ae706ba", "start": **********.44572, "relative_start": 2.9320030212402344, "end": **********.44572, "relative_end": **********.44572, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::bd9adf6608e0d55e77c26a59fa85b0a2", "start": **********.446761, "relative_start": 2.933043956756592, "end": **********.446761, "relative_end": **********.446761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.447863, "relative_start": 2.9341461658477783, "end": **********.447863, "relative_end": **********.447863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c73f13ef25c37df445e040c2e298817c", "start": **********.448829, "relative_start": 2.9351119995117188, "end": **********.448829, "relative_end": **********.448829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.449787, "relative_start": 2.936069965362549, "end": **********.449787, "relative_end": **********.449787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::66b7debda96562d7d5ec9a50db5fb253", "start": **********.450806, "relative_start": 2.937088966369629, "end": **********.450806, "relative_end": **********.450806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.451677, "relative_start": 2.937960147857666, "end": **********.451677, "relative_end": **********.451677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.451958, "relative_start": 2.9382410049438477, "end": **********.451958, "relative_end": **********.451958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.454063, "relative_start": 2.9403460025787354, "end": **********.454063, "relative_end": **********.454063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown-link", "start": **********.455213, "relative_start": 2.9414961338043213, "end": **********.455213, "relative_end": **********.455213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dropdown", "start": **********.455588, "relative_start": 2.941871166229248, "end": **********.455588, "relative_end": **********.455588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.notification", "start": **********.457905, "relative_start": 2.944188117980957, "end": **********.457905, "relative_end": **********.457905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e619de47f59c986e9f566638044a3ab7", "start": **********.459236, "relative_start": 2.945518970489502, "end": **********.459236, "relative_end": **********.459236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f2b343610bcc0778e0f00f4bfe27f188", "start": **********.460362, "relative_start": 2.9466450214385986, "end": **********.460362, "relative_end": **********.460362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0d5cde93dab074a387e37ef65b78febc", "start": **********.461602, "relative_start": 2.947885036468506, "end": **********.461602, "relative_end": **********.461602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::10cb7a8570e3e511d44febea50de8e08", "start": **********.462693, "relative_start": 2.9489760398864746, "end": **********.462693, "relative_end": **********.462693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::46553ac5b5a848738f4879c0717608ca", "start": **********.46477, "relative_start": 2.9510531425476074, "end": **********.46477, "relative_end": **********.46477, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.473315, "relative_start": 2.9595980644226074, "end": **********.473462, "relative_end": **********.473462, "duration": 0.00014710426330566406, "duration_str": "147μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 39862576, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 124, "nb_templates": 124, "templates": [{"name": "1x livewire.admin.settings.system.system-update-settings", "param_count": null, "params": [], "start": **********.201997, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/settings/system/system-update-settings.blade.phplivewire.admin.settings.system.system-update-settings", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fsettings%2Fsystem%2Fsystem-update-settings.blade.php&line=1", "ajax": false, "filename": "system-update-settings.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.settings.system.system-update-settings"}, {"name": "2x components.settings-heading", "param_count": null, "params": [], "start": **********.210608, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/settings-heading.blade.phpcomponents.settings-heading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fsettings-heading.blade.php&line=1", "ajax": false, "filename": "settings-heading.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.settings-heading"}, {"name": "1x components.admin-system-settings-navigation", "param_count": null, "params": [], "start": **********.211896, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/admin-system-settings-navigation.blade.phpcomponents.admin-system-settings-navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fadmin-system-settings-navigation.blade.php&line=1", "ajax": false, "filename": "admin-system-settings-navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.admin-system-settings-navigation"}, {"name": "1x __components::3884f14aaba57a4f386448d1a7c68e96", "param_count": null, "params": [], "start": **********.221359, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/3884f14aaba57a4f386448d1a7c68e96.blade.php__components::3884f14aaba57a4f386448d1a7c68e96", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F3884f14aaba57a4f386448d1a7c68e96.blade.php&line=1", "ajax": false, "filename": "3884f14aaba57a4f386448d1a7c68e96.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3884f14aaba57a4f386448d1a7c68e96"}, {"name": "17x components.dropdown-link", "param_count": null, "params": [], "start": **********.225708, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown-link.blade.phpcomponents.dropdown-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown-link.blade.php&line=1", "ajax": false, "filename": "dropdown-link.blade.php", "line": "?"}, "render_count": 17, "name_original": "components.dropdown-link"}, {"name": "6x components.dropdown", "param_count": null, "params": [], "start": **********.240722, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/dropdown.blade.phpcomponents.dropdown", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fdropdown.blade.php&line=1", "ajax": false, "filename": "dropdown.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.dropdown"}, {"name": "1x __components::22c5497599a0281ab25c8247b437d302", "param_count": null, "params": [], "start": **********.242126, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/22c5497599a0281ab25c8247b437d302.blade.php__components::22c5497599a0281ab25c8247b437d302", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F22c5497599a0281ab25c8247b437d302.blade.php&line=1", "ajax": false, "filename": "22c5497599a0281ab25c8247b437d302.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::22c5497599a0281ab25c8247b437d302"}, {"name": "1x __components::a432816b85ee65ed60368f9a3c94be48", "param_count": null, "params": [], "start": **********.244216, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/a432816b85ee65ed60368f9a3c94be48.blade.php__components::a432816b85ee65ed60368f9a3c94be48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fa432816b85ee65ed60368f9a3c94be48.blade.php&line=1", "ajax": false, "filename": "a432816b85ee65ed60368f9a3c94be48.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::a432816b85ee65ed60368f9a3c94be48"}, {"name": "1x __components::46f5938c81ab183386dac5ba8284fe95", "param_count": null, "params": [], "start": **********.247595, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/46f5938c81ab183386dac5ba8284fe95.blade.php__components::46f5938c81ab183386dac5ba8284fe95", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F46f5938c81ab183386dac5ba8284fe95.blade.php&line=1", "ajax": false, "filename": "46f5938c81ab183386dac5ba8284fe95.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46f5938c81ab183386dac5ba8284fe95"}, {"name": "1x __components::ce224f6c6bbe814e8fd6f78c6703ba06", "param_count": null, "params": [], "start": **********.249474, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ce224f6c6bbe814e8fd6f78c6703ba06.blade.php__components::ce224f6c6bbe814e8fd6f78c6703ba06", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fce224f6c6bbe814e8fd6f78c6703ba06.blade.php&line=1", "ajax": false, "filename": "ce224f6c6bbe814e8fd6f78c6703ba06.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ce224f6c6bbe814e8fd6f78c6703ba06"}, {"name": "1x __components::1165f1d1cef30b0a01dd0d4b054072c1", "param_count": null, "params": [], "start": **********.251257, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/1165f1d1cef30b0a01dd0d4b054072c1.blade.php__components::1165f1d1cef30b0a01dd0d4b054072c1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F1165f1d1cef30b0a01dd0d4b054072c1.blade.php&line=1", "ajax": false, "filename": "1165f1d1cef30b0a01dd0d4b054072c1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1165f1d1cef30b0a01dd0d4b054072c1"}, {"name": "1x __components::e921a1eb2ea111d1003732b3951f1bd9", "param_count": null, "params": [], "start": **********.253008, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/e921a1eb2ea111d1003732b3951f1bd9.blade.php__components::e921a1eb2ea111d1003732b3951f1bd9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fe921a1eb2ea111d1003732b3951f1bd9.blade.php&line=1", "ajax": false, "filename": "e921a1eb2ea111d1003732b3951f1bd9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e921a1eb2ea111d1003732b3951f1bd9"}, {"name": "1x __components::b402eeba76379a019857e2a4f3f270ea", "param_count": null, "params": [], "start": **********.255518, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b402eeba76379a019857e2a4f3f270ea.blade.php__components::b402eeba76379a019857e2a4f3f270ea", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb402eeba76379a019857e2a4f3f270ea.blade.php&line=1", "ajax": false, "filename": "b402eeba76379a019857e2a4f3f270ea.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b402eeba76379a019857e2a4f3f270ea"}, {"name": "1x __components::b72a2d28d61b467c04005109a3e72ee0", "param_count": null, "params": [], "start": **********.257955, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b72a2d28d61b467c04005109a3e72ee0.blade.php__components::b72a2d28d61b467c04005109a3e72ee0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb72a2d28d61b467c04005109a3e72ee0.blade.php&line=1", "ajax": false, "filename": "b72a2d28d61b467c04005109a3e72ee0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b72a2d28d61b467c04005109a3e72ee0"}, {"name": "1x __components::01ada89b1421c14d3d3bfdef1871a2a7", "param_count": null, "params": [], "start": **********.260499, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/01ada89b1421c14d3d3bfdef1871a2a7.blade.php__components::01ada89b1421c14d3d3bfdef1871a2a7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F01ada89b1421c14d3d3bfdef1871a2a7.blade.php&line=1", "ajax": false, "filename": "01ada89b1421c14d3d3bfdef1871a2a7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::01ada89b1421c14d3d3bfdef1871a2a7"}, {"name": "1x __components::0d6b2f7c1dc9e7fcc55a90d9e555aa73", "param_count": null, "params": [], "start": **********.262803, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/0d6b2f7c1dc9e7fcc55a90d9e555aa73.blade.php__components::0d6b2f7c1dc9e7fcc55a90d9e555aa73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F0d6b2f7c1dc9e7fcc55a90d9e555aa73.blade.php&line=1", "ajax": false, "filename": "0d6b2f7c1dc9e7fcc55a90d9e555aa73.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d6b2f7c1dc9e7fcc55a90d9e555aa73"}, {"name": "1x __components::4439815e9fdb154c1dfeba18fb973a1b", "param_count": null, "params": [], "start": **********.264722, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4439815e9fdb154c1dfeba18fb973a1b.blade.php__components::4439815e9fdb154c1dfeba18fb973a1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4439815e9fdb154c1dfeba18fb973a1b.blade.php&line=1", "ajax": false, "filename": "4439815e9fdb154c1dfeba18fb973a1b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4439815e9fdb154c1dfeba18fb973a1b"}, {"name": "1x __components::78f47a3fc9978bef41f40fdb7eb82ff0", "param_count": null, "params": [], "start": **********.266516, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/78f47a3fc9978bef41f40fdb7eb82ff0.blade.php__components::78f47a3fc9978bef41f40fdb7eb82ff0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F78f47a3fc9978bef41f40fdb7eb82ff0.blade.php&line=1", "ajax": false, "filename": "78f47a3fc9978bef41f40fdb7eb82ff0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::78f47a3fc9978bef41f40fdb7eb82ff0"}, {"name": "1x components.settings-description", "param_count": null, "params": [], "start": **********.268845, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/settings-description.blade.phpcomponents.settings-description", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fsettings-description.blade.php&line=1", "ajax": false, "filename": "settings-description.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.settings-description"}, {"name": "1x __components::9e3f7297abf85d8b4fa0b27d9f588b96", "param_count": null, "params": [], "start": **********.271417, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/9e3f7297abf85d8b4fa0b27d9f588b96.blade.php__components::9e3f7297abf85d8b4fa0b27d9f588b96", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F9e3f7297abf85d8b4fa0b27d9f588b96.blade.php&line=1", "ajax": false, "filename": "9e3f7297abf85d8b4fa0b27d9f588b96.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9e3f7297abf85d8b4fa0b27d9f588b96"}, {"name": "1x __components::d231bde2c50418020678628f9ecb5e5e", "param_count": null, "params": [], "start": **********.276156, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/d231bde2c50418020678628f9ecb5e5e.blade.php__components::d231bde2c50418020678628f9ecb5e5e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fd231bde2c50418020678628f9ecb5e5e.blade.php&line=1", "ajax": false, "filename": "d231bde2c50418020678628f9ecb5e5e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d231bde2c50418020678628f9ecb5e5e"}, {"name": "1x __components::6ec1be0947a4b87477d5e2dc1a19cf1b", "param_count": null, "params": [], "start": **********.281462, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6ec1be0947a4b87477d5e2dc1a19cf1b.blade.php__components::6ec1be0947a4b87477d5e2dc1a19cf1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6ec1be0947a4b87477d5e2dc1a19cf1b.blade.php&line=1", "ajax": false, "filename": "6ec1be0947a4b87477d5e2dc1a19cf1b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6ec1be0947a4b87477d5e2dc1a19cf1b"}, {"name": "1x components.card", "param_count": null, "params": [], "start": **********.293777, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.card"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.302968, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": **********.303481, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x livewire.backend.sidebar-navigation", "param_count": null, "params": [], "start": **********.323245, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/backend/sidebar-navigation.blade.phplivewire.backend.sidebar-navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fbackend%2Fsidebar-navigation.blade.php&line=1", "ajax": false, "filename": "sidebar-navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.backend.sidebar-navigation"}, {"name": "2x __components::886d727e9dc72997f32a0f2f234fc84f", "param_count": null, "params": [], "start": **********.327979, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/886d727e9dc72997f32a0f2f234fc84f.blade.php__components::886d727e9dc72997f32a0f2f234fc84f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F886d727e9dc72997f32a0f2f234fc84f.blade.php&line=1", "ajax": false, "filename": "886d727e9dc72997f32a0f2f234fc84f.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::886d727e9dc72997f32a0f2f234fc84f"}, {"name": "1x __components::6b1898751c066ebcc10934b78f87cbf0", "param_count": null, "params": [], "start": **********.329371, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6b1898751c066ebcc10934b78f87cbf0.blade.php__components::6b1898751c066ebcc10934b78f87cbf0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6b1898751c066ebcc10934b78f87cbf0.blade.php&line=1", "ajax": false, "filename": "6b1898751c066ebcc10934b78f87cbf0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6b1898751c066ebcc10934b78f87cbf0"}, {"name": "1x __components::8438fc7bf4d7ce759e8eba19af63a3f1", "param_count": null, "params": [], "start": **********.331205, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/8438fc7bf4d7ce759e8eba19af63a3f1.blade.php__components::8438fc7bf4d7ce759e8eba19af63a3f1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F8438fc7bf4d7ce759e8eba19af63a3f1.blade.php&line=1", "ajax": false, "filename": "8438fc7bf4d7ce759e8eba19af63a3f1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8438fc7bf4d7ce759e8eba19af63a3f1"}, {"name": "1x __components::56fe4b6f285fe1ec6232822f4d144f28", "param_count": null, "params": [], "start": **********.332856, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/56fe4b6f285fe1ec6232822f4d144f28.blade.php__components::56fe4b6f285fe1ec6232822f4d144f28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F56fe4b6f285fe1ec6232822f4d144f28.blade.php&line=1", "ajax": false, "filename": "56fe4b6f285fe1ec6232822f4d144f28.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::56fe4b6f285fe1ec6232822f4d144f28"}, {"name": "3x __components::c6c9caa75cd6f207df991e15d7cbf1c8", "param_count": null, "params": [], "start": **********.334472, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c6c9caa75cd6f207df991e15d7cbf1c8.blade.php__components::c6c9caa75cd6f207df991e15d7cbf1c8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc6c9caa75cd6f207df991e15d7cbf1c8.blade.php&line=1", "ajax": false, "filename": "c6c9caa75cd6f207df991e15d7cbf1c8.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::c6c9caa75cd6f207df991e15d7cbf1c8"}, {"name": "1x __components::9cfed0aaa378ec61417e808f5b171d30", "param_count": null, "params": [], "start": **********.336007, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/9cfed0aaa378ec61417e808f5b171d30.blade.php__components::9cfed0aaa378ec61417e808f5b171d30", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F9cfed0aaa378ec61417e808f5b171d30.blade.php&line=1", "ajax": false, "filename": "9cfed0aaa378ec61417e808f5b171d30.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9cfed0aaa378ec61417e808f5b171d30"}, {"name": "1x __components::c5096c547e1e4799b251226d536a755f", "param_count": null, "params": [], "start": **********.337596, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c5096c547e1e4799b251226d536a755f.blade.php__components::c5096c547e1e4799b251226d536a755f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc5096c547e1e4799b251226d536a755f.blade.php&line=1", "ajax": false, "filename": "c5096c547e1e4799b251226d536a755f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c5096c547e1e4799b251226d536a755f"}, {"name": "2x __components::bbeeb09c8e60a84bbc3758cdf5155cc7", "param_count": null, "params": [], "start": **********.339329, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bbeeb09c8e60a84bbc3758cdf5155cc7.blade.php__components::bbeeb09c8e60a84bbc3758cdf5155cc7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbbeeb09c8e60a84bbc3758cdf5155cc7.blade.php&line=1", "ajax": false, "filename": "bbeeb09c8e60a84bbc3758cdf5155cc7.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::bbeeb09c8e60a84bbc3758cdf5155cc7"}, {"name": "2x __components::782be997dc25a78b850884434c00f240", "param_count": null, "params": [], "start": **********.341066, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/782be997dc25a78b850884434c00f240.blade.php__components::782be997dc25a78b850884434c00f240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F782be997dc25a78b850884434c00f240.blade.php&line=1", "ajax": false, "filename": "782be997dc25a78b850884434c00f240.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::782be997dc25a78b850884434c00f240"}, {"name": "2x __components::871b569fe4b8bc1f493e29254eddc76e", "param_count": null, "params": [], "start": **********.342581, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/871b569fe4b8bc1f493e29254eddc76e.blade.php__components::871b569fe4b8bc1f493e29254eddc76e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F871b569fe4b8bc1f493e29254eddc76e.blade.php&line=1", "ajax": false, "filename": "871b569fe4b8bc1f493e29254eddc76e.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::871b569fe4b8bc1f493e29254eddc76e"}, {"name": "2x __components::d9dc4902cc1ebcdd71cccc9ce512531a", "param_count": null, "params": [], "start": **********.34421, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/d9dc4902cc1ebcdd71cccc9ce512531a.blade.php__components::d9dc4902cc1ebcdd71cccc9ce512531a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fd9dc4902cc1ebcdd71cccc9ce512531a.blade.php&line=1", "ajax": false, "filename": "d9dc4902cc1ebcdd71cccc9ce512531a.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d9dc4902cc1ebcdd71cccc9ce512531a"}, {"name": "1x __components::3c343ff09c2ca79e16f50878c798a182", "param_count": null, "params": [], "start": **********.348148, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/3c343ff09c2ca79e16f50878c798a182.blade.php__components::3c343ff09c2ca79e16f50878c798a182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F3c343ff09c2ca79e16f50878c798a182.blade.php&line=1", "ajax": false, "filename": "3c343ff09c2ca79e16f50878c798a182.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3c343ff09c2ca79e16f50878c798a182"}, {"name": "2x __components::136011ccc3a87208e1917491717f9a4f", "param_count": null, "params": [], "start": **********.350513, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/136011ccc3a87208e1917491717f9a4f.blade.php__components::136011ccc3a87208e1917491717f9a4f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F136011ccc3a87208e1917491717f9a4f.blade.php&line=1", "ajax": false, "filename": "136011ccc3a87208e1917491717f9a4f.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::136011ccc3a87208e1917491717f9a4f"}, {"name": "1x __components::b37254cf00729b74237ee910ffb6596f", "param_count": null, "params": [], "start": **********.353257, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b37254cf00729b74237ee910ffb6596f.blade.php__components::b37254cf00729b74237ee910ffb6596f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb37254cf00729b74237ee910ffb6596f.blade.php&line=1", "ajax": false, "filename": "b37254cf00729b74237ee910ffb6596f.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b37254cf00729b74237ee910ffb6596f"}, {"name": "1x __components::93ef6be6fe1eb178e1feb898975a91ee", "param_count": null, "params": [], "start": **********.356061, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/93ef6be6fe1eb178e1feb898975a91ee.blade.php__components::93ef6be6fe1eb178e1feb898975a91ee", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F93ef6be6fe1eb178e1feb898975a91ee.blade.php&line=1", "ajax": false, "filename": "93ef6be6fe1eb178e1feb898975a91ee.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::93ef6be6fe1eb178e1feb898975a91ee"}, {"name": "1x __components::fbf47fa86220ae7145c8d582e8c0eed4", "param_count": null, "params": [], "start": **********.358353, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/fbf47fa86220ae7145c8d582e8c0eed4.blade.php__components::fbf47fa86220ae7145c8d582e8c0eed4", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ffbf47fa86220ae7145c8d582e8c0eed4.blade.php&line=1", "ajax": false, "filename": "fbf47fa86220ae7145c8d582e8c0eed4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fbf47fa86220ae7145c8d582e8c0eed4"}, {"name": "1x __components::bb3f94f23803809e73a8da21ef4efa8e", "param_count": null, "params": [], "start": **********.359934, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bb3f94f23803809e73a8da21ef4efa8e.blade.php__components::bb3f94f23803809e73a8da21ef4efa8e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbb3f94f23803809e73a8da21ef4efa8e.blade.php&line=1", "ajax": false, "filename": "bb3f94f23803809e73a8da21ef4efa8e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bb3f94f23803809e73a8da21ef4efa8e"}, {"name": "1x __components::83003a3f2a3ed01b01e553b0ac0ad83c", "param_count": null, "params": [], "start": **********.361504, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83003a3f2a3ed01b01e553b0ac0ad83c.blade.php__components::83003a3f2a3ed01b01e553b0ac0ad83c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83003a3f2a3ed01b01e553b0ac0ad83c.blade.php&line=1", "ajax": false, "filename": "83003a3f2a3ed01b01e553b0ac0ad83c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::83003a3f2a3ed01b01e553b0ac0ad83c"}, {"name": "1x __components::6de57ecc36cdded0fb3b0d8da86d69ff", "param_count": null, "params": [], "start": **********.36311, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6de57ecc36cdded0fb3b0d8da86d69ff.blade.php__components::6de57ecc36cdded0fb3b0d8da86d69ff", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6de57ecc36cdded0fb3b0d8da86d69ff.blade.php&line=1", "ajax": false, "filename": "6de57ecc36cdded0fb3b0d8da86d69ff.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6de57ecc36cdded0fb3b0d8da86d69ff"}, {"name": "1x __components::220c546cb91e6a4599242f7bf0cef465", "param_count": null, "params": [], "start": **********.365106, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/220c546cb91e6a4599242f7bf0cef465.blade.php__components::220c546cb91e6a4599242f7bf0cef465", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F220c546cb91e6a4599242f7bf0cef465.blade.php&line=1", "ajax": false, "filename": "220c546cb91e6a4599242f7bf0cef465.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::220c546cb91e6a4599242f7bf0cef465"}, {"name": "1x __components::9dfa94569e0724e5dabad0703eee2d6a", "param_count": null, "params": [], "start": **********.367182, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/9dfa94569e0724e5dabad0703eee2d6a.blade.php__components::9dfa94569e0724e5dabad0703eee2d6a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F9dfa94569e0724e5dabad0703eee2d6a.blade.php&line=1", "ajax": false, "filename": "9dfa94569e0724e5dabad0703eee2d6a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::9dfa94569e0724e5dabad0703eee2d6a"}, {"name": "1x __components::7b66fb07888ac783788e2b26bcd125a8", "param_count": null, "params": [], "start": **********.369047, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7b66fb07888ac783788e2b26bcd125a8.blade.php__components::7b66fb07888ac783788e2b26bcd125a8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7b66fb07888ac783788e2b26bcd125a8.blade.php&line=1", "ajax": false, "filename": "7b66fb07888ac783788e2b26bcd125a8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7b66fb07888ac783788e2b26bcd125a8"}, {"name": "2x __components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "param_count": null, "params": [], "start": **********.370636, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4bd12cd2b54372ae8dc9e9d0ea19ceca.blade.php__components::4bd12cd2b54372ae8dc9e9d0ea19ceca", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4bd12cd2b54372ae8dc9e9d0ea19ceca.blade.php&line=1", "ajax": false, "filename": "4bd12cd2b54372ae8dc9e9d0ea19ceca.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::4bd12cd2b54372ae8dc9e9d0ea19ceca"}, {"name": "1x __components::c949e4db934c39e884a3b8df4f399cf6", "param_count": null, "params": [], "start": **********.373838, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c949e4db934c39e884a3b8df4f399cf6.blade.php__components::c949e4db934c39e884a3b8df4f399cf6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc949e4db934c39e884a3b8df4f399cf6.blade.php&line=1", "ajax": false, "filename": "c949e4db934c39e884a3b8df4f399cf6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c949e4db934c39e884a3b8df4f399cf6"}, {"name": "1x __components::5bdb4266551740440506aff4bf77ee0d", "param_count": null, "params": [], "start": **********.375198, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/5bdb4266551740440506aff4bf77ee0d.blade.php__components::5bdb4266551740440506aff4bf77ee0d", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F5bdb4266551740440506aff4bf77ee0d.blade.php&line=1", "ajax": false, "filename": "5bdb4266551740440506aff4bf77ee0d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5bdb4266551740440506aff4bf77ee0d"}, {"name": "1x __components::5f16700bfd815b296f7f512eeaea5673", "param_count": null, "params": [], "start": **********.376465, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/5f16700bfd815b296f7f512eeaea5673.blade.php__components::5f16700bfd815b296f7f512eeaea5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F5f16700bfd815b296f7f512eeaea5673.blade.php&line=1", "ajax": false, "filename": "5f16700bfd815b296f7f512eeaea5673.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5f16700bfd815b296f7f512eeaea5673"}, {"name": "1x __components::86e645ab8f001358369d5b4e60f4bf59", "param_count": null, "params": [], "start": **********.378476, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/86e645ab8f001358369d5b4e60f4bf59.blade.php__components::86e645ab8f001358369d5b4e60f4bf59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F86e645ab8f001358369d5b4e60f4bf59.blade.php&line=1", "ajax": false, "filename": "86e645ab8f001358369d5b4e60f4bf59.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::86e645ab8f001358369d5b4e60f4bf59"}, {"name": "1x __components::2aa3fa40c579d34617cbba7fa1f682ca", "param_count": null, "params": [], "start": **********.379738, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/2aa3fa40c579d34617cbba7fa1f682ca.blade.php__components::2aa3fa40c579d34617cbba7fa1f682ca", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F2aa3fa40c579d34617cbba7fa1f682ca.blade.php&line=1", "ajax": false, "filename": "2aa3fa40c579d34617cbba7fa1f682ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2aa3fa40c579d34617cbba7fa1f682ca"}, {"name": "1x __components::b5ca655d4335b87ed0d138a5d744f3d6", "param_count": null, "params": [], "start": **********.387514, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b5ca655d4335b87ed0d138a5d744f3d6.blade.php__components::b5ca655d4335b87ed0d138a5d744f3d6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb5ca655d4335b87ed0d138a5d744f3d6.blade.php&line=1", "ajax": false, "filename": "b5ca655d4335b87ed0d138a5d744f3d6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b5ca655d4335b87ed0d138a5d744f3d6"}, {"name": "1x __components::7891d25efdd26b215062e0b170dbcab6", "param_count": null, "params": [], "start": **********.391058, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7891d25efdd26b215062e0b170dbcab6.blade.php__components::7891d25efdd26b215062e0b170dbcab6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7891d25efdd26b215062e0b170dbcab6.blade.php&line=1", "ajax": false, "filename": "7891d25efdd26b215062e0b170dbcab6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7891d25efdd26b215062e0b170dbcab6"}, {"name": "1x __components::7e1275e529df599477ba24a2029d9c4c", "param_count": null, "params": [], "start": **********.392709, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7e1275e529df599477ba24a2029d9c4c.blade.php__components::7e1275e529df599477ba24a2029d9c4c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7e1275e529df599477ba24a2029d9c4c.blade.php&line=1", "ajax": false, "filename": "7e1275e529df599477ba24a2029d9c4c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e1275e529df599477ba24a2029d9c4c"}, {"name": "1x __components::f754263ea240437bcce4902de59bc4b9", "param_count": null, "params": [], "start": **********.394651, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f754263ea240437bcce4902de59bc4b9.blade.php__components::f754263ea240437bcce4902de59bc4b9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff754263ea240437bcce4902de59bc4b9.blade.php&line=1", "ajax": false, "filename": "f754263ea240437bcce4902de59bc4b9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f754263ea240437bcce4902de59bc4b9"}, {"name": "1x __components::b1589f415f31dd93e57e18d0643b6093", "param_count": null, "params": [], "start": **********.395992, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b1589f415f31dd93e57e18d0643b6093.blade.php__components::b1589f415f31dd93e57e18d0643b6093", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb1589f415f31dd93e57e18d0643b6093.blade.php&line=1", "ajax": false, "filename": "b1589f415f31dd93e57e18d0643b6093.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b1589f415f31dd93e57e18d0643b6093"}, {"name": "1x __components::99c7a16739e2f632d30b83abd5fdcad9", "param_count": null, "params": [], "start": **********.397555, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/99c7a16739e2f632d30b83abd5fdcad9.blade.php__components::99c7a16739e2f632d30b83abd5fdcad9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F99c7a16739e2f632d30b83abd5fdcad9.blade.php&line=1", "ajax": false, "filename": "99c7a16739e2f632d30b83abd5fdcad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::99c7a16739e2f632d30b83abd5fdcad9"}, {"name": "1x __components::adfc97029070652f0e172ecbe3494c8b", "param_count": null, "params": [], "start": **********.399369, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/adfc97029070652f0e172ecbe3494c8b.blade.php__components::adfc97029070652f0e172ecbe3494c8b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fadfc97029070652f0e172ecbe3494c8b.blade.php&line=1", "ajax": false, "filename": "adfc97029070652f0e172ecbe3494c8b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::adfc97029070652f0e172ecbe3494c8b"}, {"name": "1x __components::8fdc46d28d72c53c63d9392f76f64223", "param_count": null, "params": [], "start": **********.401669, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/8fdc46d28d72c53c63d9392f76f64223.blade.php__components::8fdc46d28d72c53c63d9392f76f64223", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F8fdc46d28d72c53c63d9392f76f64223.blade.php&line=1", "ajax": false, "filename": "8fdc46d28d72c53c63d9392f76f64223.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::8fdc46d28d72c53c63d9392f76f64223"}, {"name": "1x __components::5409a33a8ed4a8289e392fc684d7eb1a", "param_count": null, "params": [], "start": **********.404, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/5409a33a8ed4a8289e392fc684d7eb1a.blade.php__components::5409a33a8ed4a8289e392fc684d7eb1a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F5409a33a8ed4a8289e392fc684d7eb1a.blade.php&line=1", "ajax": false, "filename": "5409a33a8ed4a8289e392fc684d7eb1a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5409a33a8ed4a8289e392fc684d7eb1a"}, {"name": "1x __components::ff2a50cf02e428f29948df9e0b706bc2", "param_count": null, "params": [], "start": **********.406101, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ff2a50cf02e428f29948df9e0b706bc2.blade.php__components::ff2a50cf02e428f29948df9e0b706bc2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fff2a50cf02e428f29948df9e0b706bc2.blade.php&line=1", "ajax": false, "filename": "ff2a50cf02e428f29948df9e0b706bc2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff2a50cf02e428f29948df9e0b706bc2"}, {"name": "1x livewire.backend.header-navigation", "param_count": null, "params": [], "start": **********.412772, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/backend/header-navigation.blade.phplivewire.backend.header-navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fbackend%2Fheader-navigation.blade.php&line=1", "ajax": false, "filename": "header-navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.backend.header-navigation"}, {"name": "1x __components::ac8aa99487bb4cf2a4bcd65a29a3f04d", "param_count": null, "params": [], "start": **********.415768, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ac8aa99487bb4cf2a4bcd65a29a3f04d.blade.php__components::ac8aa99487bb4cf2a4bcd65a29a3f04d", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fac8aa99487bb4cf2a4bcd65a29a3f04d.blade.php&line=1", "ajax": false, "filename": "ac8aa99487bb4cf2a4bcd65a29a3f04d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ac8aa99487bb4cf2a4bcd65a29a3f04d"}, {"name": "1x __components::df7c6ab5de2b42f6cee2f41a9504d04c", "param_count": null, "params": [], "start": **********.416919, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/df7c6ab5de2b42f6cee2f41a9504d04c.blade.php__components::df7c6ab5de2b42f6cee2f41a9504d04c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fdf7c6ab5de2b42f6cee2f41a9504d04c.blade.php&line=1", "ajax": false, "filename": "df7c6ab5de2b42f6cee2f41a9504d04c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::df7c6ab5de2b42f6cee2f41a9504d04c"}, {"name": "1x __components::368721f198a3f75f1fe85ac4948f0e84", "param_count": null, "params": [], "start": **********.41796, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/368721f198a3f75f1fe85ac4948f0e84.blade.php__components::368721f198a3f75f1fe85ac4948f0e84", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F368721f198a3f75f1fe85ac4948f0e84.blade.php&line=1", "ajax": false, "filename": "368721f198a3f75f1fe85ac4948f0e84.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::368721f198a3f75f1fe85ac4948f0e84"}, {"name": "1x __components::914f269289845944af4ea7064df47b48", "param_count": null, "params": [], "start": **********.420928, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/914f269289845944af4ea7064df47b48.blade.php__components::914f269289845944af4ea7064df47b48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F914f269289845944af4ea7064df47b48.blade.php&line=1", "ajax": false, "filename": "914f269289845944af4ea7064df47b48.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::914f269289845944af4ea7064df47b48"}, {"name": "1x __components::73f1fde11c4734cced4d836d3a092bec", "param_count": null, "params": [], "start": **********.423677, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/73f1fde11c4734cced4d836d3a092bec.blade.php__components::73f1fde11c4734cced4d836d3a092bec", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F73f1fde11c4734cced4d836d3a092bec.blade.php&line=1", "ajax": false, "filename": "73f1fde11c4734cced4d836d3a092bec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::73f1fde11c4734cced4d836d3a092bec"}, {"name": "2x components.button.primary-round", "param_count": null, "params": [], "start": **********.424005, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/primary-round.blade.phpcomponents.button.primary-round", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fprimary-round.blade.php&line=1", "ajax": false, "filename": "primary-round.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.button.primary-round"}, {"name": "1x __components::18061f0a7de6cc38934967fcf0ab7119", "param_count": null, "params": [], "start": **********.424976, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/18061f0a7de6cc38934967fcf0ab7119.blade.php__components::18061f0a7de6cc38934967fcf0ab7119", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F18061f0a7de6cc38934967fcf0ab7119.blade.php&line=1", "ajax": false, "filename": "18061f0a7de6cc38934967fcf0ab7119.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::18061f0a7de6cc38934967fcf0ab7119"}, {"name": "1x __components::826d9ae0f124683ce39e8e5582533db2", "param_count": null, "params": [], "start": **********.426472, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/826d9ae0f124683ce39e8e5582533db2.blade.php__components::826d9ae0f124683ce39e8e5582533db2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F826d9ae0f124683ce39e8e5582533db2.blade.php&line=1", "ajax": false, "filename": "826d9ae0f124683ce39e8e5582533db2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::826d9ae0f124683ce39e8e5582533db2"}, {"name": "1x __components::f43fb37d760babea44d38d2243b9db02", "param_count": null, "params": [], "start": **********.427766, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f43fb37d760babea44d38d2243b9db02.blade.php__components::f43fb37d760babea44d38d2243b9db02", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff43fb37d760babea44d38d2243b9db02.blade.php&line=1", "ajax": false, "filename": "f43fb37d760babea44d38d2243b9db02.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f43fb37d760babea44d38d2243b9db02"}, {"name": "1x __components::6b40256b79ce902897968ff2c396eeb2", "param_count": null, "params": [], "start": **********.429065, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6b40256b79ce902897968ff2c396eeb2.blade.php__components::6b40256b79ce902897968ff2c396eeb2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6b40256b79ce902897968ff2c396eeb2.blade.php&line=1", "ajax": false, "filename": "6b40256b79ce902897968ff2c396eeb2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6b40256b79ce902897968ff2c396eeb2"}, {"name": "1x __components::7dbf7b1d342ba2c17a858fb8b23129fb", "param_count": null, "params": [], "start": **********.430317, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7dbf7b1d342ba2c17a858fb8b23129fb.blade.php__components::7dbf7b1d342ba2c17a858fb8b23129fb", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7dbf7b1d342ba2c17a858fb8b23129fb.blade.php&line=1", "ajax": false, "filename": "7dbf7b1d342ba2c17a858fb8b23129fb.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7dbf7b1d342ba2c17a858fb8b23129fb"}, {"name": "1x __components::4a886341e8b9106f3f63a321813c268e", "param_count": null, "params": [], "start": **********.43157, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4a886341e8b9106f3f63a321813c268e.blade.php__components::4a886341e8b9106f3f63a321813c268e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4a886341e8b9106f3f63a321813c268e.blade.php&line=1", "ajax": false, "filename": "4a886341e8b9106f3f63a321813c268e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4a886341e8b9106f3f63a321813c268e"}, {"name": "1x __components::b45136778d9e1f97d0856c0b8bfe0ab6", "param_count": null, "params": [], "start": **********.432815, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/b45136778d9e1f97d0856c0b8bfe0ab6.blade.php__components::b45136778d9e1f97d0856c0b8bfe0ab6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fb45136778d9e1f97d0856c0b8bfe0ab6.blade.php&line=1", "ajax": false, "filename": "b45136778d9e1f97d0856c0b8bfe0ab6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b45136778d9e1f97d0856c0b8bfe0ab6"}, {"name": "1x livewire.language-switcher", "param_count": null, "params": [], "start": **********.435715, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/language-switcher.blade.phplivewire.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.language-switcher"}, {"name": "1x __components::7d5927e3602df13b6cb94272364e60cd", "param_count": null, "params": [], "start": **********.436756, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7d5927e3602df13b6cb94272364e60cd.blade.php__components::7d5927e3602df13b6cb94272364e60cd", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7d5927e3602df13b6cb94272364e60cd.blade.php&line=1", "ajax": false, "filename": "7d5927e3602df13b6cb94272364e60cd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7d5927e3602df13b6cb94272364e60cd"}, {"name": "1x __components::626e3186dfddcc134144cee5faad3af0", "param_count": null, "params": [], "start": **********.44334, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/626e3186dfddcc134144cee5faad3af0.blade.php__components::626e3186dfddcc134144cee5faad3af0", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F626e3186dfddcc134144cee5faad3af0.blade.php&line=1", "ajax": false, "filename": "626e3186dfddcc134144cee5faad3af0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::626e3186dfddcc134144cee5faad3af0"}, {"name": "1x __components::bf02a25af80fbc3a9220a1cbfec1ed84", "param_count": null, "params": [], "start": **********.444421, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bf02a25af80fbc3a9220a1cbfec1ed84.blade.php__components::bf02a25af80fbc3a9220a1cbfec1ed84", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbf02a25af80fbc3a9220a1cbfec1ed84.blade.php&line=1", "ajax": false, "filename": "bf02a25af80fbc3a9220a1cbfec1ed84.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bf02a25af80fbc3a9220a1cbfec1ed84"}, {"name": "1x __components::4a422c51ce49d9d4b3edb4019ae706ba", "param_count": null, "params": [], "start": **********.445661, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/4a422c51ce49d9d4b3edb4019ae706ba.blade.php__components::4a422c51ce49d9d4b3edb4019ae706ba", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F4a422c51ce49d9d4b3edb4019ae706ba.blade.php&line=1", "ajax": false, "filename": "4a422c51ce49d9d4b3edb4019ae706ba.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4a422c51ce49d9d4b3edb4019ae706ba"}, {"name": "1x __components::bd9adf6608e0d55e77c26a59fa85b0a2", "param_count": null, "params": [], "start": **********.446729, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/bd9adf6608e0d55e77c26a59fa85b0a2.blade.php__components::bd9adf6608e0d55e77c26a59fa85b0a2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fbd9adf6608e0d55e77c26a59fa85b0a2.blade.php&line=1", "ajax": false, "filename": "bd9adf6608e0d55e77c26a59fa85b0a2.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::bd9adf6608e0d55e77c26a59fa85b0a2"}, {"name": "1x __components::c73f13ef25c37df445e040c2e298817c", "param_count": null, "params": [], "start": **********.448796, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c73f13ef25c37df445e040c2e298817c.blade.php__components::c73f13ef25c37df445e040c2e298817c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc73f13ef25c37df445e040c2e298817c.blade.php&line=1", "ajax": false, "filename": "c73f13ef25c37df445e040c2e298817c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c73f13ef25c37df445e040c2e298817c"}, {"name": "1x __components::66b7debda96562d7d5ec9a50db5fb253", "param_count": null, "params": [], "start": **********.450773, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/66b7debda96562d7d5ec9a50db5fb253.blade.php__components::66b7debda96562d7d5ec9a50db5fb253", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F66b7debda96562d7d5ec9a50db5fb253.blade.php&line=1", "ajax": false, "filename": "66b7debda96562d7d5ec9a50db5fb253.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::66b7debda96562d7d5ec9a50db5fb253"}, {"name": "1x components.notification", "param_count": null, "params": [], "start": **********.457872, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/notification.blade.phpcomponents.notification", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.notification"}, {"name": "1x __components::e619de47f59c986e9f566638044a3ab7", "param_count": null, "params": [], "start": **********.459189, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/e619de47f59c986e9f566638044a3ab7.blade.php__components::e619de47f59c986e9f566638044a3ab7", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fe619de47f59c986e9f566638044a3ab7.blade.php&line=1", "ajax": false, "filename": "e619de47f59c986e9f566638044a3ab7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e619de47f59c986e9f566638044a3ab7"}, {"name": "1x __components::f2b343610bcc0778e0f00f4bfe27f188", "param_count": null, "params": [], "start": **********.460325, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f2b343610bcc0778e0f00f4bfe27f188.blade.php__components::f2b343610bcc0778e0f00f4bfe27f188", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff2b343610bcc0778e0f00f4bfe27f188.blade.php&line=1", "ajax": false, "filename": "f2b343610bcc0778e0f00f4bfe27f188.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f2b343610bcc0778e0f00f4bfe27f188"}, {"name": "1x __components::0d5cde93dab074a387e37ef65b78febc", "param_count": null, "params": [], "start": **********.461564, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/0d5cde93dab074a387e37ef65b78febc.blade.php__components::0d5cde93dab074a387e37ef65b78febc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F0d5cde93dab074a387e37ef65b78febc.blade.php&line=1", "ajax": false, "filename": "0d5cde93dab074a387e37ef65b78febc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0d5cde93dab074a387e37ef65b78febc"}, {"name": "1x __components::10cb7a8570e3e511d44febea50de8e08", "param_count": null, "params": [], "start": **********.46265, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/10cb7a8570e3e511d44febea50de8e08.blade.php__components::10cb7a8570e3e511d44febea50de8e08", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F10cb7a8570e3e511d44febea50de8e08.blade.php&line=1", "ajax": false, "filename": "10cb7a8570e3e511d44febea50de8e08.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::10cb7a8570e3e511d44febea50de8e08"}, {"name": "1x __components::46553ac5b5a848738f4879c0717608ca", "param_count": null, "params": [], "start": **********.464724, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/46553ac5b5a848738f4879c0717608ca.blade.php__components::46553ac5b5a848738f4879c0717608ca", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F46553ac5b5a848738f4879c0717608ca.blade.php&line=1", "ajax": false, "filename": "46553ac5b5a848738f4879c0717608ca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::46553ac5b5a848738f4879c0717608ca"}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00211, "accumulated_duration_str": "2.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.101295, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 25.592}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/AdminMiddleware.php", "file": "C:\\laragon\\www\\whats\\app\\Http\\Middleware\\AdminMiddleware.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.114593, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 25.592, "width_percent": 27.014}, {"sql": "select `code`, `name` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.438014, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "GeneralHelper.php:376", "source": {"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FHelpers%2FGeneralHelper.php&line=376", "ajax": false, "filename": "GeneralHelper.php", "line": "376"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 52.607, "width_percent": 47.393}]}, "models": {"data": {"App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"admin.settings.system.system-update-settings #DgsQfvhFvEbvmHhhNI88": "array:4 [\n  \"data\" => array:11 [\n    \"currentVersion\" => \"\"\n    \"latestVersion\" => \"1.0.4\"\n    \"purchase_key\" => null\n    \"username\" => null\n    \"update_id\" => \"29a9f03edda39454031c\"\n    \"has_sql_update\" => true\n    \"releases\" => []\n    \"update\" => array:7 [\n      \"current_version\" => \"1.0.3\"\n      \"latest_version\" => \"1.0.4\"\n      \"update_id\" => \"29a9f03edda39454031c\"\n      \"has_sql_update\" => true\n      \"release_date\" => \"2025-05-23\"\n      \"changelog\" => \"<p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">New Features</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Privacy &amp; Policy Page - Added a section to show privacy and policy details manage within permissions</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile – no switching tabs.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Delete Chat Permission - Only allowed users can delete chats now – more control and security.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Sidebar Collapse/Expand - You can hide or show the sidebar to make more space on your screen.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Download Campaign Reports - Easily download reports for your campaigns.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Module Concept - Module concept implemented</span></li></ol><p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Bug Fixes</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Fixed an issue where some templates were not loading from Meta – now all templates load properly.</span></li></ol><p><br></p><p><br></p>\"\n      \"summary\" => \"minor update\"\n    ]\n    \"token\" => \"ZGVmYXVsdC12ZXJpZmljYXRpb24taWQ=\"\n    \"support\" => []\n    \"versionLog\" => array:2 [\n      \"success\" => true\n      \"versions\" => array:5 [\n        0 => array:4 [\n          \"version\" => \"1.1.0\"\n          \"date\" => \"July 04, 2025\"\n          \"is_latest\" => true\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Bot Flow Builder</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Build automated conversation flows with a drag-and-drop interface.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Management</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Introduced module management feature including core module support for better scalability and organization.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Campaign New Layout</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Updated campaign UI with enhanced filters for better targeting and usability.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Optimized Chat Loading</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Improved chat load speed and performance for smoother user experience.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">New Line Message Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – You can now send messages with new lines in chat (Shift + Enter supported).</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Location &amp; Contacts Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Chat now supports receiving shared location and contact data for better communication.</span></li></ol>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Fixed several performance issues in chat and campaign sections for faster operation and better stability.</span></li></ol>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<p><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">New Feature</strong></p><p><br></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Bot Flow Builder</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Build automated conversation flows with a drag-and-drop interface.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Management</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Introduced module management feature including core module support for better scalability and organization.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Campaign New Layout</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Updated campaign UI with enhanced filters for better targeting and usability.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Optimized Chat Loading</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Improved chat load speed and performance for smoother user experience.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">New Line Message Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – You can now send messages with new lines in chat (Shift + Enter supported).</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Location &amp; Contacts Support</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Chat now supports receiving shared location and contact data for better communication.</span></li></ol><p><br></p><p><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> </span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Bug Fix</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> Fixed several performance issues in chat and campaign sections for faster operation and better stability.</span></li></ol>\"\n            ]\n          ]\n        ]\n        1 => array:4 [\n          \"version\" => \"1.0.5\"\n          \"date\" => \"July 03, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Module Management Preparation</strong><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\"> – Refactored internal structure to lay the groundwork for future advanced module features.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Codebase Cleanup</strong><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\"> – Removed deprecated logic and optimized backend structure related to module loading.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Improved Stability</strong><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\"> – Minor updates to enhance system reliability during module initialization.</span></li></ol><p><br></p>\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Management Preparation</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Refactored internal structure to lay the groundwork for future advanced module features.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Codebase Cleanup</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Removed deprecated logic and optimized backend structure related to module loading.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><strong style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Improved Stability</strong><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\"> – Minor updates to enhance system reliability during module initialization.</span></li></ol>\"\n            ]\n          ]\n        ]\n        2 => array:4 [\n          \"version\" => \"1.0.4\"\n          \"date\" => \"May 23, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Privacy &amp; Policy Page - Added a section to show privacy and policy details manage within permissions</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile – no switching tabs.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Delete Chat Permission - Only allowed users can delete chats now – more control and security.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Sidebar Collapse/Expand - You can hide or show the sidebar to make more space on your screen.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Download Campaign Reports - Easily download reports for your campaigns.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Module Concept - Module concept implemented</span></li></ol><p><br></p>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"background-color: rgb(248, 248, 248); color: rgb(29, 28, 29);\">Fixed an issue where some templates were not loading from Meta – now all templates load properly.</span></li></ol>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">New Features</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Privacy &amp; Policy Page - Added a section to show privacy and policy details manage within permissions</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Initiate Chat from Contacts and Chat Section - Connect instantly with your customers right from their profile – no switching tabs.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Delete Chat Permission - Only allowed users can delete chats now – more control and security.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Sidebar Collapse/Expand - You can hide or show the sidebar to make more space on your screen.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Download Campaign Reports - Easily download reports for your campaigns.</span></li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Module Concept - Module concept implemented</span></li></ol><p><strong style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Bug Fixes</strong></p><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span><span style=\"color: rgb(29, 28, 29); background-color: rgb(248, 248, 248);\">Fixed an issue where some templates were not loading from Meta – now all templates load properly.</span></li></ol><p><br></p><p><br></p>\"\n            ]\n          ]\n        ]\n        3 => array:4 [\n          \"version\" => \"1.0.3\"\n          \"date\" => \"April 22, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>The module concept will be implemented in upcoming features.</li></ol>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li></ol>\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fix the issue with updating the template.</li></ol>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<h3><strong>Features</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>The module concept will be implemented in upcoming features.</li></ol><p><br></p><h3><strong>Bug Fixes</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fix the issue with updating the template.</li></ol><p><br></p><h3><strong>Improvements</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li></ol><p><br></p>\"\n            ]\n          ]\n        ]\n        4 => array:4 [\n          \"version\" => \"1.0.2\"\n          \"date\" => \"April 14, 2025\"\n          \"is_latest\" => false\n          \"changes\" => array:4 [\n            0 => array:2 [\n              \"type\" => \"feature\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Option added to enable or disable environment mode.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Ability to toggle Laravel and WhatsApp logs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Refresh button added to all data tables.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Added functionality to resend failed campaigns.</li></ol><p><br></p>\"\n            ]\n            1 => array:2 [\n              \"type\" => \"improvement\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Improved performance of campaign queue jobs and cron jobs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>General performance improvements across the system.</li></ol><p><br></p>\"\n            ]\n            2 => array:2 [\n              \"type\" => \"bug\"\n              \"description\" => \"<ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fixed an issue where campaign statistics were displaying incorrect data.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>In some cases, campaign scheduling was not running properly.</li></ol><p><br></p>\"\n            ]\n            3 => array:2 [\n              \"type\" => \"changelog\"\n              \"description\" => \"<h3><strong>Features</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Option added to enable or disable environment mode.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Ability to toggle Laravel and WhatsApp logs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Refresh button added to all data tables.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Added functionality to resend failed campaigns.</li></ol><p><br></p><h3><strong>Bug Fixes</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Fixed an issue where campaign statistics were displaying incorrect data.</li></ol><p><br></p><h3><strong>Improvements</strong></h3><ol><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Enhanced identification of potential bugs and issues.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>Improved performance of campaign queue jobs and cron jobs.</li><li data-list=\"bullet\"><span class=\"ql-ui\" contenteditable=\"false\"></span>General performance improvements across the system.</li></ol><p><br></p>\"\n            ]\n          ]\n        ]\n      ]\n    ]\n  ]\n  \"name\" => \"admin.settings.system.system-update-settings\"\n  \"component\" => \"App\\Livewire\\Admin\\Settings\\System\\SystemUpdateSettings\"\n  \"id\" => \"DgsQfvhFvEbvmHhhNI88\"\n]", "backend.sidebar-navigation #pgrGjKRqnhAek5pc4OX1": "array:4 [\n  \"data\" => []\n  \"name\" => \"backend.sidebar-navigation\"\n  \"component\" => \"App\\Livewire\\Backend\\SidebarNavigation\"\n  \"id\" => \"pgrGjKRqnhAek5pc4OX1\"\n]", "backend.header-navigation #JJWdnIrSfStjA3FVc1Xb": "array:4 [\n  \"data\" => []\n  \"name\" => \"backend.header-navigation\"\n  \"component\" => \"App\\Livewire\\Backend\\HeaderNavigation\"\n  \"id\" => \"JJWdnIrSfStjA3FVc1Xb\"\n]", "language-switcher #SDHns6ff5HzAqIIk9mQs": "array:4 [\n  \"data\" => array:1 [\n    \"currentLocale\" => \"en\"\n  ]\n  \"name\" => \"language-switcher\"\n  \"component\" => \"App\\Livewire\\LanguageSwitcher\"\n  \"id\" => \"SDHns6ff5HzAqIIk9mQs\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/system-update", "action_name": "admin.system-update.settings.view", "controller_action": "App\\Livewire\\Admin\\Settings\\System\\SystemUpdateSettings", "uri": "GET admin/system-update", "controller": "App\\Livewire\\Admin\\Settings\\System\\SystemUpdateSettings@render<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FSettings%2FSystem%2FSystemUpdateSettings.php&line=82\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FSettings%2FSystem%2FSystemUpdateSettings.php&line=82\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Admin/Settings/System/SystemUpdateSettings.php:82-85</a>", "middleware": "web, validate, App\\Http\\Middleware\\AdminMiddleware, App\\Http\\Middleware\\SanitizeInputs", "duration": "2.96s", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-243799871 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-243799871\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-317703604 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-317703604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1202147570 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdQVFFaQkFqUXBOdm9TTDJ6S3lPbUE9PSIsInZhbHVlIjoidlFOVTdlTEppVlNMOTV0eGVjeHVxOW1LQjQ2UWxYQ2dBR3NuWVBoWGIwdnIvWmloRUdPUmN4SEJIYXVVZkRLSGYxaWJwWmxUbTNwdHl0UG5FVk5scDJyUm1vdHZuM0NJNEU2SGpydjZSNVVNRzBIclZZNjYxTVZFbmc3Vi9aZzIiLCJtYWMiOiJiOTZmOWQ4MjhlZDczNmVjZTc5MTExZTY3YTkwOWQ2MWM5NTllYTIwMmQ4MzYzMTQ1NDE1YTU1Nzg5NzZiYjkzIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Im13Umgwb2RrUVF3Qm1OSUMvSjAvWnc9PSIsInZhbHVlIjoiYmlBYWhIMXBMVDlqeDJ2Sng0dXFZN050ajM3SUMvUmk3UEIwVTdvcmxjdWFlUEx2bGdhcDc0SW0rVTRURzBvdy9mVUFXUE9oWHgyU1Fnckd2S2IzaEJNZEV0TEQ5ZXhQSHR4RnR0bjFWcVpUUkdQdjJZS00ybmNGU3A2bWp2TFQiLCJtYWMiOiIwMTI0MGM1ZjRhN2QzZTcyZTMzODM3ZjczMGIxMzgyZmFmN2ZjMDI4Y2NmOTdkYmMyM2FlNmFkYmU1MTdiYjAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202147570\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-591183821 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-591183821\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1951679660 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:48:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951679660\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-146363029 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146363029\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/system-update", "action_name": "admin.system-update.settings.view", "controller_action": "App\\Livewire\\Admin\\Settings\\System\\SystemUpdateSettings"}, "badge": null}}