{"__meta": {"id": "01JZZHFSE07MT3TTV32SNC07C6", "datetime": "2025-07-12 04:44:05", "utime": **********.696699, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331444.808772, "end": **********.696708, "duration": 0.8879358768463135, "duration_str": "888ms", "measures": [{"label": "Booting", "start": 1752331444.808772, "relative_start": 0, "end": **********.400418, "relative_end": **********.400418, "duration": 0.****************, "duration_str": "592ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.40047, "relative_start": 0.****************, "end": **********.696709, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "296ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.417889, "relative_start": 0.****************, "end": **********.421852, "relative_end": **********.421852, "duration": 0.003962993621826172, "duration_str": "3.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.table-base", "start": **********.631103, "relative_start": 0.****************, "end": **********.631103, "relative_end": **********.631103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header", "start": **********.631767, "relative_start": 0.****************, "end": **********.631767, "relative_end": **********.631767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "start": **********.633153, "relative_start": 0.8243808746337891, "end": **********.633153, "relative_end": **********.633153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye-off", "start": **********.636346, "relative_start": 0.8275740146636963, "end": **********.636346, "relative_end": **********.636346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.637054, "relative_start": 0.8282818794250488, "end": **********.637054, "relative_end": **********.637054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.637594, "relative_start": 0.8288218975067139, "end": **********.637594, "relative_end": **********.637594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.638125, "relative_start": 0.829352855682373, "end": **********.638125, "relative_end": **********.638125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.eye", "start": **********.638514, "relative_start": 0.8297419548034668, "end": **********.638514, "relative_end": **********.638514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "start": **********.638895, "relative_start": 0.8301229476928711, "end": **********.638895, "relative_end": **********.638895, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.search", "start": **********.639448, "relative_start": 0.8306758403778076, "end": **********.639448, "relative_end": **********.639448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.search", "start": **********.640023, "relative_start": 0.8312509059906006, "end": **********.640023, "relative_end": **********.640023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "start": **********.641754, "relative_start": 0.8329818248748779, "end": **********.641754, "relative_end": **********.641754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "start": **********.642537, "relative_start": 0.8337650299072266, "end": **********.642537, "relative_end": **********.642537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table", "start": **********.642998, "relative_start": 0.8342258930206299, "end": **********.642998, "relative_end": **********.642998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.643823, "relative_start": 0.8350508213043213, "end": **********.643823, "relative_end": **********.643823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.644536, "relative_start": 0.8357639312744141, "end": **********.644536, "relative_end": **********.644536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f80280cbef3647ab5b62bdedf0ef25a1", "start": **********.652587, "relative_start": 0.8438148498535156, "end": **********.652587, "relative_end": **********.652587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-down", "start": **********.653153, "relative_start": 0.8443808555603027, "end": **********.653153, "relative_end": **********.653153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.65352, "relative_start": 0.8447480201721191, "end": **********.65352, "relative_end": **********.65352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.654621, "relative_start": 0.845848798751831, "end": **********.654621, "relative_end": **********.654621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.655515, "relative_start": 0.8467428684234619, "end": **********.655515, "relative_end": **********.655515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.655933, "relative_start": 0.847160816192627, "end": **********.655933, "relative_end": **********.655933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.656484, "relative_start": 0.8477118015289307, "end": **********.656484, "relative_end": **********.656484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.65687, "relative_start": 0.8480978012084961, "end": **********.65687, "relative_end": **********.65687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.657178, "relative_start": 0.8484058380126953, "end": **********.657178, "relative_end": **********.657178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6da562a044c365ee08fb55b6325b90a1", "start": **********.657669, "relative_start": 0.8488969802856445, "end": **********.657669, "relative_end": **********.657669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.chevron-up-down", "start": **********.658048, "relative_start": 0.8492758274078369, "end": **********.658048, "relative_end": **********.658048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.cols", "start": **********.658349, "relative_start": 0.8495769500732422, "end": **********.658349, "relative_end": **********.658349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table.tr", "start": **********.658828, "relative_start": 0.8500559329986572, "end": **********.658828, "relative_end": **********.658828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.custom-loading", "start": **********.659373, "relative_start": 0.8506009578704834, "end": **********.659373, "relative_end": **********.659373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.660227, "relative_start": 0.8514549732208252, "end": **********.660227, "relative_end": **********.660227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.661464, "relative_start": 0.8526918888092041, "end": **********.661464, "relative_end": **********.661464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.663111, "relative_start": 0.8543388843536377, "end": **********.663111, "relative_end": **********.663111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.663868, "relative_start": 0.8550958633422852, "end": **********.663868, "relative_end": **********.663868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.664714, "relative_start": 0.8559420108795166, "end": **********.664714, "relative_end": **********.664714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.665828, "relative_start": 0.8570559024810791, "end": **********.665828, "relative_end": **********.665828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.668801, "relative_start": 0.8600289821624756, "end": **********.668801, "relative_end": **********.668801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.669814, "relative_start": 0.8610420227050781, "end": **********.669814, "relative_end": **********.669814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.671504, "relative_start": 0.86273193359375, "end": **********.671504, "relative_end": **********.671504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.672661, "relative_start": 0.8638889789581299, "end": **********.672661, "relative_end": **********.672661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.673875, "relative_start": 0.865103006362915, "end": **********.673875, "relative_end": **********.673875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.674727, "relative_start": 0.8659548759460449, "end": **********.674727, "relative_end": **********.674727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.675869, "relative_start": 0.8670969009399414, "end": **********.675869, "relative_end": **********.675869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.676734, "relative_start": 0.8679618835449219, "end": **********.676734, "relative_end": **********.676734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.678174, "relative_start": 0.8694019317626953, "end": **********.678174, "relative_end": **********.678174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.679027, "relative_start": 0.8702549934387207, "end": **********.679027, "relative_end": **********.679027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.680248, "relative_start": 0.8714759349822998, "end": **********.680248, "relative_end": **********.680248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.6814, "relative_start": 0.8726279735565186, "end": **********.6814, "relative_end": **********.6814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.row", "start": **********.684453, "relative_start": 0.8756809234619141, "end": **********.684453, "relative_end": **********.684453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.toggleable", "start": **********.685984, "relative_start": 0.8772118091583252, "end": **********.685984, "relative_end": **********.685984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.table-base", "start": **********.687037, "relative_start": 0.8782649040222168, "end": **********.687037, "relative_end": **********.687037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.footer", "start": **********.688684, "relative_start": 0.8799118995666504, "end": **********.688684, "relative_end": **********.688684, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.icons.down", "start": **********.689612, "relative_start": 0.8808398246765137, "end": **********.689612, "relative_end": **********.689612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-powergrid::components.frameworks.tailwind.pagination", "start": **********.690399, "relative_start": 0.8816268444061279, "end": **********.690399, "relative_end": **********.690399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.693452, "relative_start": 0.8846797943115234, "end": **********.694519, "relative_end": **********.694519, "duration": 0.0010671615600585938, "duration_str": "1.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 38415280, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 54, "nb_templates": 54, "templates": [{"name": "1x livewire-powergrid::components.frameworks.tailwind.table-base", "param_count": null, "params": [], "start": **********.631061, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/table-base.blade.phplivewire-powergrid::components.frameworks.tailwind.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header", "param_count": null, "params": [], "start": **********.631732, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header.blade.phplivewire-powergrid::components.frameworks.tailwind.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "param_count": null, "params": [], "start": **********.633078, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/toggle-columns.blade.phplivewire-powergrid::components.frameworks.tailwind.header.toggle-columns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Ftoggle-columns.blade.php&line=1", "ajax": false, "filename": "toggle-columns.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.toggle-columns"}, {"name": "1x livewire-powergrid::components.icons.eye-off", "param_count": null, "params": [], "start": **********.636297, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye-off.blade.phplivewire-powergrid::components.icons.eye-off", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye-off.blade.php&line=1", "ajax": false, "filename": "eye-off.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.eye-off"}, {"name": "4x livewire-powergrid::components.icons.eye", "param_count": null, "params": [], "start": **********.637017, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/eye.blade.phplivewire-powergrid::components.icons.eye", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Feye.blade.php&line=1", "ajax": false, "filename": "eye.blade.php", "line": "?"}, "render_count": 4, "name_original": "livewire-powergrid::components.icons.eye"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "param_count": null, "params": [], "start": **********.638861, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsoft-deletes.blade.php&line=1", "ajax": false, "filename": "soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.soft-deletes"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.search", "param_count": null, "params": [], "start": **********.639414, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/search.blade.phplivewire-powergrid::components.frameworks.tailwind.header.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.search"}, {"name": "1x livewire-powergrid::components.icons.search", "param_count": null, "params": [], "start": **********.639989, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/search.blade.phplivewire-powergrid::components.icons.search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fsearch.blade.php&line=1", "ajax": false, "filename": "search.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.search"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "param_count": null, "params": [], "start": **********.641683, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/enabled-filters.blade.phplivewire-powergrid::components.frameworks.tailwind.header.enabled-filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fenabled-filters.blade.php&line=1", "ajax": false, "filename": "enabled-filters.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.enabled-filters"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "param_count": null, "params": [], "start": **********.642499, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/header/message-soft-deletes.blade.phplivewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fheader%2Fmessage-soft-deletes.blade.php&line=1", "ajax": false, "filename": "message-soft-deletes.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.header.message-soft-deletes"}, {"name": "1x livewire-powergrid::components.table", "param_count": null, "params": [], "start": **********.642964, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table.blade.phplivewire-powergrid::components.table", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable.blade.php&line=1", "ajax": false, "filename": "table.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table"}, {"name": "2x livewire-powergrid::components.table.tr", "param_count": null, "params": [], "start": **********.643789, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table/tr.blade.phplivewire-powergrid::components.table.tr", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable%2Ftr.blade.php&line=1", "ajax": false, "filename": "tr.blade.php", "line": "?"}, "render_count": 2, "name_original": "livewire-powergrid::components.table.tr"}, {"name": "5x livewire-powergrid::components.cols", "param_count": null, "params": [], "start": **********.644502, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/cols.blade.phplivewire-powergrid::components.cols", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fcols.blade.php&line=1", "ajax": false, "filename": "cols.blade.php", "line": "?"}, "render_count": 5, "name_original": "livewire-powergrid::components.cols"}, {"name": "1x __components::f80280cbef3647ab5b62bdedf0ef25a1", "param_count": null, "params": [], "start": **********.652547, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/f80280cbef3647ab5b62bdedf0ef25a1.blade.php__components::f80280cbef3647ab5b62bdedf0ef25a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Ff80280cbef3647ab5b62bdedf0ef25a1.blade.php&line=1", "ajax": false, "filename": "f80280cbef3647ab5b62bdedf0ef25a1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f80280cbef3647ab5b62bdedf0ef25a1"}, {"name": "1x livewire-powergrid::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.653113, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-down.blade.phplivewire-powergrid::components.icons.chevron-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-down.blade.php&line=1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.chevron-down"}, {"name": "3x __components::6da562a044c365ee08fb55b6325b90a1", "param_count": null, "params": [], "start": **********.65455, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/6da562a044c365ee08fb55b6325b90a1.blade.php__components::6da562a044c365ee08fb55b6325b90a1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F6da562a044c365ee08fb55b6325b90a1.blade.php&line=1", "ajax": false, "filename": "6da562a044c365ee08fb55b6325b90a1.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::6da562a044c365ee08fb55b6325b90a1"}, {"name": "3x livewire-powergrid::components.icons.chevron-up-down", "param_count": null, "params": [], "start": **********.655478, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/chevron-up-down.blade.phplivewire-powergrid::components.icons.chevron-up-down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fchevron-up-down.blade.php&line=1", "ajax": false, "filename": "chevron-up-down.blade.php", "line": "?"}, "render_count": 3, "name_original": "livewire-powergrid::components.icons.chevron-up-down"}, {"name": "1x components.custom-loading", "param_count": null, "params": [], "start": **********.659341, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/custom-loading.blade.phpcomponents.custom-loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcustom-loading.blade.php&line=1", "ajax": false, "filename": "custom-loading.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.custom-loading"}, {"name": "10x livewire-powergrid::components.row", "param_count": null, "params": [], "start": **********.660193, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/row.blade.phplivewire-powergrid::components.row", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 10, "name_original": "livewire-powergrid::components.row"}, {"name": "10x livewire-powergrid::components.frameworks.tailwind.toggleable", "param_count": null, "params": [], "start": **********.661368, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/toggleable.blade.phplivewire-powergrid::components.frameworks.tailwind.toggleable", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ftoggleable.blade.php&line=1", "ajax": false, "filename": "toggleable.blade.php", "line": "?"}, "render_count": 10, "name_original": "livewire-powergrid::components.frameworks.tailwind.toggleable"}, {"name": "1x livewire-powergrid::components.table-base", "param_count": null, "params": [], "start": **********.687, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/table-base.blade.phplivewire-powergrid::components.table-base", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ftable-base.blade.php&line=1", "ajax": false, "filename": "table-base.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.table-base"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.footer", "param_count": null, "params": [], "start": **********.688644, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/footer.blade.phplivewire-powergrid::components.frameworks.tailwind.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.footer"}, {"name": "1x livewire-powergrid::components.icons.down", "param_count": null, "params": [], "start": **********.689577, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/icons/down.blade.phplivewire-powergrid::components.icons.down", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Ficons%2Fdown.blade.php&line=1", "ajax": false, "filename": "down.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.icons.down"}, {"name": "1x livewire-powergrid::components.frameworks.tailwind.pagination", "param_count": null, "params": [], "start": **********.690364, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\Providers/../../resources/views/components/frameworks/tailwind/pagination.blade.phplivewire-powergrid::components.frameworks.tailwind.pagination", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fresources%2Fviews%2Fcomponents%2Fframeworks%2Ftailwind%2Fpagination.blade.php&line=1", "ajax": false, "filename": "pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire-powergrid::components.frameworks.tailwind.pagination"}]}, "queries": {"count": 6, "nb_statements": 6, "nb_visible_statements": 6, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.009510000000000001, "accumulated_duration_str": "9.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.424729, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 7.15}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.443595, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 7.15, "width_percent": 9.779}, {"sql": "select count(*) as aggregate from `canned_replies`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/ModelProcessor.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\ModelProcessor.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/ProcessDataSource.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\ProcessDataSource.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 174}, {"index": 20, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 140}], "start": **********.486956, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "DataSourceBase.php:85", "source": {"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FDataSource%2FProcessors%2FDataSourceBase.php&line=85", "ajax": false, "filename": "DataSourceBase.php", "line": "85"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 16.93, "width_percent": 35.436}, {"sql": "select * from `canned_replies` order by `id` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, {"index": 17, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/ModelProcessor.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\ModelProcessor.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/ProcessDataSource.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\ProcessDataSource.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 174}, {"index": 20, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/PowerGridComponent.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\PowerGridComponent.php", "line": 140}], "start": **********.492105, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DataSourceBase.php:85", "source": {"index": 16, "namespace": null, "name": "vendor/power-components/livewire-powergrid/src/DataSource/Processors/DataSourceBase.php", "file": "C:\\laragon\\www\\whats\\vendor\\power-components\\livewire-powergrid\\src\\DataSource\\Processors\\DataSourceBase.php", "line": 85}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FDataSource%2FProcessors%2FDataSourceBase.php&line=85", "ajax": false, "filename": "DataSourceBase.php", "line": "85"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 52.366, "width_percent": 4.837}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.502859, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 57.203, "width_percent": 20.189}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.5064828, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\whats\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 77.392, "width_percent": 22.608}]}, "models": {"data": {"App\\Models\\CannedReply": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FCannedReply.php&line=1", "ajax": false, "filename": "CannedReply.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": {"admin.table.canned-reply-table #kQveuSlJcb6pbklTtmY8": "array:4 [\n  \"data\" => array:44 [\n    \"tableName\" => \"canned-reply-table-qxiqed-table\"\n    \"deferLoading\" => true\n    \"loadingComponent\" => \"components.custom-loading\"\n    \"theme\" => array:17 [\n      \"name\" => \"tailwind\"\n      \"root\" => \"livewire-powergrid::components.frameworks.tailwind\"\n      \"table\" => array:3 [\n        \"layout\" => array:5 [\n          \"base\" => \"p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8\"\n          \"div\" => \"rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n          \"table\" => \"min-w-full dark:!bg-primary-800\"\n          \"container\" => \"-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8\"\n          \"actions\" => \"flex gap-2\"\n        ]\n        \"header\" => array:4 [\n          \"thead\" => \"shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900\"\n          \"tr\" => \"\"\n          \"th\" => \"font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300\"\n          \"thAction\" => \"!font-bold\"\n        ]\n        \"body\" => array:10 [\n          \"tbody\" => \"text-pg-primary-800\"\n          \"tbodyEmpty\" => \"\"\n          \"tr\" => \"border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700\"\n          \"td\" => \"px-3 py-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdEmpty\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200\"\n          \"tdSummarize\" => \"p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2\"\n          \"trSummarize\" => \"\"\n          \"tdFilters\" => \"\"\n          \"trFilters\" => \"\"\n          \"tdActionsContainer\" => \"flex gap-2\"\n        ]\n      ]\n      \"footer\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n        \"footer\" => \"border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600\"\n        \"footer_with_pagination\" => \"md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900\"\n      ]\n      \"cols\" => array:1 [\n        \"div\" => \"select-none flex items-center gap-1\"\n      ]\n      \"editable\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.editable\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"layout\" => array:4 [\n        \"table\" => \"livewire-powergrid::components.frameworks.tailwind.table-base\"\n        \"header\" => \"livewire-powergrid::components.frameworks.tailwind.header\"\n        \"pagination\" => \"livewire-powergrid::components.frameworks.tailwind.pagination\"\n        \"footer\" => \"livewire-powergrid::components.frameworks.tailwind.footer\"\n      ]\n      \"toggleable\" => array:1 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.toggleable\"\n      ]\n      \"checkbox\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900\"\n      ]\n      \"radio\" => array:4 [\n        \"th\" => \"px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider\"\n        \"base\" => \"\"\n        \"label\" => \"flex items-center space-x-3\"\n        \"input\" => \"form-radio rounded-full transition ease-in-out duration-100\"\n      ]\n      \"filterBoolean\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.boolean\"\n        \"base\" => \"min-w-[5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterDatePicker\" => array:3 [\n        \"base\" => \"\"\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.date-picker\"\n        \"input\" => \"flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto\"\n      ]\n      \"filterMultiSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.multi-select\"\n        \"base\" => \"inline-block relative w-full\"\n        \"select\" => \"mt-1\"\n      ]\n      \"filterNumber\" => array:2 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.number\"\n        \"input\" => \"w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6\"\n      ]\n      \"filterSelect\" => array:3 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.select\"\n        \"base\" => \"\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"filterInputText\" => array:4 [\n        \"view\" => \"livewire-powergrid::components.frameworks.tailwind.filters.input-text\"\n        \"base\" => \"min-w-[9.5rem]\"\n        \"select\" => \"appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full\"\n      ]\n      \"searchBox\" => array:3 [\n        \"input\" => \"focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8\"\n        \"iconClose\" => \"text-pg-primary-400 dark:text-pg-primary-200\"\n        \"iconSearch\" => \"text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200\"\n      ]\n    ]\n    \"primaryKey\" => \"id\"\n    \"primaryKeyAlias\" => null\n    \"ignoreTablePrefix\" => true\n    \"setUp\" => array:2 [\n      \"header\" => array:8 [\n        \"name\" => \"header\"\n        \"searchInput\" => true\n        \"toggleColumns\" => true\n        \"softDeletes\" => false\n        \"showMessageSoftDeletes\" => false\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"wireLoading\" => false\n      ]\n      \"footer\" => array:8 [\n        \"name\" => \"footer\"\n        \"perPage\" => 10\n        \"perPageValues\" => array:5 [\n          0 => 10\n          1 => 25\n          2 => 50\n          3 => 100\n          4 => 0\n        ]\n        \"recordCount\" => \"full\"\n        \"pagination\" => null\n        \"includeViewOnTop\" => \"\"\n        \"includeViewOnBottom\" => \"\"\n        \"pageName\" => \"page\"\n      ]\n    ]\n    \"showErrorBag\" => false\n    \"rowIndex\" => true\n    \"readyToLoad\" => true\n    \"columns\" => array:5 [\n      0 => array:25 [\n        \"title\" => \"ID\"\n        \"field\" => \"id\"\n        \"dataField\" => \"id\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      1 => array:25 [\n        \"title\" => \"Title\"\n        \"field\" => \"title\"\n        \"dataField\" => \"title\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      2 => array:25 [\n        \"title\" => \"Description\"\n        \"field\" => \"description\"\n        \"dataField\" => \"description\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      3 => array:25 [\n        \"title\" => \"Public\"\n        \"field\" => \"is_public\"\n        \"dataField\" => \"is_public\"\n        \"placeholder\" => \"\"\n        \"searchable\" => true\n        \"enableSort\" => true\n        \"hidden\" => false\n        \"forceHidden\" => false\n        \"visibleInExport\" => null\n        \"sortable\" => true\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => false\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"flex align-center mt-2\"\n        \"bodyStyle\" => \"\"\n        \"toggleable\" => array:2 [\n          \"enabled\" => true\n          \"default\" => array:2 [\n            0 => \"Public\"\n            1 => \"No\"\n          ]\n        ]\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n      4 => array:25 [\n        \"title\" => \"Action\"\n        \"field\" => \"\"\n        \"dataField\" => \"\"\n        \"placeholder\" => \"\"\n        \"searchable\" => false\n        \"enableSort\" => false\n        \"hidden\" => false\n        \"forceHidden\" => true\n        \"visibleInExport\" => false\n        \"sortable\" => false\n        \"index\" => false\n        \"properties\" => []\n        \"isAction\" => true\n        \"fixedOnResponsive\" => false\n        \"template\" => false\n        \"contentClassField\" => \"\"\n        \"contentClasses\" => []\n        \"headerClass\" => \"\"\n        \"headerStyle\" => \"\"\n        \"bodyClass\" => \"wire:key\"\n        \"bodyStyle\" => \"action_{{ $id }}\"\n        \"toggleable\" => []\n        \"editable\" => []\n        \"filters\" => null\n        \"customContent\" => []\n      ]\n    ]\n    \"headers\" => []\n    \"search\" => \"\"\n    \"currentTable\" => \"canned_replies\"\n    \"total\" => 10\n    \"totalCurrentPage\" => 0\n    \"supportModel\" => true\n    \"paginateRaw\" => false\n    \"measurePerformance\" => false\n    \"checkbox\" => false\n    \"checkboxAll\" => false\n    \"checkboxValues\" => []\n    \"checkboxAttribute\" => \"id\"\n    \"filters\" => []\n    \"filtered\" => []\n    \"enabledFilters\" => []\n    \"select\" => []\n    \"showFilters\" => false\n    \"withoutResourcesActions\" => false\n    \"additionalCacheKey\" => \"\"\n    \"persist\" => []\n    \"persistPrefix\" => \"\"\n    \"radio\" => false\n    \"radioAttribute\" => \"id\"\n    \"selectedRow\" => \"\"\n    \"softDeletes\" => \"\"\n    \"sortField\" => \"id\"\n    \"sortDirection\" => \"asc\"\n    \"multiSort\" => false\n    \"sortArray\" => []\n    \"headerTotalColumn\" => false\n    \"footerTotalColumn\" => false\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"admin.table.canned-reply-table\"\n  \"component\" => \"App\\Livewire\\Admin\\Table\\CannedReplyTable\"\n  \"id\" => \"kQveuSlJcb6pbklTtmY8\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 20, "messages": [{"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1578067315 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578067315\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.511729, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1128518238 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128518238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.513651, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1014244719 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014244719\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.597571, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1892129802 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892129802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.598582, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-50540746 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50540746\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.602691, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-183021096 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183021096\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.60393, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1860633276 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860633276\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.606192, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-196013217 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196013217\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.607144, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-970360598 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-970360598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.608542, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1078322772 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078322772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.609363, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1993066939 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993066939\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.610749, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-445148422 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445148422\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.611608, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-44004772 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44004772\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.613175, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1784785107 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784785107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.614042, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1882987373 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882987373\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.615969, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-32825380 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32825380\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.618841, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2091999415 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091999415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.620741, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-121405506 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121405506\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.622644, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.edit,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-920798598 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.edit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">canned_reply.edit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920798598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.624232, "xdebug_link": null}, {"message": "[\n  ability => canned_reply.delete,\n  target => null,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1331856235 data-indent-pad=\"  \"><span class=sf-dump-note>canned_reply.delete </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">canned_reply.delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1331856235\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.625104, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Admin\\Table\\CannedReplyTable@fetchDatasource<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fpower-components%2Flivewire-powergrid%2Fsrc%2FPowerGridComponent.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/power-components/livewire-powergrid/src/PowerGridComponent.php:79-82</a>", "middleware": "web", "duration": "889ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1095469636 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1095469636\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-169334642 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"12913 characters\">{&quot;data&quot;:{&quot;tableName&quot;:&quot;canned-reply-table-qxiqed-table&quot;,&quot;deferLoading&quot;:true,&quot;loadingComponent&quot;:&quot;components.custom-loading&quot;,&quot;theme&quot;:[{&quot;name&quot;:&quot;tailwind&quot;,&quot;root&quot;:&quot;livewire-powergrid::components.frameworks.tailwind&quot;,&quot;table&quot;:[{&quot;layout&quot;:[{&quot;base&quot;:&quot;p-3 align-middle inline-block min-w-full w-full sm:px-6 lg:px-8&quot;,&quot;div&quot;:&quot;rounded-t-lg relative border-x border-t border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;table&quot;:&quot;min-w-full dark:!bg-primary-800&quot;,&quot;container&quot;:&quot;-my-2 overflow-x-auto sm:-mx-3 lg:-mx-8&quot;,&quot;actions&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;header&quot;:[{&quot;thead&quot;:&quot;shadow-sm rounded-t-lg bg-pg-primary-100 dark:bg-pg-primary-900&quot;,&quot;tr&quot;:&quot;&quot;,&quot;th&quot;:&quot;font-extrabold px-3 py-3 text-left text-xs text-pg-primary-700 tracking-wider whitespace-nowrap dark:text-pg-primary-300&quot;,&quot;thAction&quot;:&quot;!font-bold&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;body&quot;:[{&quot;tbody&quot;:&quot;text-pg-primary-800&quot;,&quot;tbodyEmpty&quot;:&quot;&quot;,&quot;tr&quot;:&quot;border-b border-pg-primary-100 dark:border-pg-primary-600 hover:bg-pg-primary-50 dark:bg-pg-primary-800 dark:hover:bg-pg-primary-700&quot;,&quot;td&quot;:&quot;px-3 py-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdEmpty&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200&quot;,&quot;tdSummarize&quot;:&quot;p-2 whitespace-nowrap dark:text-pg-primary-200 text-sm text-pg-primary-600 text-right space-y-2&quot;,&quot;trSummarize&quot;:&quot;&quot;,&quot;tdFilters&quot;:&quot;&quot;,&quot;trFilters&quot;:&quot;&quot;,&quot;tdActionsContainer&quot;:&quot;flex gap-2&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;footer&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-4 pr-7 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;,&quot;footer&quot;:&quot;border-x border-b rounded-b-lg border-b border-pg-primary-200 dark:bg-pg-primary-700 dark:border-pg-primary-600&quot;,&quot;footer_with_pagination&quot;:&quot;md:flex md:flex-row w-full items-center py-3 bg-white overflow-y-auto pl-2 pr-2 relative dark:bg-pg-primary-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;cols&quot;:[{&quot;div&quot;:&quot;select-none flex items-center gap-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.editable&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;layout&quot;:[{&quot;table&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.table-base&quot;,&quot;header&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.header&quot;,&quot;pagination&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.pagination&quot;,&quot;footer&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.footer&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;toggleable&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.toggleable&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;checkbox&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-checkbox dark:border-dark-600 border-1 dark:bg-dark-800 rounded border-gray-300 bg-white transition duration-100 ease-in-out h-4 w-4 text-primary-500 focus:ring-primary-500 dark:ring-offset-dark-900&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;radio&quot;:[{&quot;th&quot;:&quot;px-6 py-3 text-left text-xs font-medium text-pg-primary-500 tracking-wider&quot;,&quot;base&quot;:&quot;&quot;,&quot;label&quot;:&quot;flex items-center space-x-3&quot;,&quot;input&quot;:&quot;form-radio rounded-full transition ease-in-out duration-100&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterBoolean&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.boolean&quot;,&quot;base&quot;:&quot;min-w-[5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterDatePicker&quot;:[{&quot;base&quot;:&quot;&quot;,&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.date-picker&quot;,&quot;input&quot;:&quot;flatpickr flatpickr-input focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-auto&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterMultiSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.multi-select&quot;,&quot;base&quot;:&quot;inline-block relative w-full&quot;,&quot;select&quot;:&quot;mt-1&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterNumber&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.number&quot;,&quot;input&quot;:&quot;w-full min-w-[5rem] block focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 pl-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterSelect&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.select&quot;,&quot;base&quot;:&quot;&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;filterInputText&quot;:[{&quot;view&quot;:&quot;livewire-powergrid::components.frameworks.tailwind.filters.input-text&quot;,&quot;base&quot;:&quot;min-w-[9.5rem]&quot;,&quot;select&quot;:&quot;appearance-none !bg-none focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;,&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full&quot;},{&quot;s&quot;:&quot;arr&quot;}],&quot;searchBox&quot;:[{&quot;input&quot;:&quot;focus:ring-primary-600 focus-within:focus:ring-primary-600 focus-within:ring-primary-600 dark:focus-within:ring-primary-600 flex items-center rounded-md ring-1 transition focus-within:ring-2 dark:ring-pg-primary-600 dark:text-pg-primary-300 text-gray-600 ring-gray-300 dark:bg-pg-primary-800 bg-white dark:placeholder-pg-primary-400 w-full rounded-md border-0 bg-transparent py-1.5 px-2 ring-0 placeholder:text-gray-400 focus:outline-none sm:text-sm sm:leading-6 w-full pl-8&quot;,&quot;iconClose&quot;:&quot;text-pg-primary-400 dark:text-pg-primary-200&quot;,&quot;iconSearch&quot;:&quot;text-pg-primary-300 mr-2 w-5 h-5 dark:text-pg-primary-200&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;primaryKey&quot;:&quot;id&quot;,&quot;primaryKeyAlias&quot;:null,&quot;ignoreTablePrefix&quot;:true,&quot;setUp&quot;:[{&quot;header&quot;:[{&quot;name&quot;:&quot;header&quot;,&quot;searchInput&quot;:true,&quot;toggleColumns&quot;:true,&quot;softDeletes&quot;:false,&quot;showMessageSoftDeletes&quot;:false,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;wireLoading&quot;:false},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Header&quot;,&quot;s&quot;:&quot;wrbl&quot;}],&quot;footer&quot;:[{&quot;name&quot;:&quot;footer&quot;,&quot;perPage&quot;:10,&quot;perPageValues&quot;:[[10,25,50,100,0],{&quot;s&quot;:&quot;arr&quot;}],&quot;recordCount&quot;:&quot;full&quot;,&quot;pagination&quot;:null,&quot;includeViewOnTop&quot;:&quot;&quot;,&quot;includeViewOnBottom&quot;:&quot;&quot;,&quot;pageName&quot;:&quot;page&quot;},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Components\\\\SetUp\\\\Footer&quot;,&quot;s&quot;:&quot;wrbl&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;showErrorBag&quot;:false,&quot;rowIndex&quot;:true,&quot;readyToLoad&quot;:false,&quot;columns&quot;:[[[{&quot;title&quot;:&quot;ID&quot;,&quot;field&quot;:&quot;id&quot;,&quot;dataField&quot;:&quot;id&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Title&quot;,&quot;field&quot;:&quot;title&quot;,&quot;dataField&quot;:&quot;title&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Description&quot;,&quot;field&quot;:&quot;description&quot;,&quot;dataField&quot;:&quot;description&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Public&quot;,&quot;field&quot;:&quot;is_public&quot;,&quot;dataField&quot;:&quot;is_public&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:true,&quot;enableSort&quot;:true,&quot;hidden&quot;:false,&quot;forceHidden&quot;:false,&quot;visibleInExport&quot;:null,&quot;sortable&quot;:true,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:false,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;flex align-center mt-2&quot;,&quot;bodyStyle&quot;:&quot;&quot;,&quot;toggleable&quot;:[{&quot;enabled&quot;:true,&quot;default&quot;:[[&quot;Public&quot;,&quot;No&quot;],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}],[{&quot;title&quot;:&quot;Action&quot;,&quot;field&quot;:&quot;&quot;,&quot;dataField&quot;:&quot;&quot;,&quot;placeholder&quot;:&quot;&quot;,&quot;searchable&quot;:false,&quot;enableSort&quot;:false,&quot;hidden&quot;:false,&quot;forceHidden&quot;:true,&quot;visibleInExport&quot;:false,&quot;sortable&quot;:false,&quot;index&quot;:false,&quot;properties&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;isAction&quot;:true,&quot;fixedOnResponsive&quot;:false,&quot;template&quot;:false,&quot;contentClassField&quot;:&quot;&quot;,&quot;contentClasses&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerClass&quot;:&quot;&quot;,&quot;headerStyle&quot;:&quot;&quot;,&quot;bodyClass&quot;:&quot;wire:key&quot;,&quot;bodyStyle&quot;:&quot;action_{{ $id }}&quot;,&quot;toggleable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;editable&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filters&quot;:null,&quot;customContent&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},{&quot;class&quot;:&quot;PowerComponents\\\\LivewirePowerGrid\\\\Column&quot;,&quot;s&quot;:&quot;wrbl&quot;}]],{&quot;s&quot;:&quot;arr&quot;}],&quot;headers&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;search&quot;:&quot;&quot;,&quot;currentTable&quot;:&quot;&quot;,&quot;total&quot;:0,&quot;totalCurrentPage&quot;:0,&quot;supportModel&quot;:true,&quot;paginateRaw&quot;:false,&quot;measurePerformance&quot;:false,&quot;checkbox&quot;:false,&quot;checkboxAll&quot;:false,&quot;checkboxValues&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;checkboxAttribute&quot;:&quot;id&quot;,&quot;filters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;filtered&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;enabledFilters&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;select&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;showFilters&quot;:false,&quot;withoutResourcesActions&quot;:false,&quot;additionalCacheKey&quot;:&quot;&quot;,&quot;persist&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;persistPrefix&quot;:&quot;&quot;,&quot;radio&quot;:false,&quot;radioAttribute&quot;:&quot;id&quot;,&quot;selectedRow&quot;:&quot;&quot;,&quot;softDeletes&quot;:&quot;&quot;,&quot;sortField&quot;:&quot;id&quot;,&quot;sortDirection&quot;:&quot;asc&quot;,&quot;multiSort&quot;:false,&quot;sortArray&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;headerTotalColumn&quot;:false,&quot;footerTotalColumn&quot;:false,&quot;paginators&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;kQveuSlJcb6pbklTtmY8&quot;,&quot;name&quot;:&quot;admin.table.canned-reply-table&quot;,&quot;path&quot;:&quot;admin\\/canned-reply&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;f357e2406debc38c9848dec070bce6f4ffcea455eb401a4e260e936daf2b50cc&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">fetchDatasource</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-169334642\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-890775021 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">14226</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/canned-reply</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImFIN2dlVm0yRlVWNGUvV0NLdG44RFE9PSIsInZhbHVlIjoiTFpMaE84QTlIU1VnTVpIaDFCYjVxSmVxZmpjRkUxUUovcDhpQzVzbUxweEljYmlkRFB1NncyY2tWRkFNZXpWei9XVUtKQ1RFV1BZQlFVMXVDRVBDelpobkl5bzQySkphSWt0aW54L1FBTDd1bVNTa1UyaHFqSmVSN2Q4dCt1ajEiLCJtYWMiOiI3MGMwM2UyMWI5ODcyNTVmZjVlNzJjNmRjMDQ1NDM5OGQ2ZDMxNGRlYjg4MWNjMjlhNTlmYjllMmRjYmE1MzEwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6ImVqNGxLa0ZOZWtiVGEyUkEyQjc0dWc9PSIsInZhbHVlIjoiTkRaRkNoZ2x5MmlhL09pR1RmYkdOTExvUUlyYWVycWZhQmN5ZTUzSHB6L2hTelVOenZldkZGSmZPVnBwbjYzZFBDMFR6QTVDWm9oU3pldXJXRS9wZGwrYnNDRm9vMmNXQW55Z2l3R3o2UlZIY3VKWWowVnJvcldUMVZyRE12ZWkiLCJtYWMiOiIwNjEzZDE3Yjc0OGUzY2YwNDI4MTJlNTQzOTZkMDMwNTEyYjY0YWI1ZTliYjQ3NjBiNTYzNThkYjViMmI0NGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890775021\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1179375415 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179375415\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1883987503 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:44:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1883987503\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-485866023 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/canned-reply</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485866023\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}