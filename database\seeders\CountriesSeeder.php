<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class CountriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting Countries Seeder...');

        $jsonPath = base_path('platform/packages/corbital/installer/countries.json');

        if (!File::exists($jsonPath)) {
            $this->command->error('Countries JSON file not found!');
            return;
        }

        $countries = json_decode(File::get($jsonPath), true);

        if (empty($countries)) {
            $this->command->error('No countries data found in JSON file!');
            return;
        }

        DB::beginTransaction();

        try {
            // Clear existing countries (use delete instead of truncate due to foreign key constraints)
            DB::table('countries')->delete();

            $chunks = array_chunk($countries, 100);
            $bar = $this->command->getOutput()->createProgressBar(count($chunks));

            foreach ($chunks as $chunk) {
                $insertData = [];
                
                foreach ($chunk as $country) {
                    $insertData[] = [
                        'id' => $country['id'],
                        'iso2' => $country['iso2'],
                        'short_name' => $country['short_name'],
                        'long_name' => $country['long_name'],
                        'iso3' => $country['iso3'],
                        'numcode' => $country['numcode'],
                        'un_member' => $country['un_member'],
                        'calling_code' => $country['calling_code'],
                        'cctld' => $country['cctld'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                DB::table('countries')->insert($insertData);
                $bar->advance();
            }

            DB::commit();

            $bar->finish();
            $this->command->info("\nSuccessfully seeded " . count($countries) . " countries!");

        } catch (\Exception $e) {
            DB::rollBack();
            $this->command->error("\nError seeding countries: " . $e->getMessage());
        }
    }
}
