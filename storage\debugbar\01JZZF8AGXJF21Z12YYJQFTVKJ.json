{"__meta": {"id": "01JZZF8AGXJF21Z12YYJQFTVKJ", "datetime": "2025-07-13 02:05:03", "utime": **********.902286, "method": "GET", "uri": "/install", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.21624, "end": **********.902298, "duration": 0.6860580444335938, "duration_str": "686ms", "measures": [{"label": "Booting", "start": **********.21624, "relative_start": 0, "end": **********.863024, "relative_end": **********.863024, "duration": 0.****************, "duration_str": "647ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.863034, "relative_start": 0.****************, "end": **********.9023, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "39.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.881067, "relative_start": 0.****************, "end": **********.883437, "relative_end": **********.883437, "duration": 0.0023698806762695312, "duration_str": "2.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.895701, "relative_start": 0.****************, "end": **********.900235, "relative_end": **********.900235, "duration": 0.004534006118774414, "duration_str": "4.53ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: installer::installation.welcome", "start": **********.897524, "relative_start": 0.****************, "end": **********.897524, "relative_end": **********.897524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: installer::installation.layout", "start": **********.899404, "relative_start": 0.***************, "end": **********.899404, "relative_end": **********.899404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 31602112, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Wallis", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "installer::installation.welcome", "param_count": null, "params": [], "start": **********.897484, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Providers/../resources/views/installation/welcome.blade.phpinstaller::installation.welcome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2Fresources%2Fviews%2Finstallation%2Fwelcome.blade.php&line=1", "ajax": false, "filename": "welcome.blade.php", "line": "?"}}, {"name": "installer::installation.layout", "param_count": null, "params": [], "start": **********.899371, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\platform\\packages\\corbital\\installer\\src\\Providers/../resources/views/installation/layout.blade.phpinstaller::installation.layout", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2Fresources%2Fviews%2Finstallation%2Flayout.blade.php&line=1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00184, "accumulated_duration_str": "1.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9gnFUveucibV5icVeYkMzp3ptWmipfWPCsLXoPJF' limit 1", "type": "query", "params": [], "bindings": ["9gnFUveucibV5icVeYkMzp3ptWmipfWPCsLXoPJF"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.8867269, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/install", "action_name": "install.index", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@index", "uri": "GET install", "controller": "Corbital\\Installer\\Http\\Controllers\\InstallController@index<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=31\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "install", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fplatform%2Fpackages%2Fcorbital%2Finstaller%2Fsrc%2FHttp%2FControllers%2FInstallController.php&line=31\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/corbital/installer/src/Http/Controllers/InstallController.php:31-34</a>", "middleware": "web, Corbital\\Installer\\Http\\Middleware\\CanInstall", "duration": "686ms", "peak_memory": "38MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1338126271 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1338126271\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1041387490 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1041387490\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">script</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/install</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Im5TQS9TZEVZRmFBNEk2Y3lMNDJBVUE9PSIsInZhbHVlIjoicEEzTEVJRlBTaFhyUGpRUEREM1daRTFqZGk4d3hzRGJmOGhBOGQrSWNub3MxSkwwcUdGUFh5ZnhpK29KdHpGS1BpZjV2d25ZT1pmdXJwL3hGSEdMQ21DTlEzWktiZkRCbkJ3bGs3bm16WDk5cUQvak5GUDNwUDd0SFRFZU9kbnciLCJtYWMiOiI3MTk1Mjg4NmZjYjY5MzFlNDRhMWE3ZjU3NDM0ZmFhMTQ1Nzk3YjA4OWEwYjEwZTgyOTZlZTdjMjllZDAxMzEwIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjlmVTE5T2xnalBpbnNVdk5Ebm9xR0E9PSIsInZhbHVlIjoiN05vYW5iRGxHbWVURDdqZkd1REVqaUlsSVJUWDU4eE5VTExXbzVTY01zYjFxMExyYmFGem1FVTNiRGJJMWsrcEkrcElCbkx1QzVXNjFzaXdyc09mbWoxbHZXNU96QjQ3NXhERE5hYnBIbWtYK0JBNWc5SXJiQ0R0KzhJcTAxc2siLCJtYWMiOiJjNjFiNmQ3MWNkZjRkNjlkNzdkZTczYjgzODUzYmRlMDlkM2ZjYzYxMmVkZWViYmI5MjgzZGY0MzFmNTBlNTI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1748547218 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEnadNAZGonjfMy1yxQ15NHa3pBdJ4Vc7MHJA2CB</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9gnFUveucibV5icVeYkMzp3ptWmipfWPCsLXoPJF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748547218\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1027522137 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:05:03 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027522137\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1767296700 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">jEnadNAZGonjfMy1yxQ15NHa3pBdJ4Vc7MHJA2CB</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"29 characters\">http://127.0.0.1:8000/install</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767296700\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/install", "action_name": "install.index", "controller_action": "Corbital\\Installer\\Http\\Controllers\\InstallController@index"}, "badge": null}}