{"__meta": {"id": "01JZZH7DY1C1ZF9G4KDB5HBX90", "datetime": "2025-07-12 04:39:31", "utime": **********.777939, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752331170.771407, "end": **********.77797, "duration": 1.0065631866455078, "duration_str": "1.01s", "measures": [{"label": "Booting", "start": 1752331170.771407, "relative_start": 0, "end": **********.610572, "relative_end": **********.610572, "duration": 0.***************, "duration_str": "839ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.610593, "relative_start": 0.****************, "end": **********.777971, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "167ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.629851, "relative_start": 0.****************, "end": **********.633479, "relative_end": **********.633479, "duration": 0.0036280155181884766, "duration_str": "3.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.admin.user.user-creator", "start": **********.677679, "relative_start": 0.****************, "end": **********.677679, "relative_end": **********.677679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c3e78b549e314c45350a1f2014a728f1", "start": **********.684949, "relative_start": 0.****************, "end": **********.684949, "relative_end": **********.684949, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.689707, "relative_start": 0.************9512, "end": **********.689707, "relative_end": **********.689707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7b30154958fd08f5dfb04c6347a46789", "start": **********.691113, "relative_start": 0.9197061061859131, "end": **********.691113, "relative_end": **********.691113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.693724, "relative_start": 0.9223170280456543, "end": **********.693724, "relative_end": **********.693724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.694335, "relative_start": 0.9229280948638916, "end": **********.694335, "relative_end": **********.694335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.695988, "relative_start": 0.9245810508728027, "end": **********.695988, "relative_end": **********.695988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.69733, "relative_start": 0.9259231090545654, "end": **********.69733, "relative_end": **********.69733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.697915, "relative_start": 0.9265081882476807, "end": **********.697915, "relative_end": **********.697915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.698822, "relative_start": 0.927415132522583, "end": **********.698822, "relative_end": **********.698822, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.69993, "relative_start": 0.928523063659668, "end": **********.69993, "relative_end": **********.69993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.700217, "relative_start": 0.9288101196289062, "end": **********.700217, "relative_end": **********.700217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.700925, "relative_start": 0.9295182228088379, "end": **********.700925, "relative_end": **********.700925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ee090b29be96ce0a49e5dbcd11e0496c", "start": **********.701889, "relative_start": 0.9304821491241455, "end": **********.701889, "relative_end": **********.701889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.705364, "relative_start": 0.9339570999145508, "end": **********.705364, "relative_end": **********.705364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.7057, "relative_start": 0.9342930316925049, "end": **********.7057, "relative_end": **********.7057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.706494, "relative_start": 0.9350872039794922, "end": **********.706494, "relative_end": **********.706494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.706861, "relative_start": 0.9354541301727295, "end": **********.706861, "relative_end": **********.706861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.707192, "relative_start": 0.9357850551605225, "end": **********.707192, "relative_end": **********.707192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.70797, "relative_start": 0.9365630149841309, "end": **********.70797, "relative_end": **********.70797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.712174, "relative_start": 0.9407670497894287, "end": **********.712174, "relative_end": **********.712174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.712725, "relative_start": 0.9413180351257324, "end": **********.712725, "relative_end": **********.712725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.713802, "relative_start": 0.9423952102661133, "end": **********.713802, "relative_end": **********.713802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e6fff3659b0ed5c1aa616103d2d8c906", "start": **********.714909, "relative_start": 0.9435021877288818, "end": **********.714909, "relative_end": **********.714909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.71806, "relative_start": 0.9466531276702881, "end": **********.71806, "relative_end": **********.71806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.718425, "relative_start": 0.9470181465148926, "end": **********.718425, "relative_end": **********.718425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.719538, "relative_start": 0.9481310844421387, "end": **********.719538, "relative_end": **********.719538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e6fff3659b0ed5c1aa616103d2d8c906", "start": **********.719919, "relative_start": 0.948512077331543, "end": **********.719919, "relative_end": **********.719919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input", "start": **********.721138, "relative_start": 0.9497311115264893, "end": **********.721138, "relative_end": **********.721138, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.721489, "relative_start": 0.9500820636749268, "end": **********.721489, "relative_end": **********.721489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.722325, "relative_start": 0.9509181976318359, "end": **********.722325, "relative_end": **********.722325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::39f3dc700baf0170584b79c8d561970f", "start": **********.723928, "relative_start": 0.9525210857391357, "end": **********.723928, "relative_end": **********.723928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53ebc1ebb35fa01a8cd72e001f23ef14", "start": **********.724905, "relative_start": 0.953498125076294, "end": **********.724905, "relative_end": **********.724905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.726197, "relative_start": 0.9547901153564453, "end": **********.726197, "relative_end": **********.726197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::39f3dc700baf0170584b79c8d561970f", "start": **********.727416, "relative_start": 0.9560091495513916, "end": **********.727416, "relative_end": **********.727416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53ebc1ebb35fa01a8cd72e001f23ef14", "start": **********.727799, "relative_start": 0.9563920497894287, "end": **********.727799, "relative_end": **********.727799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.7289, "relative_start": 0.9574930667877197, "end": **********.7289, "relative_end": **********.7289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cd7e41c6e607d6a89d50d4ad2a98abd8", "start": **********.730157, "relative_start": 0.9587500095367432, "end": **********.730157, "relative_end": **********.730157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.733456, "relative_start": 0.9620490074157715, "end": **********.733456, "relative_end": **********.733456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::39f3dc700baf0170584b79c8d561970f", "start": **********.734346, "relative_start": 0.9629390239715576, "end": **********.734346, "relative_end": **********.734346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::53ebc1ebb35fa01a8cd72e001f23ef14", "start": **********.734712, "relative_start": 0.9633049964904785, "end": **********.734712, "relative_end": **********.734712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.label", "start": **********.736063, "relative_start": 0.9646561145782471, "end": **********.736063, "relative_end": **********.736063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.select", "start": **********.736776, "relative_start": 0.9653692245483398, "end": **********.736776, "relative_end": **********.736776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.input-error", "start": **********.737067, "relative_start": 0.9656600952148438, "end": **********.737067, "relative_end": **********.737067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c6d2b0abe301d90865d3a9363cc05318", "start": **********.738163, "relative_start": 0.9667561054229736, "end": **********.738163, "relative_end": **********.738163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97dd5d9098ff5fff74d84335c08804fe", "start": **********.741254, "relative_start": 0.9698472023010254, "end": **********.741254, "relative_end": **********.741254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.751146, "relative_start": 0.9797391891479492, "end": **********.751146, "relative_end": **********.751146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.753368, "relative_start": 0.9819610118865967, "end": **********.753368, "relative_end": **********.753368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.753655, "relative_start": 0.982248067855835, "end": **********.753655, "relative_end": **********.753655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.75391, "relative_start": 0.9825031757354736, "end": **********.75391, "relative_end": **********.75391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.75416, "relative_start": 0.9827530384063721, "end": **********.75416, "relative_end": **********.75416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.754415, "relative_start": 0.9830081462860107, "end": **********.754415, "relative_end": **********.754415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.754667, "relative_start": 0.9832601547241211, "end": **********.754667, "relative_end": **********.754667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.754903, "relative_start": 0.9834961891174316, "end": **********.754903, "relative_end": **********.754903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.755156, "relative_start": 0.9837491512298584, "end": **********.755156, "relative_end": **********.755156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.755395, "relative_start": 0.9839880466461182, "end": **********.755395, "relative_end": **********.755395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.755645, "relative_start": 0.9842381477355957, "end": **********.755645, "relative_end": **********.755645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.755934, "relative_start": 0.9845271110534668, "end": **********.755934, "relative_end": **********.755934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.756221, "relative_start": 0.9848141670227051, "end": **********.756221, "relative_end": **********.756221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.75652, "relative_start": 0.9851131439208984, "end": **********.75652, "relative_end": **********.75652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.756972, "relative_start": 0.985565185546875, "end": **********.756972, "relative_end": **********.756972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.757372, "relative_start": 0.9859650135040283, "end": **********.757372, "relative_end": **********.757372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.75773, "relative_start": 0.9863231182098389, "end": **********.75773, "relative_end": **********.75773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97693b359951e7d64261ebfaee711ce1", "start": **********.758044, "relative_start": 0.9866371154785156, "end": **********.758044, "relative_end": **********.758044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ef0b18846b6665ccb08f54bad8c6955b", "start": **********.759254, "relative_start": 0.987847089767456, "end": **********.759254, "relative_end": **********.759254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.card", "start": **********.76295, "relative_start": 0.9915430545806885, "end": **********.76295, "relative_end": **********.76295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.secondary", "start": **********.763948, "relative_start": 0.9925410747528076, "end": **********.763948, "relative_end": **********.763948, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button", "start": **********.764587, "relative_start": 0.9931800365447998, "end": **********.764587, "relative_end": **********.764587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.button.loading-button", "start": **********.766053, "relative_start": 0.9946460723876953, "end": **********.766053, "relative_end": **********.766053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::83f96136a4af4da91c50b6760ff72460", "start": **********.767509, "relative_start": 0.9961020946502686, "end": **********.767509, "relative_end": **********.767509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.774492, "relative_start": 1.0030851364135742, "end": **********.775738, "relative_end": **********.775738, "duration": 0.0012459754943847656, "duration_str": "1.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 37560040, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.28", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Pacific/Tahiti", "Locale": "en"}}, "views": {"count": 70, "nb_templates": 70, "templates": [{"name": "1x livewire.admin.user.user-creator", "param_count": null, "params": [], "start": **********.67764, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/livewire/admin/user/user-creator.blade.phplivewire.admin.user.user-creator", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Flivewire%2Fadmin%2Fuser%2Fuser-creator.blade.php&line=1", "ajax": false, "filename": "user-creator.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.admin.user.user-creator"}, {"name": "1x __components::c3e78b549e314c45350a1f2014a728f1", "param_count": null, "params": [], "start": **********.684914, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c3e78b549e314c45350a1f2014a728f1.blade.php__components::c3e78b549e314c45350a1f2014a728f1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc3e78b549e314c45350a1f2014a728f1.blade.php&line=1", "ajax": false, "filename": "c3e78b549e314c45350a1f2014a728f1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c3e78b549e314c45350a1f2014a728f1"}, {"name": "12x components.label", "param_count": null, "params": [], "start": **********.689663, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/label.blade.phpcomponents.label", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 12, "name_original": "components.label"}, {"name": "1x __components::7b30154958fd08f5dfb04c6347a46789", "param_count": null, "params": [], "start": **********.69108, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/7b30154958fd08f5dfb04c6347a46789.blade.php__components::7b30154958fd08f5dfb04c6347a46789", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F7b30154958fd08f5dfb04c6347a46789.blade.php&line=1", "ajax": false, "filename": "7b30154958fd08f5dfb04c6347a46789.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7b30154958fd08f5dfb04c6347a46789"}, {"name": "2x components.button.secondary", "param_count": null, "params": [], "start": **********.693689, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/secondary.blade.phpcomponents.button.secondary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Fsecondary.blade.php&line=1", "ajax": false, "filename": "secondary.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.button.secondary"}, {"name": "2x components.button", "param_count": null, "params": [], "start": **********.694303, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button.blade.phpcomponents.button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.button"}, {"name": "6x components.input", "param_count": null, "params": [], "start": **********.697294, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input.blade.phpcomponents.input", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput.blade.php&line=1", "ajax": false, "filename": "input.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.input"}, {"name": "8x components.input-error", "param_count": null, "params": [], "start": **********.697876, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/input-error.blade.phpcomponents.input-error", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Finput-error.blade.php&line=1", "ajax": false, "filename": "input-error.blade.php", "line": "?"}, "render_count": 8, "name_original": "components.input-error"}, {"name": "1x __components::ee090b29be96ce0a49e5dbcd11e0496c", "param_count": null, "params": [], "start": **********.701855, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ee090b29be96ce0a49e5dbcd11e0496c.blade.php__components::ee090b29be96ce0a49e5dbcd11e0496c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fee090b29be96ce0a49e5dbcd11e0496c.blade.php&line=1", "ajax": false, "filename": "ee090b29be96ce0a49e5dbcd11e0496c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ee090b29be96ce0a49e5dbcd11e0496c"}, {"name": "2x components.select", "param_count": null, "params": [], "start": **********.712137, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/select.blade.phpcomponents.select", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fselect.blade.php&line=1", "ajax": false, "filename": "select.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.select"}, {"name": "2x __components::e6fff3659b0ed5c1aa616103d2d8c906", "param_count": null, "params": [], "start": **********.714876, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/e6fff3659b0ed5c1aa616103d2d8c906.blade.php__components::e6fff3659b0ed5c1aa616103d2d8c906", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fe6fff3659b0ed5c1aa616103d2d8c906.blade.php&line=1", "ajax": false, "filename": "e6fff3659b0ed5c1aa616103d2d8c906.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::e6fff3659b0ed5c1aa616103d2d8c906"}, {"name": "3x __components::39f3dc700baf0170584b79c8d561970f", "param_count": null, "params": [], "start": **********.723895, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/39f3dc700baf0170584b79c8d561970f.blade.php__components::39f3dc700baf0170584b79c8d561970f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F39f3dc700baf0170584b79c8d561970f.blade.php&line=1", "ajax": false, "filename": "39f3dc700baf0170584b79c8d561970f.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::39f3dc700baf0170584b79c8d561970f"}, {"name": "3x __components::53ebc1ebb35fa01a8cd72e001f23ef14", "param_count": null, "params": [], "start": **********.724871, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/53ebc1ebb35fa01a8cd72e001f23ef14.blade.php__components::53ebc1ebb35fa01a8cd72e001f23ef14", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F53ebc1ebb35fa01a8cd72e001f23ef14.blade.php&line=1", "ajax": false, "filename": "53ebc1ebb35fa01a8cd72e001f23ef14.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::53ebc1ebb35fa01a8cd72e001f23ef14"}, {"name": "2x components.card", "param_count": null, "params": [], "start": **********.728863, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/card.blade.phpcomponents.card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fcard.blade.php&line=1", "ajax": false, "filename": "card.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.card"}, {"name": "1x __components::cd7e41c6e607d6a89d50d4ad2a98abd8", "param_count": null, "params": [], "start": **********.730124, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/cd7e41c6e607d6a89d50d4ad2a98abd8.blade.php__components::cd7e41c6e607d6a89d50d4ad2a98abd8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fcd7e41c6e607d6a89d50d4ad2a98abd8.blade.php&line=1", "ajax": false, "filename": "cd7e41c6e607d6a89d50d4ad2a98abd8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cd7e41c6e607d6a89d50d4ad2a98abd8"}, {"name": "1x __components::c6d2b0abe301d90865d3a9363cc05318", "param_count": null, "params": [], "start": **********.738131, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/c6d2b0abe301d90865d3a9363cc05318.blade.php__components::c6d2b0abe301d90865d3a9363cc05318", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fc6d2b0abe301d90865d3a9363cc05318.blade.php&line=1", "ajax": false, "filename": "c6d2b0abe301d90865d3a9363cc05318.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c6d2b0abe301d90865d3a9363cc05318"}, {"name": "1x __components::97dd5d9098ff5fff74d84335c08804fe", "param_count": null, "params": [], "start": **********.741218, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/97dd5d9098ff5fff74d84335c08804fe.blade.php__components::97dd5d9098ff5fff74d84335c08804fe", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F97dd5d9098ff5fff74d84335c08804fe.blade.php&line=1", "ajax": false, "filename": "97dd5d9098ff5fff74d84335c08804fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::97dd5d9098ff5fff74d84335c08804fe"}, {"name": "18x __components::97693b359951e7d64261ebfaee711ce1", "param_count": null, "params": [], "start": **********.75111, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/97693b359951e7d64261ebfaee711ce1.blade.php__components::97693b359951e7d64261ebfaee711ce1", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F97693b359951e7d64261ebfaee711ce1.blade.php&line=1", "ajax": false, "filename": "97693b359951e7d64261ebfaee711ce1.blade.php", "line": "?"}, "render_count": 18, "name_original": "__components::97693b359951e7d64261ebfaee711ce1"}, {"name": "1x __components::ef0b18846b6665ccb08f54bad8c6955b", "param_count": null, "params": [], "start": **********.759222, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/ef0b18846b6665ccb08f54bad8c6955b.blade.php__components::ef0b18846b6665ccb08f54bad8c6955b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2Fef0b18846b6665ccb08f54bad8c6955b.blade.php&line=1", "ajax": false, "filename": "ef0b18846b6665ccb08f54bad8c6955b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ef0b18846b6665ccb08f54bad8c6955b"}, {"name": "1x components.button.loading-button", "param_count": null, "params": [], "start": **********.766003, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\resources\\views/components/button/loading-button.blade.phpcomponents.button.loading-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fresources%2Fviews%2Fcomponents%2Fbutton%2Floading-button.blade.php&line=1", "ajax": false, "filename": "loading-button.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.button.loading-button"}, {"name": "1x __components::83f96136a4af4da91c50b6760ff72460", "param_count": null, "params": [], "start": **********.767472, "type": "blade", "hash": "bladeC:\\laragon\\www\\whats\\storage\\framework\\views/83f96136a4af4da91c50b6760ff72460.blade.php__components::83f96136a4af4da91c50b6760ff72460", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fstorage%2Fframework%2Fviews%2F83f96136a4af4da91c50b6760ff72460.blade.php&line=1", "ajax": false, "filename": "83f96136a4af4da91c50b6760ff72460.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::83f96136a4af4da91c50b6760ff72460"}]}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00317, "accumulated_duration_str": "3.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q' limit 1", "type": "query", "params": [], "bindings": ["vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.636528, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 0, "width_percent": 19.874}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.655143, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 19.874, "width_percent": 19.874}, {"sql": "select `code`, `name` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\whats\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.7092152, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "GeneralHelper.php:376", "source": {"index": 15, "namespace": null, "name": "app/Helpers/GeneralHelper.php", "file": "C:\\laragon\\www\\whats\\app\\Helpers\\GeneralHelper.php", "line": 376}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FHelpers%2FGeneralHelper.php&line=376", "ajax": false, "filename": "GeneralHelper.php", "line": "376"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 39.748, "width_percent": 15.142}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/Admin/User/UserCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\User\\UserCreator.php", "line": 236}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\laragon\\www\\whats\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.744966, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "UserCreator.php:236", "source": {"index": 16, "namespace": null, "name": "app/Livewire/Admin/User/UserCreator.php", "file": "C:\\laragon\\www\\whats\\app\\Livewire\\Admin\\User\\UserCreator.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FLivewire%2FAdmin%2FUser%2FUserCreator.php&line=236", "ajax": false, "filename": "UserCreator.php", "line": "236"}, "connection": "whatsmark_non_saas", "explain": null, "start_percent": 54.89, "width_percent": 45.11}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 60, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 62, "is_counter": true}, "livewire": {"data": {"admin.user.user-creator #49KmJxfQssfA4Nh9To4P": "array:4 [\n  \"data\" => array:14 [\n    \"id\" => \"49KmJxfQssfA4Nh9To4P\"\n    \"user\" => App\\Models\\User {#2023\n      #connection: null\n      #table: null\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: false\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:6 [\n        \"firstname\" => null\n        \"lastname\" => null\n        \"email\" => null\n        \"phone\" => null\n        \"default_language\" => null\n        \"profile_image_url\" => null\n      ]\n      #original: []\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"password\" => \"hashed\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:10 [\n        0 => \"firstname\"\n        1 => \"lastname\"\n        2 => \"phone\"\n        3 => \"default_language\"\n        4 => \"profile_image_url\"\n        5 => \"email\"\n        6 => \"password\"\n        7 => \"role_id\"\n        8 => \"is_admin\"\n        9 => \"email_verified_at\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #authPasswordName: \"password\"\n      #rememberTokenName: \"remember_token\"\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n      -wildcardPermissionsIndex: ? array\n    }\n    \"profile_image_url\" => null\n    \"is_admin\" => null\n    \"role_id\" => null\n    \"password\" => null\n    \"password_confirmation\" => null\n    \"roles\" => Illuminate\\Support\\Collection {#2027\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n    \"sendWelcomeMail\" => false\n    \"isVerified\" => false\n    \"selectedPermissions\" => []\n    \"rolePermissions\" => []\n    \"userAdditionalPermissions\" => []\n    \"roleAdditionalPermissions\" => []\n  ]\n  \"name\" => \"admin.user.user-creator\"\n  \"component\" => \"App\\Livewire\\Admin\\User\\UserCreator\"\n  \"id\" => \"49KmJxfQssfA4Nh9To4P\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fwhats%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.01s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-106605540 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-106605540\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1812700517 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"850 characters\">{&quot;data&quot;:{&quot;id&quot;:&quot;49KmJxfQssfA4Nh9To4P&quot;,&quot;user&quot;:[{&quot;firstname&quot;:null,&quot;lastname&quot;:null,&quot;email&quot;:null,&quot;phone&quot;:null,&quot;default_language&quot;:null,&quot;profile_image_url&quot;:null},{&quot;class&quot;:&quot;App\\\\Models\\\\User&quot;,&quot;connection&quot;:null,&quot;s&quot;:&quot;elmdl&quot;}],&quot;profile_image_url&quot;:null,&quot;is_admin&quot;:null,&quot;role_id&quot;:null,&quot;password&quot;:null,&quot;password_confirmation&quot;:null,&quot;roles&quot;:[[],{&quot;class&quot;:&quot;Illuminate\\\\Support\\\\Collection&quot;,&quot;s&quot;:&quot;clctn&quot;}],&quot;sendWelcomeMail&quot;:false,&quot;isVerified&quot;:false,&quot;selectedPermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;rolePermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;userAdditionalPermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;roleAdditionalPermissions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;49KmJxfQssfA4Nh9To4P&quot;,&quot;name&quot;:&quot;admin.user.user-creator&quot;,&quot;path&quot;:&quot;admin\\/users\\/user&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;0728092b95a81e401e40216b919f41a521e4f2083714dce44eb07594094d2506&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>selectedPermissions</span>\" => []\n        \"<span class=sf-dump-key>role_id</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      \"<span class=sf-dump-key>calls</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812700517\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-657520134 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1119</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/users/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IjlzeXppaFJDc0swWUU2Mll6ODdIMkE9PSIsInZhbHVlIjoiRk5RcEtPYmRLT2k0dG8xZU43VzdHKytrUXZqcTRsSVFxS3NSRTdPZFRiRmdLeU5tZ1JWSkRIZ3ZSYStIQVJ4LzI2M1VtN3BQODVyZEdyZ1U5UVdlNGE3NTNtdW9jUnNFVGptUjBZUGVCSllzckVGVmlQenluVGoyTnBpMXRCOHEiLCJtYWMiOiI5OWI3NTMyZGNkYTkxYzIyZWU5ZGE0OGZlYjk4YTZmYTljMDIxMDJlYmRkNGMwNzEzODRiM2Q2YzM0M2ZiY2E1IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IjVLaGhHZDNUOURkYkNFQkF2Ni9FU1E9PSIsInZhbHVlIjoiZ2JPeVFrV3ljUG80ZU9KMDFLYTR6UndIcVlJejJyb0Y2aTlRUXdXQ01CemZ3M21wcHhPWkxUUjhlOGFRQTQ4WXVlY2tvS3NzQWNPSzVjdDVSNkdqOWd2U0E1RFlaMnkxTDdkUU0rMEVqU3ZtdnpDY2xUWm1VcHZBY2tVQmdHYWYiLCJtYWMiOiJkMmNlZDA3NmZkZWQ5MzUxNGI0NWFkNmJhZjI5YjFkNzNjMGQyZTJlMzJmNjJjY2U4ODdiOTUyMjI5NGYyZmVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657520134\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-397084141 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vILqHUdI5hnXjELBclK5v3OaFcrLLWL0dbZ9sS8Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-397084141\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-570410846 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 12 Jul 2025 14:39:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570410846\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-756737352 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RIrLEgE2tWFIs8GEenlNf2TZPADfmgKQcBsCgsZf</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://127.0.0.1:8000/admin/users/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>admin_firstname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">reber</span>\"\n  \"<span class=sf-dump-key>admin_lastname</span>\" => \"<span class=sf-dump-str title=\"5 characters\">kurdi</span>\"\n  \"<span class=sf-dump-key>admin_email</span>\" => \"<span class=sf-dump-str title=\"22 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>admin_password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">123456789</span>\"\n  \"<span class=sf-dump-key>admin_timezone</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Pacific/Tahiti</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756737352\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}